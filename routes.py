from flask import render_template, request, jsonify, redirect, url_for, flash, send_from_directory, session, send_file
from flask_login import login_user, logout_user, login_required, current_user
from werkzeug.security import generate_password_hash, check_password_hash
from app import app, db
from models import User, AIModelConfig, TradingAccount, Trade, Strategy, BacktestResult
from services.ai_service import AIService
from services.trading_service import TradingService
from services.backtest_service import BacktestService
from services.deep_learning_service import deep_learning_service
from pattern_recognition import <PERSON><PERSON><PERSON>ec<PERSON><PERSON><PERSON>, PatternSignal
from pattern_test_data import PatternTestDataGenerator
# 延迟导入这些模块，避免启动时的导入问题
# from services.multi_timeframe_pattern_service import MultiTimeframePatternService
# from services.data_converter import DataConverter
import json
import time
import random
import secrets
import pandas as pd
import numpy as np
from datetime import datetime, timedelta
from functools import wraps
import logging

# 设置logger
logger = logging.getLogger(__name__)

# 权限装饰器
def require_user_type(*allowed_types):
    """要求特定用户类型的装饰器"""
    def decorator(f):
        @wraps(f)
        def decorated_function(*args, **kwargs):
            if not current_user.is_authenticated:
                return redirect(url_for('login'))

            if current_user.user_type not in allowed_types:
                flash('您没有权限访问此功能', 'error')
                return redirect(url_for('dashboard'))

            return f(*args, **kwargs)
        return decorated_function
    return decorator

def require_vip_or_admin(f):
    """要求VIP或管理员权限的装饰器"""
    @wraps(f)
    def decorated_function(*args, **kwargs):
        if not current_user.is_authenticated:
            return redirect(url_for('login'))

        if current_user.user_type not in ['vip', 'admin']:
            flash('此功能仅限VIP用户和管理员使用', 'error')
            return redirect(url_for('dashboard'))

        return f(*args, **kwargs)
    return decorated_function

def require_admin(f):
    """要求管理员权限的装饰器"""
    @wraps(f)
    def decorated_function(*args, **kwargs):
        if not current_user.is_authenticated:
            return redirect(url_for('login'))

        if current_user.user_type != 'admin':
            flash('此功能仅限管理员使用', 'error')
            return redirect(url_for('dashboard'))

        return f(*args, **kwargs)
    return decorated_function

@app.route('/')
@app.route('/dashboard')
@login_required
def dashboard():
    """首页仪表盘 - 只显示真实交易账户信息，包含MT5实时数据"""
    from datetime import datetime, timedelta
    import pytz

    # 获取账户统计信息 - 只获取真实交易账户
    accounts = TradingAccount.query.filter_by(
        user_id=current_user.id,
        account_type='real',  # 只获取真实账户
        is_active=True
    ).all()

    # 计算总资产和净值，包含MT5实时数据
    total_balance = 0
    total_equity = 0

    for acc in accounts:
        # 如果是MT5账户，尝试获取实时数据
        if 'MT5' in acc.broker or 'MT5' in acc.account_name:
            try:
                from services.mt5_service import mt5_service
                # 尝试获取MT5实时账户信息
                mt5_info = mt5_service.get_account_info()
                if mt5_info and mt5_info.get('success'):
                    account_data = mt5_info.get('account_info', {})
                    # 使用MT5实时数据
                    total_balance += account_data.get('balance', acc.balance)
                    total_equity += account_data.get('equity', acc.equity)
                else:
                    # 回退到数据库数据
                    total_balance += acc.balance
                    total_equity += acc.equity
            except Exception as e:
                print(f"获取MT5实时数据失败: {e}")
                # 回退到数据库数据
                total_balance += acc.balance
                total_equity += acc.equity
        else:
            # 非MT5账户使用数据库数据
            total_balance += acc.balance
            total_equity += acc.equity

    # 获取当天的交易记录 - 只显示真实交易账户的当天交易
    china_tz = pytz.timezone('Asia/Shanghai')
    today_start = datetime.now(china_tz).replace(hour=0, minute=0, second=0, microsecond=0)
    today_end = today_start + timedelta(days=1)

    # 转换为UTC时间进行数据库查询
    today_start_utc = today_start.astimezone(pytz.UTC).replace(tzinfo=None)
    today_end_utc = today_end.astimezone(pytz.UTC).replace(tzinfo=None)

    recent_trades_query = db.session.query(Trade, TradingAccount).join(
        TradingAccount, Trade.account_id == TradingAccount.id
    ).filter(
        TradingAccount.user_id == current_user.id,
        TradingAccount.account_type == 'real',  # 只显示真实账户的交易
        Trade.open_time >= today_start_utc,     # 当天开始
        Trade.open_time < today_end_utc         # 当天结束
    ).order_by(Trade.open_time.desc()).all()

    # 处理交易数据，添加账户信息和时区转换
    recent_trades = []

    for trade, account in recent_trades_query:
        # 转换时间到中国标准时间
        if trade.open_time:
            # 假设数据库中的时间是UTC时间
            utc_time = trade.open_time.replace(tzinfo=pytz.UTC)
            china_time = utc_time.astimezone(china_tz)
        else:
            china_time = datetime.now(china_tz)

        # 检查交易状态是否需要更新
        current_status = trade.status
        if trade.mt5_ticket and current_status == 'open':
            # 检查MT5中的实际状态
            try:
                from services.mt5_service import mt5_service
                if mt5_service.connected:
                    import MetaTrader5 as mt5
                    positions = mt5.positions_get(ticket=trade.mt5_ticket)
                    if not positions:  # 如果MT5中没有找到持仓，说明已平仓
                        current_status = 'closed'
                        # 更新数据库中的状态
                        trade.status = 'closed'
                        if not trade.close_time:
                            trade.close_time = datetime.utcnow()
                        db.session.commit()
            except Exception as e:
                print(f"检查MT5持仓状态失败: {e}")

        trade_data = {
            'id': trade.id,
            'symbol': trade.symbol,
            'trade_type': trade.trade_type,
            'volume': trade.volume,
            'open_price': trade.open_price,
            'close_price': trade.close_price,
            'profit': trade.profit,
            'status': current_status,
            'open_time': trade.open_time,
            'china_time': china_time,  # 中国标准时间
            'close_time': trade.close_time,
            'strategy_name': trade.strategy_name,
            'mt5_ticket': trade.mt5_ticket,
            'account_name': account.account_name,  # 添加账户名称
            'account_type': account.account_type,  # 添加账户类型
            'broker': account.broker  # 添加经纪商信息
        }
        recent_trades.append(trade_data)

    # 获取活跃策略（使用原生SQL避免SQLAlchemy缓存问题）
    try:
        import sqlite3
        conn = sqlite3.connect('trading_system.db')
        cursor = conn.cursor()
        cursor.execute("SELECT COUNT(*) FROM strategy WHERE user_id = ? AND is_active = 1", (current_user.id,))
        active_strategies = cursor.fetchone()[0]
        conn.close()
    except Exception as e:
        print(f"获取活跃策略数量失败: {e}")
        active_strategies = 0

    # 计算今日盈亏
    today_pnl = 0.0
    today_trades_count = 0

    if accounts:
        try:
            # 获取中国时区的今日开始时间
            china_tz = pytz.timezone('Asia/Shanghai')
            now_china = datetime.now(china_tz)
            today_start = now_china.replace(hour=0, minute=0, second=0, microsecond=0)
            today_start_utc = today_start.astimezone(pytz.UTC).replace(tzinfo=None)

            account_ids = [acc.id for acc in accounts]

            # 查询今日的交易记录
            today_trades = Trade.query.filter(
                Trade.account_id.in_(account_ids),
                Trade.open_time >= today_start_utc
            ).all()

            today_trades_count = len(today_trades)

            # 计算今日盈亏
            for trade in today_trades:
                if trade.status == 'closed' and trade.profit is not None:
                    today_pnl += trade.profit
                elif trade.status == 'open':
                    # 对于持仓交易，尝试获取实时盈亏
                    try:
                        from services.mt5_service import mt5_service
                        if trade.mt5_ticket and mt5_service.connected:
                            import MetaTrader5 as mt5
                            positions = mt5.positions_get(ticket=trade.mt5_ticket)
                            if positions:
                                position = positions[0]
                                today_pnl += position.profit
                            elif trade.profit is not None:
                                today_pnl += trade.profit
                        elif trade.profit is not None:
                            today_pnl += trade.profit
                    except Exception as e:
                        print(f"获取持仓盈亏失败: {e}")
                        if trade.profit is not None:
                            today_pnl += trade.profit
        except Exception as e:
            print(f"计算今日盈亏失败: {e}")

    return render_template('dashboard.html',
                         accounts=accounts,
                         total_balance=total_balance,
                         total_equity=total_equity,
                         recent_trades=recent_trades,
                         active_strategies=active_strategies,
                         today_pnl=round(today_pnl, 2),
                         today_trades_count=today_trades_count)

@app.route('/login', methods=['GET', 'POST'])
def login():
    if request.method == 'POST':
        username = request.form['username']
        password = request.form['password']
        user = User.query.filter_by(username=username).first()

        if user and user.check_password(password):
            login_user(user)
            return redirect(url_for('dashboard'))
        else:
            flash('用户名或密码错误')

    return render_template('login.html')

@app.route('/logout')
@login_required
def logout():
    logout_user()
    return redirect(url_for('login'))

# 真实交易页面已删除

@app.route('/trading/demo')
@login_required
def demo_trading():
    """模拟交易页面"""
    accounts = TradingAccount.query.filter_by(
        user_id=current_user.id, account_type='demo', is_active=True
    ).all()
    return render_template('trading/demo_trading.html', accounts=accounts)

@app.route('/trading/query')
@login_required
def trading_query():
    """交易查询页面"""
    return render_template('trading/trading_query.html')

# 智能体交易-真实页面已删除

@app.route('/risk-events')
@login_required
def risk_events():
    """风险事件监控页面"""
    return render_template('risk_events.html')

# 分析回测页面已删除

@app.route('/analysis/ai-process')
@login_required
@require_vip_or_admin
def ai_analysis_process():
    """AI策略分析过程页面 - 仅VIP和管理员可访问"""
    return render_template('analysis/ai_process.html')

@app.route('/analysis/ai-training')
@login_required
@require_vip_or_admin
def ai_training():
    """训练AI策略页面 - 仅VIP和管理员可访问"""
    return render_template('analysis/ai_training.html')

# AI大模型分析页面已删除

@app.route('/user-management')
@login_required
@require_admin
def user_management():
    """用户管理页面 - 仅管理员可访问"""
    return render_template('user_management.html')

@app.route('/ai-strategy-management')
@login_required
@require_admin
def ai_strategy_management():
    """AI策略分享管理页面 - 仅管理员可访问"""
    # 获取所有已完成的AI策略（使用原生SQL避免缓存问题）
    try:
        import sqlite3
        conn = sqlite3.connect('trading_system.db')
        cursor = conn.cursor()
        cursor.execute("SELECT * FROM strategy WHERE status = 'completed'")
        strategy_rows = cursor.fetchall()
        conn.close()

        # 转换为策略对象格式
        strategies = []
        for row in strategy_rows:
            strategy_data = {
                'id': row[0],
                'name': row[2],
                'description': row[3],
                'strategy_type': row[4],
                'status': row[10] if len(row) > 10 else 'completed',
                'is_shared': row[7] if len(row) > 7 else False
            }
            strategies.append(strategy_data)
    except Exception as e:
        print(f"获取AI策略列表失败: {e}")
        strategies = []
    return render_template('ai_strategy_management.html', strategies=strategies)

@app.route('/settings')
@login_required
def settings():
    """系统设置页面"""
    ai_configs = AIModelConfig.query.all()
    return render_template('settings.html', ai_configs=ai_configs)

# API路由
@app.route('/api/ai-models', methods=['GET', 'POST'])
@login_required
def api_ai_models():
    if request.method == 'POST':
        data = request.json
        config = AIModelConfig(
            name=data['name'],
            api_key=data['api_key'],
            model_name=data['model_name'],
            base_url=data.get('base_url', ''),
            is_active=data.get('is_active', True)
        )
        db.session.add(config)
        db.session.commit()
        return jsonify({'success': True, 'message': 'AI模型配置已保存'})

    configs = AIModelConfig.query.all()
    return jsonify([{
        'id': c.id,
        'name': c.name,
        'model_name': c.model_name,
        'base_url': c.base_url,
        'is_active': c.is_active
    } for c in configs])

@app.route('/api/trading-accounts', methods=['GET', 'POST'])
@login_required
def api_trading_accounts():
    if request.method == 'POST':
        data = request.json
        account = TradingAccount(
            user_id=current_user.id,
            account_name=data['account_name'],
            account_type=data['account_type'],
            broker=data['broker'],
            api_key=data.get('api_key', ''),
            api_secret=data.get('api_secret', ''),
            balance=data.get('balance', 0.0)
        )
        db.session.add(account)
        db.session.commit()
        return jsonify({'success': True, 'message': '交易账户已添加'})

    accounts = TradingAccount.query.filter_by(user_id=current_user.id).all()
    return jsonify([{
        'id': a.id,
        'account_name': a.account_name,
        'account_type': a.account_type,
        'broker': a.broker,
        'balance': a.balance,
        'equity': a.equity,
        'is_active': a.is_active
    } for a in accounts])

@app.route('/api/strategies', methods=['GET', 'POST'])
@login_required
def api_strategies():
    if request.method == 'POST':
        data = request.json
        strategy = Strategy(
            user_id=current_user.id,
            name=data['name'],
            description=data.get('description', ''),
            strategy_type=data['strategy_type'],
            parameters=json.dumps(data.get('parameters', {}))
        )
        db.session.add(strategy)
        db.session.commit()
        return jsonify({'success': True, 'message': '策略已保存'})

    # 使用原生SQL获取策略（避免缓存问题）
    try:
        import sqlite3
        conn = sqlite3.connect('trading_system.db')
        cursor = conn.cursor()
        cursor.execute("SELECT * FROM strategy WHERE user_id = ?", (current_user.id,))
        strategy_rows = cursor.fetchall()
        conn.close()

        strategies = []
        for row in strategy_rows:
            strategies.append({
                'id': row[0],
                'name': row[2],
                'description': row[3],
                'strategy_type': row[4],
                'is_active': row[6] if len(row) > 6 else False,
                'created_at': row[17] if len(row) > 17 else ''
            })

        return jsonify(strategies)
    except Exception as e:
        print(f"获取策略列表失败: {e}")
        return jsonify([])

@app.route('/api/backtest', methods=['POST'])
@login_required
def api_backtest():
    data = request.json

    try:
        strategy_type = data.get('strategy_type', 'technical')

        if strategy_type == 'ai_strategy':
            # AI策略回测
            from services.ai_strategy_service import ai_strategy_service

            # 获取AI策略
            ai_strategy = ai_strategy_service.get_strategy_details(data['strategy_id'])
            if not ai_strategy:
                return jsonify({'success': False, 'error': 'AI策略不存在'})

            # 模拟AI策略回测结果
            result = {
                'metrics': {
                    'total_return': 0.235,  # 23.5%
                    'annual_return': 0.187,  # 18.7%
                    'max_drawdown': -0.082,  # -8.2%
                    'sharpe_ratio': 1.85,
                    'total_trades': 156,
                    'win_rate': 0.652,  # 65.2%
                    'final_value': data.get('initial_capital', 10000) * 1.235
                },
                'portfolio_data': [],
                'backtest_id': f"ai_{data['strategy_id']}_{data['symbol']}_{data['start_date']}"
            }

        else:
            # 传统策略回测
            backtest_service = BacktestService()
            result = backtest_service.run_backtest(
                strategy_id=data['strategy_id'],
                symbol=data['symbol'],
                start_date=data['start_date'],
                end_date=data['end_date'],
                timeframe=data.get('timeframe', '15m'),  # 新增时间间隔参数
                initial_capital=data.get('initial_capital', 10000)
            )

        return jsonify({'success': True, 'result': result})
    except Exception as e:
        return jsonify({'success': False, 'error': str(e)})

@app.route('/api/ai-trading/analyze', methods=['POST'])
@login_required
def api_ai_trading_analyze():
    """AI交易分析API"""
    data = request.json

    try:
        strategy_id = data.get('strategy_id')
        risk_level = data.get('risk_level', 'moderate')
        # 使用手数区间替代固定交易金额
        min_lot_size = data.get('min_lot_size', 0.01)
        max_lot_size = data.get('max_lot_size', 0.05)
        trading_symbol = data.get('trading_symbol', 'EURUSD')  # 获取用户选择的货币对
        account_type = data.get('account_type', 'demo')
        position_management_only = data.get('position_management_only', False)  # 是否仅进行持仓管理

        print(f"AI交易分析请求: 策略ID={strategy_id}, 风险等级={risk_level}, 手数区间={min_lot_size}-{max_lot_size}, 货币对={trading_symbol}, 账户类型={account_type}, 持仓管理模式={position_management_only}")

        if not strategy_id:
            return jsonify({'success': False, 'error': '策略ID不能为空'})

        # 如果是持仓管理模式，只返回持有建议
        if position_management_only:
            print("🔄 持仓管理模式：不执行新交易，仅管理现有持仓")
            return jsonify({
                'success': True,
                'recommendation': {
                    'action': 'hold',
                    'symbol': trading_symbol,
                    'amount': 0,
                    'confidence': 1.0,
                    'reason': '持仓管理模式：已达到交易次数限制，停止新增订单，继续管理现有持仓'
                }
            })

        # 获取AI策略详情
        from services.ai_strategy_service import ai_strategy_service
        ai_strategy = ai_strategy_service.get_strategy_details(strategy_id)

        if not ai_strategy:
            return jsonify({'success': False, 'error': 'AI策略不存在'})

        # 模拟AI策略分析结果
        import random

        # 根据风险等级调整交易概率和金额
        risk_multipliers = {
            'conservative': {'trade_prob': 0.3, 'amount_factor': 0.5},
            'moderate': {'trade_prob': 0.5, 'amount_factor': 0.7},
            'aggressive': {'trade_prob': 0.7, 'amount_factor': 1.0}
        }

        risk_config = risk_multipliers.get(risk_level, risk_multipliers['moderate'])

        # 随机决定是否交易
        should_trade = random.random() < risk_config['trade_prob']

        if should_trade:
            # 使用用户选择的交易品种，而不是随机选择
            symbol = trading_symbol

            print(f"AI决策: 使用用户选择的货币对 {symbol}")

            # 随机选择交易方向
            action = random.choice(['buy', 'sell'])

            # 根据手数区间计算交易量
            # 在最小和最大手数之间随机选择
            amount = round(min_lot_size + random.random() * (max_lot_size - min_lot_size), 2)

            # 确保手数在有效范围内
            amount = max(0.01, min(amount, 0.05))

            # 根据风险等级调整
            amount = amount * risk_config['amount_factor']

            # 根据交易品种调整交易数量
            if 'XAU' in symbol or 'GOLD' in symbol.upper():
                # 黄金交易：使用较小的手数，但不低于最小手数
                amount = max(min_lot_size, round(amount * 0.8, 2))
            elif 'BTC' in symbol or 'ETH' in symbol:
                # 加密货币：使用更小的手数
                amount = round(min(base_amount * 0.001, 0.01) * (0.5 + random.random() * 0.5), 3)
            else:
                # 外汇对：使用标准计算
                amount = round(min(base_amount * 0.1, 1.0) * (0.5 + random.random() * 0.5), 2)

            # 确保最小交易量
            amount = max(amount, 0.01)

            recommendation = {
                'action': action,
                'symbol': symbol,
                'amount': amount,
                'confidence': round(0.6 + random.random() * 0.3, 2),  # 60%-90%的置信度
                'strategy_id': strategy_id,
                'reason': f'基于{ai_strategy.get("name", "AI策略")}的分析，{symbol}显示{action}信号'
            }
        else:
            recommendation = {
                'action': 'hold',
                'symbol': trading_symbol,  # 即使持有也返回用户选择的货币对
                'amount': 0,
                'confidence': 0.5,
                'strategy_id': strategy_id,
                'reason': f'当前{trading_symbol}市场条件不适合交易，建议持有'
            }

        return jsonify({
            'success': True,
            'recommendation': recommendation,
            'analysis_time': datetime.now().isoformat()
        })

    except Exception as e:
        return jsonify({'success': False, 'error': str(e)})

@app.route('/api/demo-trading/order', methods=['POST'])
@login_required
def api_demo_trading_order():
    """模拟交易订单API - 智能判断是否使用MT5"""
    data = request.json

    try:
        symbol = data.get('symbol', 'EURUSD')
        side = data.get('side', 'buy')
        amount = float(data.get('amount', 0.01))
        account_id = data.get('account_id')  # 获取选择的账户ID

        print(f"收到模拟交易订单: {symbol} {side} {amount}, 账户ID: {account_id}")

        # 判断是否为MT5账户
        is_mt5_account = account_id and account_id.startswith('mt5_')

        if is_mt5_account:
            # MT5账户：通过MT5 API执行
            print("🔗 使用MT5客户端账户，发送订单到MT5...")
            return execute_mt5_order(data, symbol, side, amount)
        else:
            # 其他模拟账户：仅在本地数据库记录
            print("💾 使用内部模拟账户，仅在本地记录...")
            return execute_local_order(data, symbol, side, amount, account_id)

    except Exception as e:
        print(f"❌ 模拟交易异常: {e}")
        return jsonify({'success': False, 'error': str(e)})

def execute_mt5_order(data, symbol, side, amount):
    """执行MT5订单"""
    try:
        from services.mt5_service import mt5_service
        import MetaTrader5 as mt5

        # 转换交易类型
        if side.lower() == 'buy':
            order_type = mt5.ORDER_TYPE_BUY
        else:
            order_type = mt5.ORDER_TYPE_SELL

        # 通过MT5执行模拟交易
        result = mt5_service.place_demo_order(
            symbol=symbol,
            order_type=order_type,
            volume=amount,
            comment=f"Web模拟交易 {side}"
        )

        if result and result.retcode == mt5.TRADE_RETCODE_DONE:
            print(f"✅ MT5模拟交易成功: 订单号 {result.order}")

            # 获取或创建MT5账户记录
            account = TradingAccount.query.filter_by(
                user_id=current_user.id,
                account_name__like='%MT5%',
                account_type='demo',
                is_active=True
            ).first()

            if not account:
                # 创建MT5账户记录
                account = TradingAccount(
                    user_id=current_user.id,
                    account_name='MT5模拟账户',
                    account_type='demo',
                    broker='MT5',
                    balance=100000.0,
                    is_active=True
                )
                db.session.add(account)
                db.session.commit()

            # 创建交易记录
            trade = Trade(
                account_id=account.id,
                symbol=symbol,
                trade_type=side,
                volume=amount,
                open_price=result.price,
                status='open',
                strategy_name='AI自动交易' if data.get('ai_generated') else 'Web模拟交易',
                mt5_ticket=result.order  # 保存MT5订单号
            )

            # 如果设置了止损和止盈
            if data.get('stop_loss'):
                trade.stop_loss = float(data.get('stop_loss'))
            if data.get('take_profit'):
                trade.take_profit = float(data.get('take_profit'))

            db.session.add(trade)
            db.session.commit()

            return jsonify({
                'success': True,
                'order': {
                    'order_id': f"mt5_{result.order}",
                    'trade_id': trade.id,
                    'mt5_ticket': result.order,
                    'symbol': symbol,
                    'side': side,
                    'amount': amount,
                    'price': result.price,
                    'status': 'filled',
                    'timestamp': trade.open_time.isoformat(),
                    'ai_generated': data.get('ai_generated', False),
                    'account_type': 'mt5'
                },
                'message': f'✅ 订单已发送到MT5客户端，订单号: {result.order}'
            })
        else:
            error_msg = f"MT5下单失败: {result.comment if result else 'MT5连接失败'}"
            print(f"❌ MT5交易失败: {error_msg}")

            return jsonify({
                'success': False,
                'error': error_msg
            })

    except Exception as e:
        print(f"❌ MT5交易异常: {e}")
        return jsonify({'success': False, 'error': str(e)})

def execute_local_order(data, symbol, side, amount, account_id):
    """执行本地模拟订单"""
    try:
        # 获取或创建内部模拟账户
        if account_id and account_id.startswith('internal_'):
            account_name = account_id.replace('internal_', '内部-')
        else:
            account_name = '默认模拟账户'

        account = TradingAccount.query.filter_by(
            user_id=current_user.id,
            account_name=account_name,
            account_type='demo',
            is_active=True
        ).first()

        if not account:
            # 创建内部模拟账户
            account = TradingAccount(
                user_id=current_user.id,
                account_name=account_name,
                account_type='demo',
                broker='内部模拟',
                balance=100000.0,
                is_active=True
            )
            db.session.add(account)
            db.session.commit()

        # TODO: 集成真实市场数据API
        # 暂时返回错误，提示需要真实数据源
        return jsonify({
            'success': False,
            'error': '需要集成真实市场数据源才能执行交易'
        }), 400

        # 创建交易记录
        trade = Trade(
            account_id=account.id,
            symbol=symbol,
            trade_type=side,
            volume=amount,
            open_price=round(current_price, 5),
            status='open',
            strategy_name='AI自动交易' if data.get('ai_generated') else '内部模拟交易'
            # 注意：不设置mt5_ticket，表示这是内部模拟交易
        )

        # 如果设置了止损和止盈
        if data.get('stop_loss'):
            trade.stop_loss = float(data.get('stop_loss'))
        if data.get('take_profit'):
            trade.take_profit = float(data.get('take_profit'))

        db.session.add(trade)
        db.session.commit()

        print(f"✅ 内部模拟交易成功: 交易ID {trade.id}")

        # 返回订单结果
        return jsonify({
            'success': True,
            'order': {
                'order_id': f"local_{trade.id}",
                'trade_id': trade.id,
                'symbol': symbol,
                'side': side,
                'amount': amount,
                'price': trade.open_price,
                'status': 'filled',
                'timestamp': trade.open_time.isoformat(),
                'ai_generated': data.get('ai_generated', False),
                'account_type': 'internal'
            },
            'message': f'💾 订单已在内部模拟账户记录，交易ID: {trade.id}'
        })

    except Exception as e:
        print(f"❌ 内部模拟交易异常: {e}")
        return jsonify({'success': False, 'error': str(e)})

@app.route('/api/demo-trading/history', methods=['GET'])
@login_required
def api_demo_trading_history():
    """获取模拟交易历史"""
    try:
        # 获取用户的模拟账户
        accounts = TradingAccount.query.filter_by(
            user_id=current_user.id,
            account_type='demo',
            is_active=True
        ).all()

        if not accounts:
            return jsonify({'success': True, 'trades': []})

        # 获取所有模拟账户的交易记录
        account_ids = [acc.id for acc in accounts]
        trades = Trade.query.filter(
            Trade.account_id.in_(account_ids)
        ).order_by(Trade.open_time.desc()).limit(100).all()

        # 格式化交易记录
        trade_history = []
        for trade in trades:
            trade_data = {
                'trade_id': trade.id,
                'symbol': trade.symbol,
                'side': trade.trade_type,
                'volume': trade.volume,
                'open_price': trade.open_price,
                'close_price': trade.close_price,
                'profit': trade.profit or 0.0,
                'status': trade.status,
                'open_time': trade.open_time.isoformat(),
                'close_time': trade.close_time.isoformat() if trade.close_time else None,
                'strategy_name': trade.strategy_name or '手动交易'
            }
            trade_history.append(trade_data)

        return jsonify({'success': True, 'trades': trade_history})

    except Exception as e:
        return jsonify({'success': False, 'error': str(e)})

@app.route('/api/demo-trading/close-position', methods=['POST'])
@login_required
def api_close_position():
    """平仓API - 智能判断账户类型"""
    data = request.json
    trade_id = data.get('trade_id')
    account_type = data.get('account_type', 'internal')  # 获取账户类型

    if not trade_id:
        return jsonify({'success': False, 'error': '缺少交易ID'})

    try:
        print(f"收到平仓请求: {trade_id}, 账户类型: {account_type}")

        if account_type == 'mt5':
            # MT5账户：通过MT5平仓
            print("🔗 通过MT5客户端平仓...")
            return close_mt5_position(trade_id)
        else:
            # 其他账户：本地平仓
            print("💾 通过内部系统平仓...")
            return close_local_position(trade_id)

    except Exception as e:
        print(f"❌ 平仓异常: {e}")
        return jsonify({'success': False, 'error': str(e)})

def close_mt5_position(trade_id):
    """MT5平仓"""
    try:
        from services.mt5_service import mt5_service
        import MetaTrader5 as mt5

        # 通过MT5平仓
        result = mt5_service.close_demo_position(trade_id)

        if result and result.retcode == mt5.TRADE_RETCODE_DONE:
            print(f"✅ MT5平仓成功: {trade_id}")

            # 更新数据库中的交易记录
            trade = Trade.query.filter_by(mt5_ticket=trade_id).first()
            if trade:
                trade.status = 'closed'
                trade.close_price = result.price
                trade.close_time = datetime.utcnow()
                db.session.commit()

            return jsonify({
                'success': True,
                'message': f'✅ MT5持仓 {trade_id} 已成功平仓',
                'close_price': result.price
            })
        else:
            error_msg = f"MT5平仓失败: {result.comment if result else 'MT5连接失败'}"
            print(f"❌ MT5平仓失败: {error_msg}")

            return jsonify({
                'success': False,
                'error': error_msg
            })

    except Exception as e:
        print(f"❌ MT5平仓异常: {e}")
        return jsonify({'success': False, 'error': str(e)})

def close_local_position(trade_id):
    """本地平仓"""
    try:
        from services.position_manager import position_manager

        # 通过本地系统平仓
        result = position_manager.close_position(trade_id, 'manual')

        if result.get('success'):
            print(f"✅ 内部平仓成功: {trade_id}")
            return jsonify({
                'success': True,
                'message': f'💾 内部持仓 {trade_id} 已成功平仓'
            })
        else:
            print(f"❌ 内部平仓失败: {result.get('error')}")
            return jsonify(result)

    except Exception as e:
        print(f"❌ 内部平仓异常: {e}")
        return jsonify({'success': False, 'error': str(e)})

@app.route('/api/demo-trading/positions', methods=['GET'])
@login_required
def api_get_positions():
    """获取当前持仓API - 智能判断账户类型"""
    try:
        account_id = request.args.get('account_id')
        print(f"获取持仓请求，账户ID: {account_id}")

        # 判断是否为MT5账户
        is_mt5_account = account_id and account_id.startswith('mt5_')

        if is_mt5_account:
            # MT5账户：从MT5获取真实持仓
            print("🔗 从MT5客户端获取持仓...")
            return get_mt5_positions()
        else:
            # 其他账户：从数据库获取本地持仓
            print("💾 从数据库获取内部模拟持仓...")
            return get_local_positions(account_id)

    except Exception as e:
        print(f"获取持仓异常: {e}")
        return jsonify({'success': False, 'error': str(e)})

def get_mt5_positions():
    """获取MT5持仓"""
    try:
        from services.mt5_service import mt5_service

        # 获取MT5模拟交易持仓
        mt5_positions = mt5_service.get_demo_positions()

        if mt5_positions is None:
            return jsonify({'success': False, 'error': 'MT5连接失败'})

        # 转换为前端需要的格式
        positions = []
        for pos in mt5_positions:
            positions.append({
                'trade_id': pos['ticket'],
                'symbol': pos['symbol'],
                'trade_type': 'buy' if pos['type'] == 0 else 'sell',
                'volume': pos['volume'],
                'open_price': pos['open_price'],
                'current_price': pos['current_price'],
                'current_profit': pos['profit'],
                'swap': pos['swap'],
                'comment': pos['comment'],
                'time': pos['time'],
                'account_type': 'mt5'
            })

        return jsonify({
            'success': True,
            'positions': positions,
            'message': f'从MT5获取到 {len(positions)} 个模拟持仓'
        })

    except Exception as e:
        print(f"获取MT5持仓异常: {e}")
        return jsonify({'success': False, 'error': str(e)})

def get_local_positions(account_id):
    """获取本地模拟持仓"""
    try:
        from services.position_manager import position_manager

        # 获取用户的内部模拟账户
        if account_id and account_id.startswith('internal_'):
            account_name = account_id.replace('internal_', '内部-')
        else:
            account_name = '默认模拟账户'

        account = TradingAccount.query.filter_by(
            user_id=current_user.id,
            account_name=account_name,
            account_type='demo',
            is_active=True
        ).first()

        if not account:
            return jsonify({'success': True, 'positions': []})

        # 获取持仓
        result = position_manager.get_position_summary(account.id)

        # 添加账户类型标识
        if result.get('success') and result.get('positions'):
            for pos in result['positions']:
                pos['account_type'] = 'internal'

        return jsonify(result)

    except Exception as e:
        print(f"获取本地持仓异常: {e}")
        return jsonify({'success': False, 'error': str(e)})

@app.route('/api/demo-trading/update-positions', methods=['POST'])
@login_required
def api_update_positions():
    """更新持仓盈亏API"""
    from services.position_manager import position_manager

    try:
        # 获取用户的模拟账户
        accounts = TradingAccount.query.filter_by(
            user_id=current_user.id,
            account_type='demo',
            is_active=True
        ).all()

        if not accounts:
            return jsonify({'success': True, 'message': '无账户需要更新'})

        # 更新第一个账户的持仓
        account = accounts[0]
        result = position_manager.update_open_positions(account.id)
        return jsonify(result)

    except Exception as e:
        return jsonify({'success': False, 'error': str(e)})

@app.route('/api/place-order', methods=['POST'])
@login_required
def api_place_order():
    """真实交易下单API"""
    data = request.json

    try:
        account_id = data.get('account_id')
        account_type = data.get('account_type', 'api')
        symbol = data.get('symbol')
        order_type = data.get('order_type', 'market')
        side = data.get('side')
        amount = data.get('amount')
        price = data.get('price')
        stop_loss = data.get('stop_loss')
        take_profit = data.get('take_profit')
        trailing_stop = data.get('trailing_stop', False)

        print(f"收到真实交易订单: {symbol} {side} {amount}, 账户ID: {account_id}, 账户类型: {account_type}")

        # 判断是否为MT5账户或真实交易
        is_mt5_account = (account_type == 'mt5') or (account_id and account_id.startswith('mt5_'))
        is_real_trading = (account_type == 'real')

        # AI生成的真实交易标记
        ai_generated = data.get('ai_generated', False)

        if is_mt5_account or is_real_trading or ai_generated:
            # MT5账户、真实交易或AI生成的交易：通过MT5 API执行
            print(f"🔗 使用MT5执行真实交易订单... (MT5账户: {is_mt5_account}, 真实交易: {is_real_trading}, AI生成: {ai_generated})")
            return execute_mt5_real_order(data)
        else:
            # API账户：通过交易所API执行
            print("📡 使用API执行交易所订单...")
            return execute_api_order(data)

    except Exception as e:
        print(f"❌ 真实交易下单异常: {e}")
        return jsonify({'success': False, 'error': str(e)})

def clean_symbol_name(symbol):
    """清理货币对名称，移除可能的后缀"""
    try:
        if not symbol:
            return symbol

        # 移除常见的后缀
        suffixes_to_remove = ['=X', '.', '_', '-']

        cleaned_symbol = symbol.strip().upper()

        for suffix in suffixes_to_remove:
            if suffix in cleaned_symbol:
                cleaned_symbol = cleaned_symbol.split(suffix)[0]

        # 确保是有效的货币对格式（6个字符）
        if len(cleaned_symbol) == 6 and cleaned_symbol.isalpha():
            return cleaned_symbol

        # 如果不是标准格式，返回原始符号（可能是其他类型的交易品种）
        return symbol.strip()

    except Exception as e:
        print(f"❌ 清理货币对名称失败: {e}")
        return symbol

def execute_mt5_real_order(data):
    """执行MT5真实订单"""
    try:
        from services.mt5_service import mt5_service
        import MetaTrader5 as mt5

        symbol = data.get('symbol')
        side = data.get('side')
        amount = data.get('amount')
        stop_loss = data.get('stop_loss')
        take_profit = data.get('take_profit')
        ai_generated = data.get('ai_generated', False)
        strategy_id = data.get('strategy_id')

        # 清理货币对名称
        original_symbol = symbol
        symbol = clean_symbol_name(symbol)
        if original_symbol != symbol:
            print(f"🔧 货币对名称清理: {original_symbol} -> {symbol}")

        print(f"🔗 执行MT5真实交易: {symbol} {side} {amount}")
        print(f"📊 止损: {stop_loss}, 止盈: {take_profit}")
        print(f"🤖 AI生成: {ai_generated}, 策略ID: {strategy_id}")
        print(f"👤 用户ID: {current_user.id}")

        # 检查MT5连接状态
        if not mt5_service.connected:
            print("❌ MT5未连接，尝试连接...")
            if not mt5_service.connect():
                return jsonify({'success': False, 'error': 'MT5连接失败，请确保MT5客户端正在运行'})
            print("✅ MT5连接成功")

        # 检查账户余额
        account_info = mt5_service.get_account_info()
        if account_info:
            balance = account_info.get('balance', 0)
            equity = account_info.get('equity', 0)
            margin_free = account_info.get('margin_free', 0)
            print(f"💰 账户信息: 余额=${balance:.2f}, 净值=${equity:.2f}, 可用保证金=${margin_free:.2f}")

            # 估算所需保证金
            if 'XAU' in symbol:
                # 黄金的保证金计算 (假设1:100杠杆)
                estimated_margin = amount * (stop_loss or take_profit or 3300) * 100 / 100  # 1手=100盎司
            else:
                # 外汇的保证金计算 (假设1:100杠杆)
                estimated_margin = amount * 100000 / 100  # 1手=100,000基础货币

            print(f"📊 预估所需保证金: ${estimated_margin:.2f}")

            if estimated_margin > margin_free:
                print(f"⚠️ 警告: 所需保证金(${estimated_margin:.2f}) > 可用保证金(${margin_free:.2f})")
                return jsonify({
                    'success': False,
                    'error': f'资金不足：需要保证金${estimated_margin:.2f}，可用${margin_free:.2f}'
                })

        # 转换交易类型
        if side.lower() == 'buy':
            order_type = mt5.ORDER_TYPE_BUY
        else:
            order_type = mt5.ORDER_TYPE_SELL

        # 通过MT5执行真实交易
        result = mt5_service.send_order(
            symbol=symbol,
            order_type=order_type,
            volume=amount,
            sl=stop_loss or 0.0,
            tp=take_profit or 0.0,
            comment=f"Web真实交易{'(AI)' if ai_generated else ''}"
        )

        if result and result.get('success'):
            print(f"✅ MT5真实交易成功: 订单号 {result.get('order_id')}")

            # 获取或创建MT5真实交易账户记录
            account = None

            # 首先尝试查找现有的MT5账户
            accounts = TradingAccount.query.filter_by(
                user_id=current_user.id,
                is_active=True
            ).filter(
                TradingAccount.account_name.like('%MT5%')
            ).all()

            if accounts:
                # 如果有多个MT5账户，选择第一个
                account = accounts[0]
                print(f"✅ 使用现有MT5账户: {account.account_name}")
            else:
                # 创建新的MT5真实交易账户记录
                account_info = mt5_service.get_account_info()
                if account_info:
                    account = TradingAccount(
                        user_id=current_user.id,
                        account_name=f'MT5真实账户-{account_info.get("login", "Unknown")}',
                        account_type='real',
                        broker='MT5',
                        balance=account_info.get('balance', 0.0),
                        is_active=True
                    )
                    db.session.add(account)
                    db.session.commit()
                    print(f"✅ 创建MT5真实交易账户记录: {account.account_name}")
                else:
                    # 如果无法获取账户信息，创建默认账户
                    account = TradingAccount(
                        user_id=current_user.id,
                        account_name='MT5真实账户-默认',
                        account_type='real',
                        broker='MT5',
                        balance=0.0,
                        is_active=True
                    )
                    db.session.add(account)
                    db.session.commit()
                    print(f"✅ 创建默认MT5真实交易账户记录: {account.account_name}")

            # 创建交易记录
            trade = Trade(
                account_id=account.id,
                symbol=symbol,
                trade_type=side,
                volume=amount,
                open_price=result.get('price', 0.0),
                status='open',
                strategy_name=f'AI策略-{strategy_id}' if ai_generated else 'Web真实交易',
                mt5_ticket=result.get('order_id'),  # 保存MT5订单号
                stop_loss=stop_loss,
                take_profit=take_profit
            )

            db.session.add(trade)
            db.session.commit()

            print(f"✅ 交易记录已保存: Trade ID {trade.id}, MT5 Ticket {result.get('order_id')}")

            return jsonify({
                'success': True,
                'order': {
                    'order_id': f"mt5_{result.get('order_id')}",
                    'symbol': symbol,
                    'side': side,
                    'amount': amount,
                    'price': result.get('price'),
                    'status': 'filled',
                    'account_type': 'mt5'
                },
                'message': f'✅ 订单已发送到MT5客户端，订单号: {result.get("order_id")}'
            })
        else:
            error_msg = result.get('error', 'MT5下单失败') if result else 'MT5连接失败'
            print(f"❌ MT5真实交易失败: {error_msg}")

            return jsonify({
                'success': False,
                'error': error_msg
            })

    except Exception as e:
        print(f"❌ MT5真实交易异常: {e}")
        return jsonify({'success': False, 'error': str(e)})

def execute_api_order(data):
    """执行API交易所订单"""
    try:
        # 这里应该调用实际的交易所API
        # 暂时返回模拟结果
        return jsonify({
            'success': True,
            'order_id': f"api_order_{int(time.time())}",
            'message': 'API订单提交成功（模拟）'
        })
    except Exception as e:
        return jsonify({'success': False, 'error': str(e)})

@app.route('/api/real-trading/order', methods=['POST'])
@login_required
def api_real_trading_order():
    """真实交易订单API"""
    data = request.json

    try:
        # 这里应该连接到真实的交易API (如MT5)
        # 目前返回模拟结果

        # 额外的风险检查
        if data.get('ai_generated'):
            max_amount = 10000  # AI交易的最大金额限制
            if data.get('amount', 0) > max_amount:
                return jsonify({'success': False, 'error': f'AI交易金额超过限制 (${max_amount})'})

        order_result = {
            'order_id': f"real_{int(time.time())}",
            'symbol': data.get('symbol'),
            'side': data.get('side'),
            'amount': data.get('amount'),
            'price': round(1.0000 + random.random() * 0.5000, 4),  # 模拟价格
            'status': 'filled',
            'timestamp': datetime.now().isoformat(),
            'ai_generated': data.get('ai_generated', False)
        }

        return jsonify({'success': True, 'order': order_result})

    except Exception as e:
        return jsonify({'success': False, 'error': str(e)})

@app.route('/api/market-price/<symbol>')
@login_required
def api_get_market_price(symbol):
    """获取市场价格API"""
    try:
        from services.mt5_service import mt5_service
        import MetaTrader5 as mt5

        print(f"🔍 获取 {symbol} 的市场价格...")

        # 确保MT5连接
        if not mt5_service.connected:
            if not mt5_service.connect():
                return jsonify({'success': False, 'error': 'MT5连接失败'})

        # 获取价格信息
        tick = mt5.symbol_info_tick(symbol)
        if tick:
            # 使用买卖价的中间价作为当前价格
            current_price = (tick.bid + tick.ask) / 2
            print(f"✅ {symbol} 当前价格: {current_price} (买价: {tick.bid}, 卖价: {tick.ask})")

            return jsonify({
                'success': True,
                'price': current_price,
                'bid': tick.bid,
                'ask': tick.ask,
                'symbol': symbol,
                'timestamp': tick.time
            })
        else:
            print(f"❌ 无法获取 {symbol} 的价格信息")
            return jsonify({'success': False, 'error': f'无法获取{symbol}价格信息'})

    except Exception as e:
        print(f"❌ 获取市场价格异常: {str(e)}")
        return jsonify({'success': False, 'error': str(e)})

@app.route('/api/ai-analysis', methods=['POST'])
@login_required
def api_ai_analysis():
    data = request.json
    ai_service = AIService()

    try:
        # 获取时间范围参数，默认使用1周数据
        period = data.get('period', '1wk')
        interval = data.get('interval', '1h')

        print(f"AI分析请求参数: symbol={data['symbol']}, period={period}, interval={interval}")

        result = ai_service.analyze_market(
            symbol=data['symbol'],
            model_id=data['model_id'],
            analysis_type=data.get('analysis_type', 'technical'),
            period=period,
            interval=interval
        )
        return jsonify({'success': True, 'analysis': result})
    except Exception as e:
        return jsonify({'success': False, 'error': str(e)})

# ==================== 智能体交易API ====================

@app.route('/api/agent-trading/strategies', methods=['GET'])
@login_required
def api_get_agent_strategies():
    """获取智能体交易策略列表"""
    try:
        from models import AgentTradingStrategy

        strategies = AgentTradingStrategy.query.filter_by(user_id=current_user.id).all()

        strategy_list = []
        for strategy in strategies:
            strategy_data = {
                'id': strategy.id,
                'name': strategy.name,
                'description': strategy.description,
                'risk_tolerance': strategy.risk_tolerance,
                'max_daily_trades': strategy.max_daily_trades,
                'max_position_size': strategy.max_position_size,
                'trading_symbols': strategy.get_trading_symbols(),
                'trading_hours': strategy.trading_hours,
                'is_active': strategy.is_active,
                'performance_metrics': strategy.get_performance_metrics(),
                'created_at': strategy.created_at.isoformat()
            }
            strategy_list.append(strategy_data)

        return jsonify({'success': True, 'strategies': strategy_list})

    except Exception as e:
        return jsonify({'success': False, 'error': str(e)})

@app.route('/api/agent-trading/strategies', methods=['POST'])
@login_required
def api_create_agent_strategy():
    """创建智能体交易策略"""
    try:
        from services.agent_trading_service import agent_trading_service

        data = request.json

        # 验证必要字段
        if not data.get('name'):
            return jsonify({'success': False, 'error': '策略名称不能为空'})

        strategy_data = {
            'name': data.get('name'),
            'description': data.get('description', ''),
            'user_strategy': data.get('user_strategy', ''),
            'risk_tolerance': data.get('risk_tolerance', 'moderate'),
            'max_daily_trades': data.get('max_daily_trades', 10),
            'max_position_size': data.get('max_position_size', 1000.0),
            'ai_strategy_id': data.get('ai_strategy_id'),
            'trading_symbols': data.get('trading_symbols', ['EURUSD']),
            'trading_hours': data.get('trading_hours', '9:00-18:00'),
            'ai_model_config': data.get('ai_model_config', {})
        }

        strategy = agent_trading_service.create_agent_strategy(current_user.id, strategy_data)

        return jsonify({
            'success': True,
            'strategy_id': strategy.id,
            'message': '智能体交易策略创建成功'
        })

    except Exception as e:
        return jsonify({'success': False, 'error': str(e)})

@app.route('/api/agent-trading/start', methods=['POST'])
@login_required
def api_start_agent_trading():
    """启动智能体交易"""
    try:
        from services.agent_trading_service import agent_trading_service

        data = request.json
        strategy_id = data.get('strategy_id')

        if not strategy_id:
            return jsonify({'success': False, 'error': '策略ID不能为空'})

        # 验证策略所有权
        from models import AgentTradingStrategy
        strategy = AgentTradingStrategy.query.filter_by(
            id=strategy_id,
            user_id=current_user.id
        ).first()

        if not strategy:
            return jsonify({'success': False, 'error': '策略不存在或无权限'})

        session = agent_trading_service.start_agent_trading(strategy_id)

        return jsonify({
            'success': True,
            'session_id': session.id,
            'message': '智能体交易已启动'
        })

    except Exception as e:
        return jsonify({'success': False, 'error': str(e)})

@app.route('/api/agent-trading/stop', methods=['POST'])
@login_required
def api_stop_agent_trading():
    """停止智能体交易"""
    try:
        from services.agent_trading_service import agent_trading_service

        data = request.json
        session_id = data.get('session_id')

        if not session_id:
            return jsonify({'success': False, 'error': '会话ID不能为空'})

        # 验证会话所有权
        from models import AgentTradingSession
        session = AgentTradingSession.query.filter_by(
            id=session_id,
            user_id=current_user.id
        ).first()

        if not session:
            return jsonify({'success': False, 'error': '会话不存在或无权限'})

        success = agent_trading_service.stop_agent_trading(session_id)

        if success:
            return jsonify({'success': True, 'message': '智能体交易已停止'})
        else:
            return jsonify({'success': False, 'error': '停止失败'})

    except Exception as e:
        return jsonify({'success': False, 'error': str(e)})

@app.route('/api/agent-trading/execute', methods=['POST'])
@login_required
def api_execute_agent_decision():
    """执行智能体交易决策"""
    try:
        from services.agent_trading_service import agent_trading_service

        data = request.json
        session_id = data.get('session_id')

        if not session_id:
            return jsonify({'success': False, 'error': '会话ID不能为空'})

        # 验证会话所有权
        from models import AgentTradingSession
        session = AgentTradingSession.query.filter_by(
            id=session_id,
            user_id=current_user.id
        ).first()

        if not session:
            return jsonify({'success': False, 'error': '会话不存在或无权限'})

        result = agent_trading_service.execute_ai_decision(session_id)

        return jsonify(result)

    except Exception as e:
        return jsonify({'success': False, 'error': str(e)})

@app.route('/api/agent-trading/evaluate', methods=['POST'])
@login_required
def api_evaluate_agent_performance():
    """评估智能体交易表现"""
    try:
        from services.agent_trading_service import agent_trading_service

        data = request.json
        session_id = data.get('session_id')

        if not session_id:
            return jsonify({'success': False, 'error': '会话ID不能为空'})

        # 验证会话所有权
        from models import AgentTradingSession
        session = AgentTradingSession.query.filter_by(
            id=session_id,
            user_id=current_user.id
        ).first()

        if not session:
            return jsonify({'success': False, 'error': '会话不存在或无权限'})

        result = agent_trading_service.evaluate_performance(session_id)

        return jsonify(result)

    except Exception as e:
        return jsonify({'success': False, 'error': str(e)})

@app.route('/api/agent-trading/sessions', methods=['GET'])
@login_required
def api_get_agent_sessions():
    """获取智能体交易会话列表"""
    try:
        from models import AgentTradingSession, AgentTradingStrategy

        sessions = db.session.query(AgentTradingSession, AgentTradingStrategy).join(
            AgentTradingStrategy,
            AgentTradingSession.strategy_id == AgentTradingStrategy.id
        ).filter(
            AgentTradingSession.user_id == current_user.id
        ).order_by(AgentTradingSession.created_at.desc()).all()

        session_list = []
        for session, strategy in sessions:
            session_data = {
                'id': session.id,
                'session_name': session.session_name,
                'strategy_name': strategy.name,
                'start_time': session.start_time.isoformat(),
                'end_time': session.end_time.isoformat() if session.end_time else None,
                'status': session.status,
                'total_trades': session.total_trades,
                'winning_trades': session.winning_trades,
                'losing_trades': session.losing_trades,
                'total_profit': session.total_profit,
                'max_drawdown': session.max_drawdown,
                'last_evaluation': getattr(session, 'last_evaluation', None).isoformat() if getattr(session, 'last_evaluation', None) else None
            }
            session_list.append(session_data)

        return jsonify({'success': True, 'sessions': session_list})

    except Exception as e:
        return jsonify({'success': False, 'error': str(e)})

@app.route('/api/ai-models/<int:model_id>/test', methods=['POST'])
@login_required
def api_test_ai_model(model_id):
    try:
        ai_config = AIModelConfig.query.get(model_id)
        if not ai_config:
            return jsonify({'success': False, 'error': '模型不存在'})

        ai_service = AIService()
        # 简单测试调用
        result = ai_service.call_ai_model(
            ai_config.name.lower().replace('-', '_'),
            "Hello, this is a test message.",
            "You are a helpful assistant."
        )

        if "失败" in result or "错误" in result:
            return jsonify({'success': False, 'error': result})
        else:
            return jsonify({'success': True, 'message': '模型连接正常'})
    except Exception as e:
        return jsonify({'success': False, 'error': str(e)})

@app.route('/api/ai-models/<int:model_id>/toggle', methods=['POST'])
@login_required
def api_toggle_ai_model(model_id):
    try:
        ai_config = AIModelConfig.query.get(model_id)
        if not ai_config:
            return jsonify({'success': False, 'error': '模型不存在'})

        ai_config.is_active = not ai_config.is_active
        db.session.commit()

        return jsonify({'success': True, 'message': '模型状态已更新'})
    except Exception as e:
        return jsonify({'success': False, 'error': str(e)})

@app.route('/api/system-settings', methods=['POST'])
@login_required
def api_system_settings():
    try:
        data = request.json
        # 这里可以保存系统设置到数据库
        # 暂时只返回成功消息
        return jsonify({'success': True, 'message': '系统设置已保存'})
    except Exception as e:
        return jsonify({'success': False, 'error': str(e)})

@app.route('/api/system-health')
@login_required
def api_system_health():
    try:
        # 简单的健康检查
        try:
            import psutil
            memory_usage = psutil.virtual_memory().percent
            cpu_usage = psutil.cpu_percent(interval=1)
        except ImportError:
            # 如果psutil未安装，使用默认值
            memory_usage = 50.0
            cpu_usage = 20.0

        health_data = {
            'healthy': True,
            'database': True,  # 简化检查
            'memory_usage': memory_usage,
            'cpu_usage': cpu_usage
        }

        return jsonify(health_data)
    except Exception as e:
        return jsonify({
            'healthy': False,
            'database': False,
            'memory_usage': 0,
            'cpu_usage': 0,
            'error': str(e)
        })

@app.route('/api/clear-cache', methods=['POST'])
@login_required
def api_clear_cache():
    try:
        # 这里可以实现缓存清理逻辑
        return jsonify({'success': True, 'message': '缓存清理成功'})
    except Exception as e:
        return jsonify({'success': False, 'error': str(e)})

@app.route('/api/export-data')
@login_required
def api_export_data():
    try:
        # 这里可以实现数据导出逻辑
        return jsonify({'success': True, 'message': '数据导出功能待实现'})
    except Exception as e:
        return jsonify({'success': False, 'error': str(e)})

@app.route('/api/ai-models/debug', methods=['GET'])
@login_required
def api_ai_models_debug():
    """调试AI模型配置"""
    try:
        from models import AIModelConfig

        # 获取所有AI模型配置
        all_models = AIModelConfig.query.all()

        models_info = []
        for model in all_models:
            models_info.append({
                'id': model.id,
                'name': model.name,
                'display_name': getattr(model, 'display_name', ''),
                'provider': getattr(model, 'provider', 'Unknown'),
                'is_active': model.is_active,
                'api_key_configured': bool(model.api_key),
                'created_at': model.created_at.isoformat() if hasattr(model, 'created_at') and model.created_at else None
            })

        return jsonify({
            'success': True,
            'total_models': len(all_models),
            'models': models_info,
            'system_defaults': [
                'deepseek_v3', 'openai_gpt4', 'claude_3', 'qwen_max', 'gemini_pro'
            ]
        })

    except Exception as e:
        return jsonify({
            'success': False,
            'error': str(e)
        })

@app.route('/api/test-ai-call', methods=['POST'])
@login_required
def api_test_ai_call():
    """测试AI模型调用"""
    try:
        from services.ai_service import AIService

        data = request.json or {}
        model_name = data.get('model_name')
        prompt = data.get('prompt')

        if not model_name:
            return jsonify({'success': False, 'error': '缺少模型名称'})

        if not prompt:
            return jsonify({'success': False, 'error': '缺少提示内容'})

        print(f"🧪 测试AI调用 - 模型: {model_name}")
        print(f"📝 提示: {prompt}")

        ai_service = AIService()

        # 调用AI模型
        response = ai_service.call_ai_model(
            model_name=model_name,
            prompt=prompt,
            system_prompt="你是一个AI助手，请简洁地回答用户的问题。"
        )

        print(f"✅ AI调用成功，响应长度: {len(response)}")

        return jsonify({
            'success': True,
            'response': response,
            'model_name': model_name,
            'prompt_length': len(prompt),
            'response_length': len(response)
        })

    except Exception as e:
        print(f"❌ AI调用测试失败: {e}")
        return jsonify({
            'success': False,
            'error': str(e)
        })

@app.route('/api/test-user-model', methods=['POST'])
@login_required
def api_test_user_model():
    """测试用户配置的AI模型调用"""
    try:
        from services.ai_service import AIService
        from models import AIModelConfig

        data = request.json or {}
        model_name = data.get('model_name')

        if not model_name:
            return jsonify({'success': False, 'error': '缺少模型名称'})

        print(f"🧪 测试用户模型调用 - 模型: {model_name}")

        # 1. 检查数据库中的模型配置
        model = AIModelConfig.query.filter_by(name=model_name).first()
        if not model:
            return jsonify({
                'success': False,
                'error': f'数据库中未找到模型: {model_name}'
            })

        model_info = {
            'id': model.id,
            'name': model.name,
            'model_name': model.model_name,
            'base_url': model.base_url,
            'is_active': model.is_active,
            'api_key_configured': bool(model.api_key),
            'api_key_length': len(model.api_key) if model.api_key else 0
        }

        print(f"📊 数据库模型信息: {model_info}")

        # 2. 测试AI服务配置获取
        ai_service = AIService()
        config = ai_service._get_model_config_from_db(model_name)

        if not config:
            return jsonify({
                'success': False,
                'error': 'AI服务无法获取模型配置',
                'model_info': model_info
            })

        print(f"🔧 AI服务配置: {config}")

        # 3. 测试实际AI调用
        test_prompt = "请简单回复'测试成功'"

        try:
            response = ai_service.call_ai_model(
                model_name=model_name,
                prompt=test_prompt,
                system_prompt="你是一个AI助手，请简洁回复。"
            )

            print(f"✅ AI调用成功，响应: {response[:100]}...")

            return jsonify({
                'success': True,
                'model_info': model_info,
                'ai_config': config,
                'test_response': response,
                'message': 'AI模型调用测试成功'
            })

        except Exception as ai_error:
            print(f"❌ AI调用失败: {ai_error}")

            return jsonify({
                'success': False,
                'error': f'AI调用失败: {str(ai_error)}',
                'model_info': model_info,
                'ai_config': config
            })

    except Exception as e:
        print(f"❌ 测试用户模型异常: {e}")
        return jsonify({
            'success': False,
            'error': str(e)
        })

@app.route('/api/debug-agent-config', methods=['POST'])
@login_required
def api_debug_agent_config():
    """调试智能体配置验证"""
    try:
        from services.agent_trading_real_service import agent_trading_real_service
        from models import AIModelConfig, Strategy

        data = request.json or {}

        print(f"🔍 调试智能体配置: {data}")

        result = {
            'success': True,
            'config': data,
            'validation_details': {}
        }

        # 检查AI模型
        ai_model_id = data.get('ai_model')
        if ai_model_id:
            model_info = {'found': False, 'details': None, 'search_methods': []}

            # 方法1: 按ID查找
            if str(ai_model_id).isdigit():
                try:
                    model = AIModelConfig.query.get(int(ai_model_id))
                    if model:
                        model_info['found'] = True
                        model_info['details'] = {
                            'id': model.id,
                            'name': model.name,
                            'is_active': model.is_active,
                            'api_key_configured': bool(model.api_key)
                        }
                        model_info['search_methods'].append('按ID查找成功')
                except Exception as e:
                    model_info['search_methods'].append(f'按ID查找失败: {e}')

            # 方法2: 按name查找
            if not model_info['found']:
                try:
                    model = AIModelConfig.query.filter_by(name=ai_model_id).first()
                    if model:
                        model_info['found'] = True
                        model_info['details'] = {
                            'id': model.id,
                            'name': model.name,
                            'is_active': model.is_active,
                            'api_key_configured': bool(model.api_key)
                        }
                        model_info['search_methods'].append('按name查找成功')
                except Exception as e:
                    model_info['search_methods'].append(f'按name查找失败: {e}')

            result['validation_details']['ai_model'] = model_info

        # 检查AI策略
        ai_strategy_id = data.get('ai_strategy')
        if ai_strategy_id:
            strategy_info = {'found': False, 'details': None, 'search_methods': []}

            # 检查演示策略
            if str(ai_strategy_id).startswith('demo_'):
                strategy_info['found'] = True
                strategy_info['details'] = {'type': 'demo', 'id': ai_strategy_id}
                strategy_info['search_methods'].append('演示策略')
            else:
                # 检查真实策略（使用原生SQL）
                if str(ai_strategy_id).isdigit():
                    try:
                        import sqlite3
                        conn = sqlite3.connect('trading_system.db')
                        cursor = conn.cursor()
                        cursor.execute("""
                            SELECT id, name, status, ai_model, user_id
                            FROM strategy
                            WHERE id = ? AND strategy_type = ?
                        """, (int(ai_strategy_id), 'ai'))
                        row = cursor.fetchone()
                        conn.close()

                        if row:
                            strategy_info['found'] = True
                            strategy_info['details'] = {
                                'id': row[0],
                                'name': row[1],
                                'status': row[2],
                                'ai_model': row[3],
                                'user_id': row[4]
                            }
                            strategy_info['search_methods'].append('按ID查找成功')
                    except Exception as e:
                        strategy_info['search_methods'].append(f'按ID查找失败: {e}')

            result['validation_details']['ai_strategy'] = strategy_info

        # 执行实际验证
        try:
            validation_result = agent_trading_real_service._validate_ai_config(data)
            result['validation_result'] = validation_result
        except Exception as e:
            result['validation_result'] = False
            result['validation_error'] = str(e)

        return jsonify(result)

    except Exception as e:
        return jsonify({
            'success': False,
            'error': str(e)
        })

# ==================== 智能体交易-真实API ====================

@app.route('/api/agent-trading-real/start', methods=['POST'])
@login_required
def api_start_agent_trading_real():
    """启动智能体真实交易"""
    try:
        from services.agent_trading_real_service import agent_trading_real_service

        data = request.json or {}

        # 验证配置
        required_fields = ['ai_model', 'trading_symbols', 'user_requirements']
        for field in required_fields:
            if not data.get(field):
                return jsonify({'success': False, 'error': f'缺少必要参数: {field}'})

        # 验证AI策略（支持新旧格式）
        ai_strategies = data.get('ai_strategies', [])
        ai_strategy = data.get('ai_strategy')

        if not ai_strategies and not ai_strategy:
            return jsonify({'success': False, 'error': '缺少必要参数: ai_strategies 或 ai_strategy'})

        # 兼容性处理：将旧格式转换为新格式
        if ai_strategy and not ai_strategies:
            data['ai_strategies'] = [ai_strategy]
        elif ai_strategies and not ai_strategy:
            # 为了向后兼容，设置第一个策略为ai_strategy
            data['ai_strategy'] = ai_strategies[0] if ai_strategies else None

        # 启动智能体
        result = agent_trading_real_service.start_agent(
            user_id=current_user.id,
            config=data
        )

        if result['success']:
            return jsonify({
                'success': True,
                'message': '智能体真实交易已启动',
                'agent_id': result['agent_id']
            })
        else:
            return jsonify({'success': False, 'error': result['error']})

    except Exception as e:
        return jsonify({'success': False, 'error': str(e)})

@app.route('/api/agent-trading-real/stop', methods=['POST'])
@login_required
def api_stop_agent_trading_real():
    """停止智能体真实交易"""
    try:
        from services.agent_trading_real_service import agent_trading_real_service

        result = agent_trading_real_service.stop_agent(current_user.id)

        if result['success']:
            return jsonify({
                'success': True,
                'message': '智能体真实交易已停止'
            })
        else:
            return jsonify({'success': False, 'error': result['error']})

    except Exception as e:
        return jsonify({'success': False, 'error': str(e)})

@app.route('/api/agent-trading-real/pause', methods=['POST'])
@login_required
def api_pause_agent_trading_real():
    """暂停智能体真实交易"""
    try:
        from services.agent_trading_real_service import agent_trading_real_service

        result = agent_trading_real_service.pause_agent(current_user.id)

        if result['success']:
            return jsonify({
                'success': True,
                'message': '智能体真实交易已暂停'
            })
        else:
            return jsonify({'success': False, 'error': result['error']})

    except Exception as e:
        return jsonify({'success': False, 'error': str(e)})

@app.route('/api/agent-trading-real/analyze', methods=['POST'])
@login_required
def api_agent_analyze_real():
    """执行AI市场分析"""
    try:
        from services.agent_trading_real_service import agent_trading_real_service

        data = request.json or {}

        result = agent_trading_real_service.perform_ai_analysis(
            user_id=current_user.id,
            config=data
        )

        return jsonify(result)

    except Exception as e:
        return jsonify({'success': False, 'error': str(e)})

@app.route('/api/agent-trading-real/force-stop-all', methods=['POST'])
@login_required
def api_force_stop_all_agents():
    """强制停止所有智能体（紧急情况使用）"""
    try:
        from services.agent_trading_real_service import agent_trading_real_service

        result = agent_trading_real_service.force_stop_all_agents()

        return jsonify(result)

    except Exception as e:
        print(f"❌ 强制停止所有智能体失败: {e}")
        return jsonify({'success': False, 'error': str(e)})

@app.route('/api/agent-trading-real/execute', methods=['POST'])
@login_required
def api_agent_execute_real():
    """执行真实交易"""
    try:
        from services.agent_trading_real_service import agent_trading_real_service

        data = request.json or {}

        result = agent_trading_real_service.execute_trade(
            user_id=current_user.id,
            signal=data
        )

        return jsonify(result)

    except Exception as e:
        return jsonify({'success': False, 'error': str(e)})

@app.route('/api/agent-trading-real/stats', methods=['GET'])
@login_required
def api_agent_stats_real():
    """获取智能体交易统计"""
    try:
        from services.agent_trading_real_service import agent_trading_real_service

        result = agent_trading_real_service.get_trading_stats(current_user.id)

        return jsonify(result)

    except Exception as e:
        return jsonify({'success': False, 'error': str(e)})

@app.route('/api/mt5/status', methods=['GET'])
@login_required
def api_mt5_status():
    """获取MT5连接状态"""
    try:
        from services.mt5_service import mt5_service

        result = {
            'success': True,
            'connected': mt5_service.connected
        }

        # 如果已连接，获取账户信息
        if mt5_service.connected:
            account_info = mt5_service.get_account_info()
            if account_info:
                result['account_info'] = account_info

        return jsonify(result)

    except Exception as e:
        return jsonify({
            'success': False,
            'connected': False,
            'error': str(e)
        })

@app.route('/api/mt5/positions', methods=['GET'])
@login_required
def api_mt5_positions():
    """获取当前持仓"""
    try:
        from services.mt5_service import mt5_service

        positions = mt5_service.get_positions()

        return jsonify({
            'success': True,
            'positions': positions
        })

    except Exception as e:
        return jsonify({
            'success': False,
            'error': str(e)
        })

@app.route('/api/mt5/close-position', methods=['POST'])
@login_required
def api_mt5_close_position():
    """平仓单个持仓"""
    try:
        from services.mt5_service import mt5_service

        data = request.json or {}
        ticket = data.get('ticket')

        if not ticket:
            return jsonify({'success': False, 'error': '缺少订单号'})

        result = mt5_service.close_position(ticket)

        if result and hasattr(result, 'retcode') and result.retcode == 10009:
            return jsonify({
                'success': True,
                'message': '平仓成功',
                'ticket': result.order
            })
        else:
            error_msg = result.comment if result else '未知错误'
            return jsonify({
                'success': False,
                'error': f'平仓失败: {error_msg}'
            })

    except Exception as e:
        return jsonify({
            'success': False,
            'error': str(e)
        })

@app.route('/api/mt5/close-all-positions', methods=['POST'])
@login_required
def api_mt5_close_all_positions():
    """一键平仓所有持仓"""
    try:
        from services.mt5_service import mt5_service

        # 获取所有持仓
        positions = mt5_service.get_positions()

        if not positions:
            return jsonify({
                'success': True,
                'message': '没有需要平仓的持仓',
                'closed_count': 0,
                'failed_count': 0
            })

        closed_count = 0
        failed_count = 0

        for position in positions:
            try:
                result = mt5_service.close_position(position['ticket'])
                if result and hasattr(result, 'retcode') and result.retcode == 10009:
                    closed_count += 1
                else:
                    failed_count += 1
            except Exception as e:
                print(f"❌ 平仓订单 {position['ticket']} 失败: {e}")
                failed_count += 1

        return jsonify({
            'success': True,
            'message': f'平仓完成: 成功 {closed_count} 个，失败 {failed_count} 个',
            'closed_count': closed_count,
            'failed_count': failed_count
        })

    except Exception as e:
        return jsonify({
            'success': False,
            'error': str(e)
        })

@app.route('/api/mt5/trading-history-today', methods=['GET'])
@login_required
def api_mt5_trading_history_today():
    """获取当天交易历史"""
    try:
        from services.mt5_service import mt5_service
        from datetime import datetime, timedelta

        # 获取今日开始和结束时间
        today = datetime.now().date()
        start_time = datetime.combine(today, datetime.min.time())
        end_time = datetime.combine(today, datetime.max.time())

        trades = mt5_service.get_trade_history(start_time, end_time)

        return jsonify({
            'success': True,
            'trades': trades,
            'date': today.isoformat()
        })

    except Exception as e:
        return jsonify({
            'success': False,
            'error': str(e)
        })

@app.route('/api/stop-loss-take-profit/start', methods=['POST'])
@login_required
def api_start_stop_loss_monitoring():
    """启动止盈止损监控"""
    try:
        from services.stop_loss_take_profit_service import stop_loss_take_profit_service

        stop_loss_take_profit_service.start_monitoring()

        return jsonify({
            'success': True,
            'message': '止盈止损监控已启动'
        })

    except Exception as e:
        return jsonify({
            'success': False,
            'error': str(e)
        })

@app.route('/api/stop-loss-take-profit/stop', methods=['POST'])
@login_required
def api_stop_stop_loss_monitoring():
    """停止止盈止损监控"""
    try:
        from services.stop_loss_take_profit_service import stop_loss_take_profit_service

        stop_loss_take_profit_service.stop_monitoring()

        return jsonify({
            'success': True,
            'message': '止盈止损监控已停止'
        })

    except Exception as e:
        return jsonify({
            'success': False,
            'error': str(e)
        })

@app.route('/api/stop-loss-take-profit/status', methods=['GET'])
@login_required
def api_stop_loss_monitoring_status():
    """获取止盈止损监控状态"""
    try:
        from services.stop_loss_take_profit_service import stop_loss_take_profit_service

        status = stop_loss_take_profit_service.get_monitoring_status()

        return jsonify({
            'success': True,
            'status': status
        })

    except Exception as e:
        return jsonify({
            'success': False,
            'error': str(e)
        })

@app.route('/api/agent-trading-real/status', methods=['GET'])
@login_required
def api_agent_trading_real_status():
    """获取智能体交易状态"""
    try:
        from services.agent_trading_real_service import agent_trading_real_service

        # 获取当前用户的智能体状态
        user_id = current_user.id
        status = agent_trading_real_service.get_agent_status(user_id)

        if status['success']:
            return jsonify({
                'success': True,
                'running': status['running'],
                'status': status['status'],
                'start_time': status['start_time'],
                'last_analysis': status['last_analysis'],
                'total_trades': status['total_trades'],
                'total_profit': status['total_profit'],
                'thread_alive': status.get('thread_alive', False)
            })
        else:
            return jsonify(status)

    except Exception as e:
        return jsonify({
            'success': False,
            'error': str(e)
        })

@app.route('/api/risk-events', methods=['GET'])
@login_required
def api_get_risk_events():
    """获取当前风险事件"""
    try:
        from services.risk_event_service import risk_event_service

        events = risk_event_service.get_current_risk_events()
        summary = risk_event_service.get_risk_summary()

        return jsonify({
            'success': True,
            'events': events,
            'summary': summary
        })

    except Exception as e:
        return jsonify({
            'success': False,
            'error': str(e)
        })

@app.route('/api/risk-events/summary', methods=['GET'])
@login_required
def api_get_risk_summary():
    """获取风险事件摘要"""
    try:
        from services.risk_event_service import risk_event_service

        summary = risk_event_service.get_risk_summary()

        return jsonify({
            'success': True,
            'summary': summary
        })

    except Exception as e:
        return jsonify({
            'success': False,
            'error': str(e)
        })

# MT5相关API路由
@app.route('/api/mt5/connect', methods=['POST'])
@login_required
def api_mt5_connect():
    """连接MT5"""
    try:
        from services.mt5_service import mt5_service
        data = request.json or {}

        print(f"🔗 收到MT5连接请求: {data}")

        result = mt5_service.connect(
            login=data.get('login'),
            password=data.get('password'),
            server=data.get('server')
        )

        if result:
            print("✅ MT5连接成功，获取账户信息...")
            account_info_response = mt5_service.get_account_info()

            if account_info_response and account_info_response.get('success'):
                account_info = account_info_response.get('account_info')
                if account_info:
                    print(f"✅ 账户信息获取成功: {account_info.get('login')}")
                    return jsonify({
                        'success': True,
                        'message': 'MT5连接成功',
                        'account_info': account_info
                    })
                else:
                    print("⚠️ 账户信息为空")
                    return jsonify({
                        'success': False,
                        'error': 'MT5连接成功但账户信息为空'
                    })
            else:
                print("⚠️ 连接成功但无账户信息")
                return jsonify({
                    'success': False,
                    'error': 'MT5连接成功但无法获取账户信息，请确保MT5客户端已登录'
                })
        else:
            print("❌ MT5连接失败")
            return jsonify({
                'success': False,
                'error': 'MT5连接失败。请确保：\n1. MT5客户端已启动\n2. 已登录到交易账户\n3. 允许自动交易和DLL导入'
            })

    except ImportError as e:
        print(f"❌ MT5库导入失败: {e}")
        return jsonify({
            'success': False,
            'error': 'MetaTrader5库未安装。请运行: pip install MetaTrader5'
        })
    except Exception as e:
        print(f"❌ MT5连接API异常: {e}")
        return jsonify({
            'success': False,
            'error': f'连接失败: {str(e)}'
        })

@app.route('/api/mt5/disconnect', methods=['POST'])
@login_required
def api_mt5_disconnect():
    """断开MT5连接"""
    try:
        from services.mt5_service import mt5_service
        mt5_service.disconnect()
        return jsonify({'success': True, 'message': 'MT5连接已断开'})
    except Exception as e:
        return jsonify({'success': False, 'error': str(e)})

@app.route('/api/mt5/symbols')
@login_required
def api_mt5_symbols():
    """获取MT5交易品种"""
    try:
        from services.mt5_service import mt5_service
        symbols = mt5_service.get_symbols()
        return jsonify({'success': True, 'symbols': symbols})
    except Exception as e:
        return jsonify({'success': False, 'error': str(e)})

@app.route('/api/mt5/rates/<symbol>')
@login_required
def api_mt5_rates(symbol):
    """获取MT5价格数据"""
    try:
        from services.mt5_service import mt5_service
        import MetaTrader5 as mt5

        timeframe = request.args.get('timeframe', 'H1')
        count = int(request.args.get('count', 500))

        # 转换时间框架
        tf_map = {
            'M1': mt5.TIMEFRAME_M1,
            'M5': mt5.TIMEFRAME_M5,
            'M15': mt5.TIMEFRAME_M15,
            'M30': mt5.TIMEFRAME_M30,
            'H1': mt5.TIMEFRAME_H1,
            'H4': mt5.TIMEFRAME_H4,
            'D1': mt5.TIMEFRAME_D1,
            'W1': mt5.TIMEFRAME_W1,
            'MN1': mt5.TIMEFRAME_MN1
        }

        tf = tf_map.get(timeframe, mt5.TIMEFRAME_H1)
        rates_df = mt5_service.get_rates(symbol, tf, count)

        if not rates_df.empty:
            # 转换为JSON格式
            rates_data = []
            for index, row in rates_df.iterrows():
                rates_data.append({
                    'time': index.isoformat(),
                    'open': row['Open'],
                    'high': row['High'],
                    'low': row['Low'],
                    'close': row['Close'],
                    'volume': row['Volume']
                })

            return jsonify({'success': True, 'data': rates_data})
        else:
            return jsonify({'success': False, 'error': '无法获取价格数据'})
    except Exception as e:
        return jsonify({'success': False, 'error': str(e)})

@app.route('/api/mt5/debug-positions')
@login_required
def api_mt5_debug_positions():
    """调试MT5持仓获取"""
    try:
        from services.mt5_service import mt5_service
        import MetaTrader5 as mt5

        debug_info = {
            'success': True,
            'debug_steps': [],
            'positions': [],
            'connection_status': {},
            'account_info': {}
        }

        # 1. 检查MT5服务连接状态
        debug_info['debug_steps'].append('1. 检查MT5服务连接状态')
        debug_info['connection_status']['service_connected'] = mt5_service.connected

        # 2. 检查MT5库连接状态
        debug_info['debug_steps'].append('2. 检查MT5库连接状态')
        try:
            terminal_info = mt5.terminal_info()
            if terminal_info:
                debug_info['connection_status']['terminal_connected'] = terminal_info.connected
                debug_info['connection_status']['terminal_info'] = {
                    'name': terminal_info.name,
                    'version': terminal_info.version,
                    'build': terminal_info.build,
                    'connected': terminal_info.connected
                }
            else:
                debug_info['connection_status']['terminal_connected'] = False
                debug_info['debug_steps'].append('   ❌ 无法获取终端信息')
        except Exception as e:
            debug_info['debug_steps'].append(f'   ❌ 获取终端信息异常: {str(e)}')

        # 3. 获取账户信息
        debug_info['debug_steps'].append('3. 获取账户信息')
        try:
            account_info = mt5.account_info()
            if account_info:
                debug_info['account_info'] = {
                    'login': account_info.login,
                    'server': account_info.server,
                    'name': account_info.name,
                    'company': account_info.company,
                    'currency': account_info.currency,
                    'balance': account_info.balance,
                    'equity': account_info.equity,
                    'margin': account_info.margin,
                    'trade_allowed': account_info.trade_allowed
                }
                debug_info['debug_steps'].append(f'   ✅ 账户: {account_info.login}@{account_info.server}')
            else:
                debug_info['debug_steps'].append('   ❌ 无法获取账户信息')
        except Exception as e:
            debug_info['debug_steps'].append(f'   ❌ 获取账户信息异常: {str(e)}')

        # 4. 尝试获取持仓（直接调用MT5库）
        debug_info['debug_steps'].append('4. 直接调用MT5库获取持仓')
        try:
            positions = mt5.positions_get()
            if positions is not None:
                debug_info['debug_steps'].append(f'   ✅ 获取到 {len(positions)} 个持仓')

                for pos in positions:
                    position_data = {
                        'ticket': pos.ticket,
                        'symbol': pos.symbol,
                        'type': pos.type,
                        'volume': pos.volume,
                        'price_open': pos.price_open,
                        'price_current': pos.price_current,
                        'profit': pos.profit,
                        'swap': pos.swap,
                        'comment': pos.comment,
                        'time': pos.time,
                        'magic': pos.magic
                    }
                    debug_info['positions'].append(position_data)
                    debug_info['debug_steps'].append(f'     - {pos.symbol} {pos.type} {pos.volume} 盈亏:{pos.profit} Magic:{pos.magic}')
            else:
                debug_info['debug_steps'].append('   ⚠️ positions_get() 返回 None')
        except Exception as e:
            debug_info['debug_steps'].append(f'   ❌ 直接获取持仓异常: {str(e)}')

        # 5. 通过服务获取持仓
        debug_info['debug_steps'].append('5. 通过MT5服务获取持仓')
        try:
            service_positions = mt5_service.get_positions()
            debug_info['debug_steps'].append(f'   服务返回: {service_positions}')
            if service_positions:
                debug_info['debug_steps'].append(f'   ✅ 服务获取到 {len(service_positions)} 个持仓')
            else:
                debug_info['debug_steps'].append('   ⚠️ 服务返回空列表或None')
        except Exception as e:
            debug_info['debug_steps'].append(f'   ❌ 服务获取持仓异常: {str(e)}')

        return jsonify(debug_info)

    except Exception as e:
        return jsonify({
            'success': False,
            'error': f'调试过程异常: {str(e)}',
            'debug_steps': ['调试过程发生异常']
        })

@app.route('/api/mt5/send-order', methods=['POST'])
@login_required
def api_mt5_send_order():
    """发送MT5订单"""
    try:
        from services.mt5_service import mt5_service
        import MetaTrader5 as mt5

        data = request.json

        # 转换订单类型
        order_type_map = {
            'buy': mt5.ORDER_TYPE_BUY,
            'sell': mt5.ORDER_TYPE_SELL,
            'buy_limit': mt5.ORDER_TYPE_BUY_LIMIT,
            'sell_limit': mt5.ORDER_TYPE_SELL_LIMIT,
            'buy_stop': mt5.ORDER_TYPE_BUY_STOP,
            'sell_stop': mt5.ORDER_TYPE_SELL_STOP
        }

        order_type = order_type_map.get(data['type'], mt5.ORDER_TYPE_BUY)

        result = mt5_service.send_order(
            symbol=data['symbol'],
            order_type=order_type,
            volume=data['volume'],
            price=data.get('price', 0.0),
            sl=data.get('sl', 0.0),
            tp=data.get('tp', 0.0),
            comment=data.get('comment', 'Python order')
        )

        return jsonify(result)
    except Exception as e:
        return jsonify({'success': False, 'error': str(e)})

@app.route('/api/close-all-positions', methods=['POST'])
@login_required
def api_close_all_positions():
    """一键平仓API"""
    try:
        data = request.json
        password = data.get('password')

        # 验证密码
        if password != '12356':
            return jsonify({'success': False, 'error': '安全密码错误'})

        from services.position_manager import position_manager
        from services.mt5_service import mt5_service
        from models import Trade, TradingAccount

        results = {
            'success': True,
            'total_positions': 0,
            'closed_positions': 0,
            'failed_positions': 0,
            'details': [],
            'total_profit': 0.0
        }

        # 1. 平仓所有本地交易记录中的开仓持仓
        # 通过TradingAccount关联查询当前用户的交易
        open_trades = Trade.query.join(TradingAccount).filter(
            Trade.status == 'open',
            TradingAccount.user_id == current_user.id
        ).all()

        for trade in open_trades:
            results['total_positions'] += 1

            try:
                # 使用position_manager平仓
                close_result = position_manager.close_position(trade.id, 'emergency_close_all')

                if close_result.get('success'):
                    results['closed_positions'] += 1
                    profit = close_result.get('profit', 0)
                    results['total_profit'] += profit

                    results['details'].append({
                        'type': 'local',
                        'symbol': trade.symbol,
                        'trade_id': trade.id,
                        'status': 'success',
                        'profit': profit,
                        'message': '平仓成功'
                    })
                else:
                    results['failed_positions'] += 1
                    results['details'].append({
                        'type': 'local',
                        'symbol': trade.symbol,
                        'trade_id': trade.id,
                        'status': 'failed',
                        'profit': 0,
                        'message': close_result.get('error', '平仓失败')
                    })

            except Exception as e:
                results['failed_positions'] += 1
                results['details'].append({
                    'type': 'local',
                    'symbol': trade.symbol,
                    'trade_id': trade.id,
                    'status': 'error',
                    'profit': 0,
                    'message': str(e)
                })

        # 2. 平仓所有MT5持仓
        try:
            # 直接获取MT5持仓列表
            mt5_positions = mt5_service.get_positions()

            results['details'].append({
                'type': 'mt5_debug',
                'message': f'MT5持仓获取结果: {mt5_positions}'
            })

            if mt5_positions and len(mt5_positions) > 0:
                results['details'].append({
                    'type': 'mt5_info',
                    'message': f'找到 {len(mt5_positions)} 个MT5持仓'
                })

                for position in mt5_positions:
                    results['total_positions'] += 1

                    # 添加持仓处理日志
                    results['details'].append({
                        'type': 'mt5_debug',
                        'message': f'处理持仓: {position["symbol"]} 票据:{position["ticket"]} Magic:{position.get("magic", "Unknown")}'
                    })

                    try:
                        close_result = mt5_service.close_position(position['ticket'])

                        results['details'].append({
                            'type': 'mt5_debug',
                            'message': f'平仓结果: {close_result}'
                        })

                        if close_result and hasattr(close_result, 'retcode') and close_result.retcode == 10009:  # TRADE_RETCODE_DONE
                            results['closed_positions'] += 1
                            profit = position.get('profit', 0)
                            results['total_profit'] += profit

                            results['details'].append({
                                'type': 'mt5',
                                'symbol': position['symbol'],
                                'ticket': position['ticket'],
                                'status': 'success',
                                'profit': profit,
                                'message': 'MT5平仓成功'
                            })
                        else:
                            results['failed_positions'] += 1
                            error_msg = 'MT5平仓失败'
                            if close_result:
                                if hasattr(close_result, 'comment'):
                                    error_msg = f'MT5平仓失败: {close_result.comment}'
                                elif hasattr(close_result, 'retcode'):
                                    error_msg = f'MT5平仓失败: 返回码 {close_result.retcode}'

                            results['details'].append({
                                'type': 'mt5',
                                'symbol': position['symbol'],
                                'ticket': position['ticket'],
                                'status': 'failed',
                                'profit': 0,
                                'message': error_msg
                            })

                    except Exception as e:
                        results['failed_positions'] += 1
                        results['details'].append({
                            'type': 'mt5',
                            'symbol': position['symbol'],
                            'ticket': position['ticket'],
                            'status': 'error',
                            'profit': 0,
                            'message': str(e)
                        })
            else:
                results['details'].append({
                    'type': 'mt5_info',
                    'message': 'MT5当前无持仓'
                })

        except Exception as e:
            # MT5连接失败不影响整体结果
            results['details'].append({
                'type': 'mt5',
                'symbol': 'MT5连接',
                'ticket': 0,
                'status': 'error',
                'profit': 0,
                'message': f'MT5连接失败: {str(e)}'
            })

        # 3. 设置最终结果状态
        if results['failed_positions'] > 0:
            results['success'] = False
            results['message'] = f'部分平仓失败：成功 {results["closed_positions"]} 个，失败 {results["failed_positions"]} 个'
        else:
            results['message'] = f'一键平仓完成：成功平仓 {results["closed_positions"]} 个持仓'

        return jsonify(results)

    except Exception as e:
        return jsonify({
            'success': False,
            'error': f'一键平仓执行异常: {str(e)}',
            'total_positions': 0,
            'closed_positions': 0,
            'failed_positions': 0,
            'details': []
        })

@app.route('/api/all-positions-overview', methods=['GET'])
@login_required
def api_all_positions_overview():
    """获取所有持仓概览（用于一键平仓）"""
    try:
        from services.mt5_service import mt5_service
        from models import Trade, TradingAccount

        overview = {
            'success': True,
            'total_positions': 0,
            'total_unrealized_pnl': 0.0,
            'long_positions': 0,
            'short_positions': 0,
            'api_positions': [],
            'mt5_positions': [],
            'details': []
        }

        # 1. 获取API账户持仓
        try:
            api_trades = Trade.query.join(TradingAccount).filter(
                Trade.status == 'open',
                TradingAccount.user_id == current_user.id
            ).all()

            for trade in api_trades:
                overview['total_positions'] += 1
                overview['total_unrealized_pnl'] += trade.profit or 0

                if trade.trade_type == 'buy':
                    overview['long_positions'] += 1
                else:
                    overview['short_positions'] += 1

                position_data = {
                    'trade_id': trade.id,
                    'symbol': trade.symbol,
                    'side': trade.trade_type,
                    'volume': trade.volume,
                    'open_price': trade.open_price,
                    'unrealized_pnl': trade.profit or 0,
                    'account_id': trade.account_id
                }

                overview['api_positions'].append(position_data)
                overview['details'].append({
                    'type': 'api',
                    'symbol': trade.symbol,
                    'trade_id': trade.id,
                    'side': trade.trade_type,
                    'volume': trade.volume,
                    'unrealized_pnl': trade.profit or 0
                })

        except Exception as e:
            overview['details'].append({
                'type': 'api_error',
                'message': f'获取API持仓失败: {str(e)}'
            })

        # 2. 获取MT5持仓
        try:
            # 检查MT5连接状态
            mt5_connected = mt5_service.connected
            overview['details'].append({
                'type': 'mt5_debug',
                'message': f'MT5连接状态: {mt5_connected}'
            })

            if not mt5_connected:
                overview['details'].append({
                    'type': 'mt5_warning',
                    'message': 'MT5未连接，尝试重新连接...'
                })
                # 尝试重新连接
                if mt5_service.connect():
                    overview['details'].append({
                        'type': 'mt5_info',
                        'message': 'MT5重新连接成功'
                    })
                else:
                    overview['details'].append({
                        'type': 'mt5_error',
                        'message': 'MT5重新连接失败'
                    })

            # 获取持仓数据
            mt5_positions = mt5_service.get_positions()
            overview['details'].append({
                'type': 'mt5_debug',
                'message': f'MT5原始持仓数据: {mt5_positions}'
            })

            if mt5_positions and len(mt5_positions) > 0:
                overview['details'].append({
                    'type': 'mt5_info',
                    'message': f'获取到 {len(mt5_positions)} 个MT5持仓'
                })

                for position in mt5_positions:
                    overview['total_positions'] += 1
                    overview['total_unrealized_pnl'] += position.get('profit', 0)

                    if position.get('type') == 0:  # 多头
                        overview['long_positions'] += 1
                    else:  # 空头
                        overview['short_positions'] += 1

                    position_data = {
                        'ticket': position['ticket'],
                        'symbol': position['symbol'],
                        'side': 'buy' if position.get('type') == 0 else 'sell',
                        'volume': position['volume'],
                        'open_price': position['price_open'],
                        'current_price': position.get('price_current'),
                        'unrealized_pnl': position.get('profit', 0)
                    }

                    overview['mt5_positions'].append(position_data)
                    overview['details'].append({
                        'type': 'mt5',
                        'symbol': position['symbol'],
                        'ticket': position['ticket'],
                        'side': 'buy' if position.get('type') == 0 else 'sell',
                        'volume': position['volume'],
                        'unrealized_pnl': position.get('profit', 0)
                    })
            else:
                overview['details'].append({
                    'type': 'mt5_info',
                    'message': 'MT5当前无持仓或获取失败'
                })

        except Exception as e:
            overview['details'].append({
                'type': 'mt5_error',
                'message': f'获取MT5持仓异常: {str(e)}'
            })

        return jsonify(overview)

    except Exception as e:
        return jsonify({
            'success': False,
            'error': f'获取持仓概览失败: {str(e)}',
            'total_positions': 0,
            'total_unrealized_pnl': 0.0,
            'long_positions': 0,
            'short_positions': 0,
            'api_positions': [],
            'mt5_positions': [],
            'details': []
        })

@app.route('/api/mt5/close-position-by-id/<int:ticket>', methods=['POST'])
@login_required
def api_mt5_close_position_by_id(ticket):
    """通过URL参数平仓MT5持仓"""
    try:
        from services.mt5_service import mt5_service
        result = mt5_service.close_position(ticket)
        return jsonify(result)
    except Exception as e:
        return jsonify({'success': False, 'error': str(e)})

@app.route('/api/mt5/close-position-legacy', methods=['POST'])
@login_required
def api_mt5_close_position_legacy():
    """通过POST请求平仓MT5持仓"""
    data = request.json

    try:
        ticket = data.get('ticket')
        if not ticket:
            return jsonify({'success': False, 'error': '缺少持仓票据号'})

        from services.mt5_service import mt5_service
        result = mt5_service.close_position(ticket)

        if result.get('success'):
            return jsonify({
                'success': True,
                'message': '平仓成功',
                'result': result
            })
        else:
            return jsonify({
                'success': False,
                'error': result.get('error', '平仓失败')
            })

    except Exception as e:
        print(f"MT5平仓失败: {str(e)}")
        return jsonify({'success': False, 'error': str(e)})

@app.route('/api/mt5/auto-connect', methods=['POST'])
@login_required
def api_mt5_auto_connect():
    """MT5自动连接API - 专门用于真实交易模块"""
    try:
        print("🔄 开始MT5自动连接...")

        from services.mt5_service import mt5_service

        # 尝试自动连接MT5
        result = mt5_service.connect()

        if result:
            # 获取账户信息
            account_info_response = mt5_service.get_account_info()

            if account_info_response and account_info_response.get('success'):
                account_info = account_info_response.get('account_info')
                print(f"✅ MT5自动连接成功: 账户 {account_info.get('login')}")

                return jsonify({
                    'success': True,
                    'message': 'MT5自动连接成功',
                    'account_info': account_info
                })
            else:
                print("❌ MT5连接成功但无法获取账户信息")
                return jsonify({
                    'success': False,
                    'error': 'MT5连接成功但无法获取账户信息'
                })
        else:
            print("❌ MT5自动连接失败")
            return jsonify({
                'success': False,
                'error': 'MT5客户端未运行或连接失败'
            })

    except Exception as e:
        print(f"❌ MT5自动连接异常: {str(e)}")
        return jsonify({'success': False, 'error': f'连接异常: {str(e)}'})

@app.route('/api/mt5/history', methods=['GET'])
@login_required
def api_mt5_history():
    """获取MT5交易历史"""
    try:
        from services.mt5_service import mt5_service
        from datetime import datetime, timedelta

        # 获取最近30天的交易历史
        end_date = datetime.now()
        start_date = end_date - timedelta(days=30)

        trades = mt5_service.get_trade_history(start_date, end_date)

        if trades is not None:
            # 直接返回MT5的原始数据，不进行修正
            print(f"✅ 成功获取 {len(trades)} 条有效交易记录，使用MT5原始盈亏数据")

            return jsonify({
                'success': True,
                'history': trades,
                'total_count': len(trades)
            })
        else:
            return jsonify({
                'success': False,
                'error': '无法获取MT5交易历史'
            })
    except Exception as e:
        print(f"获取MT5交易历史失败: {str(e)}")
        return jsonify({'success': False, 'error': str(e)})

@app.route('/api/mt5/debug-profit', methods=['GET'])
@login_required
def api_mt5_debug_profit():
    """调试MT5盈亏计算"""
    try:
        from services.mt5_service import mt5_service
        from datetime import datetime, timedelta
        import MetaTrader5 as mt5

        debug_info = {
            'success': True,
            'account_info': {},
            'symbol_info': {},
            'recent_trades': [],
            'profit_analysis': {}
        }

        # 1. 获取账户信息
        try:
            account_info = mt5.account_info()
            if account_info:
                debug_info['account_info'] = {
                    'login': account_info.login,
                    'server': account_info.server,
                    'currency': account_info.currency,
                    'balance': account_info.balance,
                    'equity': account_info.equity,
                    'margin': account_info.margin,
                    'leverage': account_info.leverage
                }
        except Exception as e:
            debug_info['account_info'] = {'error': str(e)}

        # 2. 获取最近的交易记录进行分析
        end_date = datetime.now()
        start_date = end_date - timedelta(days=7)
        trades = mt5_service.get_trade_history(start_date, end_date)

        if trades and len(trades) > 0:
            # 分析最近的几笔交易
            recent_trades = trades[:5]  # 取最近5笔交易

            for trade in recent_trades:
                symbol = trade['symbol']

                # 获取货币对信息
                symbol_info = mt5.symbol_info(symbol)
                if symbol_info:
                    symbol_data = {
                        'symbol': symbol,
                        'digits': symbol_info.digits,
                        'point': symbol_info.point,
                        'trade_contract_size': symbol_info.trade_contract_size,
                        'volume_min': symbol_info.volume_min,
                        'volume_max': symbol_info.volume_max,
                        'currency_base': symbol_info.currency_base,
                        'currency_profit': symbol_info.currency_profit,
                        'currency_margin': symbol_info.currency_margin
                    }
                    debug_info['symbol_info'][symbol] = symbol_data

                # 分析盈亏
                trade_analysis = {
                    'ticket': trade['ticket'],
                    'symbol': symbol,
                    'type': 'BUY' if trade['type'] == 0 else 'SELL',
                    'volume': trade['volume'],
                    'price': trade['price'],
                    'original_profit': trade['profit'],
                    'swap': trade.get('swap', 0),
                    'commission': trade.get('commission', 0),
                    'entry': trade.get('entry', 'Unknown')
                }

                # 计算理论盈亏（如果有足够信息）
                if symbol_info:
                    contract_size = symbol_info.trade_contract_size
                    point = symbol_info.point

                    # 估算每点价值
                    point_value = contract_size * point

                    trade_analysis['contract_size'] = contract_size
                    trade_analysis['point'] = point
                    trade_analysis['point_value'] = point_value

                    # 如果盈亏异常大，计算可能的修正值
                    if abs(trade['profit']) > 1000:
                        possible_corrections = {
                            'divide_by_10': trade['profit'] / 10,
                            'divide_by_100': trade['profit'] / 100,
                            'divide_by_1000': trade['profit'] / 1000,
                            'divide_by_contract_size': trade['profit'] / contract_size if contract_size > 0 else 0
                        }
                        trade_analysis['possible_corrections'] = possible_corrections

                debug_info['recent_trades'].append(trade_analysis)

        # 3. 盈亏分析总结
        if trades:
            profits = [t['profit'] for t in trades]
            debug_info['profit_analysis'] = {
                'total_trades': len(trades),
                'profit_range': {
                    'min': min(profits),
                    'max': max(profits),
                    'avg': sum(profits) / len(profits)
                },
                'large_profits': len([p for p in profits if abs(p) > 1000]),
                'normal_profits': len([p for p in profits if abs(p) <= 100]),
                'medium_profits': len([p for p in profits if 100 < abs(p) <= 1000])
            }

        return jsonify(debug_info)

    except Exception as e:
        return jsonify({
            'success': False,
            'error': str(e)
        })

@app.route('/api/test-simple', methods=['GET'])
@login_required
def api_test_simple():
    """简单测试API"""
    return jsonify({
        'success': True,
        'message': 'API工作正常',
        'timestamp': datetime.now().isoformat()
    })

@app.route('/api/test-no-auth', methods=['GET'])
def api_test_no_auth():
    """无需登录的测试API"""
    return jsonify({
        'success': True,
        'message': '无需登录的API工作正常',
        'timestamp': datetime.now().isoformat()
    })

@app.route('/api/mt5/history-no-auth', methods=['GET'])
def api_mt5_history_no_auth():
    """无需登录的MT5交易历史API（仅用于调试）"""
    try:
        from services.mt5_service import mt5_service
        from datetime import datetime, timedelta

        # 获取最近30天的交易历史
        end_date = datetime.now()
        start_date = end_date - timedelta(days=30)

        trades = mt5_service.get_trade_history(start_date, end_date)

        if trades is not None:
            # 直接返回MT5的原始数据，不进行修正
            print(f"✅ 调试API: 成功获取 {len(trades)} 条有效交易记录，使用MT5原始盈亏数据")

            return jsonify({
                'success': True,
                'history': trades,
                'debug_info': {
                    'total_trades': len(trades),
                    'note': '这是无需登录的调试版本，使用MT5原始数据'
                }
            })
        else:
            return jsonify({
                'success': False,
                'error': '无法获取MT5交易历史'
            })
    except Exception as e:
        print(f"获取MT5交易历史失败: {str(e)}")
        return jsonify({'success': False, 'error': str(e)})

# 图表相关API
@app.route('/api/charts/candlestick')
@login_required
def api_charts_candlestick():
    """获取K线图表"""
    try:
        from services.chart_service import chart_service
        import yfinance as yf

        symbol = request.args.get('symbol', 'EURUSD=X')
        period = request.args.get('period', '1d')
        interval = request.args.get('interval', '1h')

        # 获取数据
        ticker = yf.Ticker(symbol)
        data = ticker.history(period=period, interval=interval)

        if data.empty:
            return jsonify({'success': False, 'error': '无法获取数据'})

        # 计算技术指标
        from services.technical_indicators import TechnicalIndicators
        tech_indicators = TechnicalIndicators()

        indicators = {}

        # 移动平均线
        indicators['ma'] = {
            '5': tech_indicators.sma(data['Close'], 5),
            '20': tech_indicators.sma(data['Close'], 20)
        }

        # MACD
        indicators['macd'] = tech_indicators.macd(data['Close'])

        # RSI
        indicators['rsi'] = tech_indicators.rsi(data['Close'])

        # 布林带
        indicators['bollinger'] = tech_indicators.bollinger_bands(data['Close'])

        # 成交量
        indicators['volume'] = True

        # 创建图表
        chart_html = chart_service.create_candlestick_chart(
            data, symbol, indicators
        )

        return jsonify({'success': True, 'chart': chart_html})
    except Exception as e:
        return jsonify({'success': False, 'error': str(e)})

# 新增页面路由
@app.route('/charts')
@login_required
def charts():
    """专业图表分析页面"""
    return render_template('analysis/charts.html')

# ==================== 深度学习模块路由 ====================

@app.route('/deep-learning')
@login_required
def deep_learning_dashboard():
    """深度学习仪表板"""
    return render_template('deep_learning_dashboard.html')

@app.route('/deep-learning/training')
@login_required
def model_training():
    """模型训练页面"""
    return render_template('model_training.html')

@app.route('/deep-learning/management')
@login_required
def model_management():
    """模型管理页面"""
    return render_template('model_management.html')

@app.route('/deep-learning/inference')
@login_required
def model_inference():
    """模型推理页面"""
    return render_template('model_inference.html')

@app.route('/deep-learning/gpu-monitor')
@login_required
def gpu_monitor():
    """GPU监控页面"""
    return render_template('gpu_monitor.html')

@app.route('/mt5-connection')
@login_required
def mt5_connection():
    """MT5连接管理页面"""
    return render_template('trading/mt5_connection.html')

# 风险管理页面已删除

@app.route('/test_api')
def test_api():
    """API测试页面"""
    return send_from_directory('.', 'test_api.html')

@app.route('/test_timespan')
def test_timespan():
    """时间跨度显示测试页面"""
    return send_from_directory('.', 'test_timespan_display.html')

@app.route('/test_close_all')
def test_close_all():
    """一键平仓功能测试页面"""
    return send_from_directory('.', 'test_close_all_positions.html')

@app.route('/test_positions')
def test_positions():
    """持仓概览API测试页面"""
    return send_from_directory('.', 'test_positions_overview.html')

@app.route('/test_agent_apis')
def test_agent_apis():
    """智能体交易API测试页面"""
    return send_from_directory('.', 'test_agent_trading_apis.html')

@app.route('/test_ai_fix')
def test_ai_fix():
    """AI策略关联问题修复验证页面"""
    return send_from_directory('.', 'test_ai_strategies_fix.html')

@app.route('/test_mt5_profit')
def test_mt5_profit():
    """MT5盈亏计算调试页面"""
    return send_from_directory('.', 'test_mt5_profit_debug.html')

@app.route('/test_api_simple')
def test_api_simple():
    """API连接测试页面"""
    return send_from_directory('.', 'test_api_simple.html')

@app.route('/test_profit_fixed')
def test_profit_fixed():
    """MT5盈亏修复结果验证页面"""
    return send_from_directory('.', 'test_profit_fix_result.html')

@app.route('/test_ai_strategies')
def test_ai_strategies():
    """AI策略加载调试页面"""
    return send_from_directory('.', 'test_ai_strategies_debug.html')

@app.route('/test_agent_start')
def test_agent_start():
    """智能体启动调试页面"""
    return send_from_directory('.', 'test_agent_start_debug.html')

@app.route('/test_ai_call')
def test_ai_call():
    """AI模型调用测试页面"""
    return send_from_directory('.', 'test_ai_model_call.html')

@app.route('/test_agent_close')
def test_agent_close():
    """智能体平仓功能测试页面"""
    return send_from_directory('.', 'test_agent_close.html')

@app.route('/test_stop_loss')
def test_stop_loss():
    """止盈止损自动平仓测试页面"""
    return send_from_directory('.', 'test_stop_loss_take_profit.html')

@app.route('/test_ai_integration')
def test_ai_integration():
    """AI智能平仓与止盈止损整合测试页面"""
    return send_from_directory('.', 'test_ai_stop_loss_integration.html')

@app.route('/test_risk_events')
def test_risk_events():
    """风险事件监控测试页面"""
    return send_from_directory('.', 'test_risk_events.html')

# AI策略管理API
@app.route('/api/ai-strategies/save', methods=['POST'])
@login_required
def api_save_ai_strategy():
    """保存AI策略"""
    try:
        # 使用简化的AI策略服务避免数组歧义问题
        from services.ai_strategy_service_simple import AIStrategyService
        from datetime import datetime
        data = request.json

        # 准备训练配置，包含交易品种和时间跨度信息
        training_config = data.get('training_config', {})
        training_config['symbols'] = data.get('symbols', [])
        training_config['symbols_text'] = data.get('symbols_text', '')
        training_config['name'] = data.get('name', 'AI策略')
        training_config['description'] = data.get('description', 'AI训练策略')
        training_config['ai_model'] = data.get('ai_model', 'LSTM')

        # 添加时间跨度信息
        if 'training_period' in training_config:
            training_config['start_date'] = training_config['training_period'].get('start')
            training_config['end_date'] = training_config['training_period'].get('end')

        # 创建简化的AI策略服务实例
        ai_service = AIStrategyService()

        strategy = ai_service.create_ai_strategy(current_user, training_config)
        if not strategy:
            return jsonify({
                'success': False,
                'error': '创建AI策略失败'
            })

        # 模拟训练过程（使用简化版本避免数组问题）
        training_result = ai_service.simulate_training_process(
            strategy['id'], training_config
        )

        if training_result['success']:
            # 使用原生SQL更新策略状态为已完成
            import sqlite3
            conn = sqlite3.connect('trading_system.db')
            cursor = conn.cursor()

            cursor.execute("""
                UPDATE strategy
                SET status = ?, updated_at = ?
                WHERE id = ?
            """, ('completed', datetime.utcnow().isoformat(), strategy['id']))

            conn.commit()
            conn.close()

            return jsonify({
                'success': True,
                'strategy_id': strategy['id'],
                'message': 'AI策略保存成功'
            })
        else:
            # 使用原生SQL更新策略状态为失败
            import sqlite3
            conn = sqlite3.connect('trading_system.db')
            cursor = conn.cursor()

            cursor.execute("""
                UPDATE strategy
                SET status = ?, updated_at = ?
                WHERE id = ?
            """, ('failed', datetime.utcnow().isoformat(), strategy['id']))

            conn.commit()
            conn.close()

            return jsonify({
                'success': False,
                'error': training_result.get('error', '训练失败')
            })

    except Exception as e:
        return jsonify({'success': False, 'error': str(e)})

@app.route('/api/ai-strategies/train', methods=['POST'])
@login_required
@require_vip_or_admin
def api_train_ai_strategy():
    """训练AI策略（实时训练） - 仅VIP和管理员可访问"""
    try:
        # 使用简化的AI策略服务避免数组歧义问题
        from services.ai_strategy_service_simple import AIStrategyService
        import time

        data = request.json

        # 准备训练配置
        training_config = {
            'ai_model': data.get('ai_model', 'AI策略训练'),
            'symbols': data.get('symbols', ['EURUSD']),
            'timeframe': data.get('timeframe', '1d'),
            'optimization_target': data.get('optimization_target', 'total_return'),
            'analysis_dimensions': data.get('analysis_dimensions', {}),
            'training_mode': data.get('training_mode', 'supervised'),
            'training_period': data.get('training_period', {}),
            'selected_data_source': data.get('selected_data_source', 'mt5_data')  # 添加数据源配置
        }

        print(f"🔧 训练配置: {training_config}")  # 调试日志

        # 创建简化的AI策略服务实例
        ai_service = AIStrategyService()

        # 创建AI策略记录
        strategy = ai_service.create_ai_strategy(current_user, training_config)
        if not strategy:
            return jsonify({
                'success': False,
                'error': '创建AI策略失败'
            })

        # 模拟训练过程（使用简化版本避免数组问题）
        training_result = ai_service.simulate_training_process(
            strategy['id'], training_config
        )

        if training_result['success']:
            return jsonify({
                'success': True,
                'training_results': training_result['training_results'],
                'performance_metrics': training_result['performance_metrics'],
                'message': 'AI策略训练完成'
            })
        else:
            return jsonify({
                'success': False,
                'error': training_result.get('error', '训练失败')
            })

    except Exception as e:
        return jsonify({'success': False, 'error': str(e)})

# 旧的AI策略列表API已移除，使用新的API: /api/ai-strategies/list

@app.route('/api/ai-strategies/active')
@login_required
def api_active_ai_strategies():
    """获取活跃的AI策略列表"""
    try:
        # 使用原生SQL查询避免SQLAlchemy缓存问题
        import sqlite3
        conn = sqlite3.connect('trading_system.db')
        cursor = conn.cursor()

        cursor.execute("""
            SELECT id, user_id, name, description, status, is_shared, ai_model,
                   performance_metrics, created_at
            FROM strategy
            WHERE user_id = ? AND strategy_type = ? AND is_active = 1 AND status = ?
            ORDER BY created_at DESC
        """, (current_user.id, 'ai', 'completed'))

        strategy_rows = cursor.fetchall()
        conn.close()

        strategy_list = []
        for row in strategy_rows:
            strategy_id, user_id, name, description, status, is_shared, ai_model = row[:7]
            performance_metrics_str = row[7]
            created_at = row[8]

            # 解析性能指标
            try:
                performance_metrics = json.loads(performance_metrics_str) if performance_metrics_str else {}
            except:
                performance_metrics = {}

            strategy_data = {
                'id': strategy_id,
                'name': name,
                'description': description,
                'ai_model': ai_model,
                'performance_metrics': performance_metrics
            }
            strategy_list.append(strategy_data)

        return jsonify({'success': True, 'strategies': strategy_list})

    except Exception as e:
        return jsonify({'success': False, 'error': str(e)})

@app.route('/api/ai-strategies/<int:strategy_id>')
@login_required
def api_get_ai_strategy(strategy_id):
    """获取AI策略详情"""
    try:
        # 使用原生SQL查询避免SQLAlchemy缓存问题
        import sqlite3
        conn = sqlite3.connect('trading_system.db')
        cursor = conn.cursor()

        cursor.execute("""
            SELECT id, user_id, name, description, status, is_shared, ai_model,
                   timeframe, symbols, performance_metrics, training_results,
                   training_data, created_at, updated_at
            FROM strategy
            WHERE id = ? AND strategy_type = ?
        """, (strategy_id, 'ai'))

        row = cursor.fetchone()
        conn.close()

        if row:
            strategy_id, user_id, name, description, status, is_shared, ai_model = row[:7]
            timeframe, symbols_str, performance_metrics_str, training_results_str = row[7:11]
            training_data_str, created_at, updated_at = row[11:14]

            # 解析JSON字段
            try:
                performance_metrics = json.loads(performance_metrics_str) if performance_metrics_str else {}
            except:
                performance_metrics = {}

            try:
                training_results = json.loads(training_results_str) if training_results_str else {}
            except:
                training_results = {}

            # 解析训练数据
            training_data = {}
            try:
                training_data = json.loads(training_data_str) if training_data_str else {}
            except:
                training_data = {}

            # 从训练数据中提取显示信息
            symbols_display = '未指定'
            data_source = 'MT5'
            time_span_display = '未指定'

            if training_data:
                # 获取交易品种
                if 'symbols_text' in training_data:
                    symbols_display = training_data['symbols_text']
                elif 'symbols' in training_data:
                    symbols = training_data['symbols']
                    if isinstance(symbols, list):
                        symbols_display = ', '.join(symbols)
                    elif isinstance(symbols, str):
                        symbols_display = symbols

                # 获取数据源
                data_source = training_data.get('data_source', 'MT5')

                # 获取时间跨度
                if 'training_period' in training_data:
                    period = training_data['training_period']
                    start_date = period.get('start')
                    end_date = period.get('end')
                    if start_date and end_date:
                        time_span_display = f"{start_date} 至 {end_date}"

            strategy_data = {
                'id': strategy_id,
                'user_id': user_id,
                'name': name,
                'description': description,
                'status': status,
                'is_shared': bool(is_shared),
                'ai_model': ai_model,
                'timeframe': timeframe,
                'symbols': symbols_str,
                'symbols_display': symbols_display,
                'data_source': data_source,
                'time_span_display': time_span_display,
                'performance_metrics': performance_metrics,
                'training_results': training_results,
                'training_data': training_data,
                'created_at': created_at,
                'updated_at': updated_at
            }

            return jsonify({'success': True, 'strategy': strategy_data})
        else:
            return jsonify({'success': False, 'error': '策略不存在'})

    except Exception as e:
        return jsonify({'success': False, 'error': str(e)})

@app.route('/api/ai-strategies/<int:strategy_id>/activate', methods=['POST'])
@login_required
def api_activate_ai_strategy(strategy_id):
    """激活AI策略"""
    try:
        from services.ai_strategy_service import ai_strategy_service
        success = ai_strategy_service.activate_strategy(strategy_id)

        if success:
            return jsonify({'success': True, 'message': '策略已激活'})
        else:
            return jsonify({'success': False, 'error': '激活失败'})

    except Exception as e:
        return jsonify({'success': False, 'error': str(e)})

@app.route('/api/ai-strategies/<int:strategy_id>/deactivate', methods=['POST'])
@login_required
def api_deactivate_ai_strategy(strategy_id):
    """停用AI策略"""
    try:
        from services.ai_strategy_service import ai_strategy_service
        success = ai_strategy_service.deactivate_strategy(strategy_id)

        if success:
            return jsonify({'success': True, 'message': '策略已停用'})
        else:
            return jsonify({'success': False, 'error': '停用失败'})

    except Exception as e:
        return jsonify({'success': False, 'error': str(e)})

@app.route('/api/ai-strategies/<int:strategy_id>', methods=['DELETE'])
@login_required
def api_delete_ai_strategy(strategy_id):
    """删除AI策略"""
    try:
        from services.ai_strategy_service import ai_strategy_service
        success = ai_strategy_service.delete_strategy(strategy_id)

        if success:
            return jsonify({'success': True, 'message': '策略已删除'})
        else:
            return jsonify({'success': False, 'error': '删除失败'})

    except Exception as e:
        return jsonify({'success': False, 'error': str(e)})

# 用户管理API
@app.route('/api/users', methods=['GET'])
@login_required
def api_get_users():
    """获取用户列表"""
    if current_user.user_type != 'admin':
        return jsonify({'success': False, 'error': '权限不足'})

    try:
        page = request.args.get('page', 1, type=int)
        per_page = 10
        search = request.args.get('search', '')
        user_type = request.args.get('user_type', '')
        approval_status = request.args.get('approval_status', '')

        query = User.query

        # 搜索过滤
        if search:
            query = query.filter(
                db.or_(
                    User.username.contains(search),
                    User.real_name.contains(search),
                    User.email.contains(search)
                )
            )

        # 用户类型过滤
        if user_type:
            query = query.filter(User.user_type == user_type)

        # 审核状态过滤
        if approval_status == 'approved':
            query = query.filter(User.is_approved == True)
        elif approval_status == 'pending':
            query = query.filter(User.is_approved == False)

        # 分页
        pagination = query.paginate(
            page=page, per_page=per_page, error_out=False
        )

        users = []
        for user in pagination.items:
            users.append({
                'id': user.id,
                'username': user.username,
                'email': user.email,
                'phone': user.phone,
                'real_name': user.real_name,
                'user_type': user.user_type,
                'is_approved': user.is_approved,
                'is_active': user.is_active,
                'created_at': user.created_at.isoformat()
            })

        return jsonify({
            'success': True,
            'users': users,
            'total_pages': pagination.pages,
            'current_page': page,
            'total_users': pagination.total
        })

    except Exception as e:
        return jsonify({'success': False, 'error': str(e)})

@app.route('/api/users/stats', methods=['GET'])
@login_required
def api_user_stats():
    """获取用户统计"""
    if current_user.user_type != 'admin':
        return jsonify({'success': False, 'error': '权限不足'})

    try:
        total = User.query.count()
        pending = User.query.filter(User.is_approved == False).count()
        approved = User.query.filter(User.is_approved == True).count()
        vip = User.query.filter(User.user_type == 'vip').count()

        return jsonify({
            'success': True,
            'stats': {
                'total': total,
                'pending': pending,
                'approved': approved,
                'vip': vip
            }
        })

    except Exception as e:
        return jsonify({'success': False, 'error': str(e)})

@app.route('/api/dashboard/today-pnl', methods=['GET'])
@login_required
def api_today_pnl():
    """获取今日盈亏统计"""
    try:
        from datetime import datetime, timedelta
        from services.mt5_service import mt5_service
        import pytz

        # 直接从MT5获取今日交易数据
        if not mt5_service.connected:
            print("⚠️ MT5未连接，无法获取今日盈亏")
            return jsonify({
                'success': True,
                'today_pnl': 0.0,
                'today_trades': 0,
                'closed_pnl': 0.0,
                'open_pnl': 0.0,
                'note': 'MT5未连接'
            })

        # 获取今日开始和结束时间（本地时间）
        today = datetime.now().date()
        start_time = datetime.combine(today, datetime.min.time())
        end_time = datetime.now()

        print(f"📊 获取今日盈亏: {start_time.strftime('%Y-%m-%d %H:%M:%S')} 到 {end_time.strftime('%Y-%m-%d %H:%M:%S')}")

        # 从MT5获取今日交易历史
        today_deals = mt5_service.get_trade_history(start_time, end_time)

        if today_deals is None:
            today_deals = []

        # 计算今日已平仓盈亏
        closed_pnl = 0.0
        today_trades_count = 0

        # 按position_id分组计算完整交易的盈亏
        position_profits = {}

        for deal in today_deals:
            position_id = deal.get('position_id')
            profit = deal.get('profit', 0)
            swap = deal.get('swap', 0)
            commission = deal.get('commission', 0)

            # 累计每个持仓的总盈亏（包括利润、库存费、手续费）
            if position_id not in position_profits:
                position_profits[position_id] = {
                    'profit': 0,
                    'swap': 0,
                    'commission': 0,
                    'symbol': deal.get('symbol'),
                    'deals': []
                }

            position_profits[position_id]['profit'] += profit
            position_profits[position_id]['swap'] += swap
            position_profits[position_id]['commission'] += commission
            position_profits[position_id]['deals'].append(deal)

        # 计算总的已平仓盈亏
        for position_id, data in position_profits.items():
            # 检查是否为完整的交易（有入场和出场）
            has_entry = any(deal.get('entry') == 0 for deal in data['deals'])
            has_exit = any(deal.get('entry') == 1 for deal in data['deals'])

            if has_exit:  # 只要有出场就算作已平仓交易
                total_position_pnl = data['profit'] + data['swap'] + data['commission']
                closed_pnl += total_position_pnl
                today_trades_count += 1

                print(f"📈 今日平仓交易: {data['symbol']} (持仓ID: {position_id})")
                print(f"   利润: ${data['profit']:.2f}, 库存费: ${data['swap']:.2f}, 手续费: ${data['commission']:.2f}")
                print(f"   总盈亏: ${total_position_pnl:.2f}")

        # 获取当前持仓的浮动盈亏（只计算今日开仓的持仓）
        open_pnl = 0.0
        today_open_positions = 0

        try:
            import MetaTrader5 as mt5
            positions = mt5.positions_get()
            if positions:
                for position in positions:
                    # 检查持仓是否是今日开仓的
                    position_time = datetime.fromtimestamp(position.time)
                    if position_time.date() == today:
                        # 计算包括库存费在内的总浮动盈亏
                        position_pnl = position.profit + position.swap
                        open_pnl += position_pnl
                        today_open_positions += 1

                        print(f"📊 今日持仓: {position.symbol} (票号: {position.ticket})")
                        print(f"   开仓时间: {position_time.strftime('%Y-%m-%d %H:%M:%S')}")
                        print(f"   浮动盈亏: ${position.profit:.2f}, 库存费: ${position.swap:.2f}")
                        print(f"   总盈亏: ${position_pnl:.2f}")

            print(f"📊 今日持仓统计: {today_open_positions} 个持仓，总浮动盈亏: ${open_pnl:.2f}")

        except Exception as e:
            print(f"❌ 获取持仓盈亏失败: {e}")

        total_pnl = closed_pnl + open_pnl

        print(f"💰 今日盈亏统计:")
        print(f"   已平仓盈亏: ${closed_pnl:.2f} (来自 {today_trades_count} 笔已平仓交易)")
        print(f"   持仓浮动盈亏: ${open_pnl:.2f} (来自 {today_open_positions} 个今日持仓)")
        print(f"   总今日盈亏: ${total_pnl:.2f}")

        return jsonify({
            'success': True,
            'today_pnl': round(total_pnl, 2),
            'today_trades': today_trades_count,
            'closed_pnl': round(closed_pnl, 2),
            'open_pnl': round(open_pnl, 2),
            'open_positions_count': today_open_positions,
            'data_source': 'MT5_REAL_TIME'
        })

    except Exception as e:
        print(f"❌ 获取今日盈亏失败: {e}")
        return jsonify({'success': False, 'error': str(e)})

@app.route('/api/dashboard/account-info', methods=['GET'])
@login_required
def api_dashboard_account_info():
    """获取账户信息用于仪表盘更新"""
    try:
        from services.mt5_service import mt5_service

        # 获取用户的真实账户
        real_accounts = TradingAccount.query.filter_by(
            user_id=current_user.id,
            account_type='real',
            is_active=True
        ).all()

        total_balance = 0
        total_equity = 0
        mt5_connected = False

        # 获取MT5账户信息
        mt5_account_info = mt5_service.get_account_info()
        if mt5_account_info and mt5_account_info.get('success'):
            account_data = mt5_account_info.get('account_info', {})
            total_balance = account_data.get('balance', 0)
            total_equity = account_data.get('equity', 0)
            mt5_connected = True
            print(f"📊 实时MT5账户信息: 余额=${total_balance}, 净值=${total_equity}")
        else:
            # 如果MT5未连接，使用数据库中的账户信息
            for account in real_accounts:
                total_balance += account.balance
                total_equity += account.equity
            print(f"📊 数据库账户信息: 余额=${total_balance}, 净值=${total_equity}")

        return jsonify({
            'success': True,
            'total_balance': total_balance,
            'total_equity': total_equity,
            'mt5_connected': mt5_connected,
            'account_count': len(real_accounts),
            'update_time': datetime.now().strftime('%H:%M:%S')
        })

    except Exception as e:
        print(f"❌ 获取账户信息失败: {e}")
        return jsonify({'success': False, 'error': str(e)})

@app.route('/api/dashboard/market-overview', methods=['GET'])
@login_required
def api_market_overview():
    """获取市场概览数据"""
    try:
        from services.mt5_service import mt5_service

        # 定义要获取的交易品种
        symbols = ['XAUUSD', 'EURUSD', 'GBPUSD', 'USDJPY']
        market_data = []

        for symbol in symbols:
            try:
                # 获取当前价格
                tick = mt5_service.get_symbol_tick(symbol)
                if tick:
                    # 计算简单的涨跌幅（这里使用模拟数据，实际应该基于历史数据）
                    import random
                    change_percent = random.uniform(-2.0, 2.0)  # 模拟涨跌幅

                    # 格式化价格显示
                    if symbol == 'XAUUSD':
                        price_str = f"${tick['bid']:.2f}"
                        display_name = "XAU/USD"
                        icon = "fas fa-coins text-warning"
                    elif symbol in ['EURUSD', 'GBPUSD']:
                        price_str = f"{tick['bid']:.4f}"
                        display_name = f"{symbol[:3]}/{symbol[3:]}"
                        icon = ""
                    elif symbol == 'USDJPY':
                        price_str = f"{tick['bid']:.2f}"
                        display_name = "USD/JPY"
                        icon = ""
                    else:
                        price_str = f"{tick['bid']:.4f}"
                        display_name = symbol
                        icon = ""

                    market_data.append({
                        'symbol': symbol,
                        'display_name': display_name,
                        'icon': icon,
                        'price': price_str,
                        'change_percent': change_percent,
                        'is_positive': change_percent >= 0
                    })
                else:
                    # 如果无法获取实时数据，使用默认数据
                    default_data = {
                        'XAUUSD': {'price': '$2,634.78', 'change': 1.45},
                        'EURUSD': {'price': '1.0856', 'change': -0.45},
                        'GBPUSD': {'price': '1.2734', 'change': 0.12},
                        'USDJPY': {'price': '149.85', 'change': 0.23}
                    }

                    if symbol in default_data:
                        data = default_data[symbol]
                        display_name = "XAU/USD" if symbol == 'XAUUSD' else f"{symbol[:3]}/{symbol[3:]}"
                        icon = "fas fa-coins text-warning" if symbol == 'XAUUSD' else ""

                        market_data.append({
                            'symbol': symbol,
                            'display_name': display_name,
                            'icon': icon,
                            'price': data['price'],
                            'change_percent': data['change'],
                            'is_positive': data['change'] >= 0
                        })

            except Exception as e:
                print(f"获取{symbol}数据失败: {e}")
                continue

        # 添加加密货币数据（模拟）
        crypto_data = [
            {'symbol': 'BTCUSD', 'display_name': 'BTC/USD', 'icon': '', 'price': '$45,234.56', 'change_percent': 2.34, 'is_positive': True},
            {'symbol': 'ETHUSD', 'display_name': 'ETH/USD', 'icon': '', 'price': '$3,234.78', 'change_percent': 1.23, 'is_positive': True}
        ]

        # 将加密货币数据添加到市场数据中（在黄金之后）
        if market_data:
            market_data[1:1] = crypto_data  # 在索引1处插入
        else:
            market_data = crypto_data

        return jsonify({
            'success': True,
            'market_data': market_data,
            'update_time': datetime.now().strftime('%H:%M:%S')
        })

    except Exception as e:
        print(f"获取市场概览失败: {e}")
        return jsonify({'success': False, 'error': str(e)})

@app.route('/api/trading/query', methods=['GET'])
@login_required
def api_trading_query():
    """交易查询API - 整合本地数据库和MT5数据"""
    try:
        from datetime import datetime, timedelta
        import pytz

        # 获取查询参数
        start_date = request.args.get('start_date')
        end_date = request.args.get('end_date')
        symbol = request.args.get('symbol')
        direction = request.args.get('direction')
        account_type = request.args.get('account_type')
        profit_status = request.args.get('profit_status')
        min_amount = request.args.get('min_amount')
        max_amount = request.args.get('max_amount')
        include_mt5 = request.args.get('include_mt5', 'true').lower() == 'true'  # 是否包含MT5数据

        print(f"🔍 交易查询参数: start_date={start_date}, end_date={end_date}, symbol={symbol}, direction={direction}, include_mt5={include_mt5}")

        # 构建查询条件
        query = db.session.query(Trade, TradingAccount).join(
            TradingAccount, Trade.account_id == TradingAccount.id
        ).filter(TradingAccount.user_id == current_user.id)

        # 时间范围过滤 - 确保始终应用日期过滤
        china_tz = pytz.timezone('Asia/Shanghai')

        # 如果没有提供日期参数，默认使用最近30天
        if not start_date and not end_date:
            end_datetime = datetime.now()
            start_datetime = end_datetime - timedelta(days=30)
            print(f"📅 未提供日期参数，使用默认最近30天: {start_datetime.date()} 到 {end_datetime.date()}")
        else:
            # 处理开始日期
            if start_date:
                start_datetime = datetime.strptime(start_date, '%Y-%m-%d')
                print(f"📅 开始日期: {start_datetime.date()}")
            else:
                # 如果只有结束日期，开始日期默认为结束日期前30天
                end_datetime = datetime.strptime(end_date, '%Y-%m-%d') if end_date else datetime.now()
                start_datetime = end_datetime - timedelta(days=30)
                print(f"📅 未提供开始日期，默认为结束日期前30天: {start_datetime.date()}")

            # 处理结束日期
            if end_date:
                end_datetime = datetime.strptime(end_date, '%Y-%m-%d') + timedelta(days=1)
                print(f"📅 结束日期: {(end_datetime - timedelta(days=1)).date()}")
            else:
                # 如果只有开始日期，结束日期默认为今天
                end_datetime = datetime.now()
                print(f"📅 未提供结束日期，默认为今天: {end_datetime.date()}")

        # 转换为UTC时间进行数据库查询
        start_datetime_utc = china_tz.localize(start_datetime).astimezone(pytz.UTC).replace(tzinfo=None)
        end_datetime_utc = china_tz.localize(end_datetime).astimezone(pytz.UTC).replace(tzinfo=None)

        # 应用时间过滤
        query = query.filter(Trade.open_time >= start_datetime_utc)
        query = query.filter(Trade.open_time < end_datetime_utc)

        print(f"📅 数据库查询时间范围 (UTC): {start_datetime_utc} 到 {end_datetime_utc}")

        # 货币对过滤
        if symbol:
            query = query.filter(Trade.symbol == symbol)

        # 交易方向过滤
        if direction:
            query = query.filter(Trade.trade_type == direction)

        # 账户类型过滤
        if account_type:
            query = query.filter(TradingAccount.account_type == account_type)

        # 交易金额过滤
        if min_amount:
            query = query.filter(Trade.volume >= float(min_amount))

        if max_amount:
            query = query.filter(Trade.volume <= float(max_amount))

        # 盈亏状态过滤
        if profit_status:
            if profit_status == 'profit':
                query = query.filter(Trade.profit > 0)
            elif profit_status == 'loss':
                query = query.filter(Trade.profit < 0)
            elif profit_status == 'breakeven':
                query = query.filter(Trade.profit == 0)

        # 执行查询
        results = query.order_by(Trade.open_time.desc()).all()

        # 格式化交易数据
        trades = []
        for trade, account in results:
            # 转换为中国时区
            china_tz = pytz.timezone('Asia/Shanghai')
            open_time_china = pytz.UTC.localize(trade.open_time).astimezone(china_tz)
            close_time_china = None
            if trade.close_time:
                close_time_china = pytz.UTC.localize(trade.close_time).astimezone(china_tz)

            trades.append({
                'id': trade.id,
                'account_name': account.account_name,
                'account_type': account.account_type,
                'symbol': trade.symbol,
                'direction': trade.trade_type,  # 使用trade_type字段
                'volume': float(trade.volume),
                'open_price': float(trade.open_price),
                'close_price': float(trade.close_price) if trade.close_price else None,
                'profit': float(trade.profit) if trade.profit else 0.0,
                'open_time': open_time_china.isoformat(),
                'close_time': close_time_china.isoformat() if close_time_china else None,
                'status': trade.status,  # 使用数据库中的status字段
                'source': 'database'  # 标记数据来源
            })

        print(f"📊 本地数据库查询到 {len(trades)} 条记录")

        # 如果启用MT5数据查询，获取MT5交易历史
        if include_mt5:
            try:
                from services.mt5_service import mt5_service

                # 设置MT5查询的时间范围 - 使用与数据库查询相同的时间范围
                # 重用前面计算的时间范围
                if not start_date and not end_date:
                    mt5_end_date = datetime.now()
                    mt5_start_date = mt5_end_date - timedelta(days=30)
                else:
                    if start_date:
                        mt5_start_date = datetime.strptime(start_date, '%Y-%m-%d')
                    else:
                        end_dt = datetime.strptime(end_date, '%Y-%m-%d') if end_date else datetime.now()
                        mt5_start_date = end_dt - timedelta(days=30)

                    if end_date:
                        mt5_end_date = datetime.strptime(end_date, '%Y-%m-%d') + timedelta(days=1)
                    else:
                        mt5_end_date = datetime.now()

                print(f"📅 MT5查询时间范围: {mt5_start_date.date()} 到 {mt5_end_date.date()}")

                mt5_trades = mt5_service.get_trade_history(mt5_start_date, mt5_end_date)

                if mt5_trades:
                    print(f"📊 MT5查询到 {len(mt5_trades)} 条记录")

                    # 处理MT5交易数据
                    for mt5_trade in mt5_trades:
                        # 应用过滤条件
                        if symbol and mt5_trade['symbol'] != symbol:
                            continue

                        if direction:
                            mt5_direction = 'buy' if mt5_trade['type'] == 0 else 'sell'
                            if mt5_direction != direction:
                                continue

                        if min_amount and mt5_trade['volume'] < float(min_amount):
                            continue

                        if max_amount and mt5_trade['volume'] > float(max_amount):
                            continue

                        if profit_status:
                            if profit_status == 'profit' and mt5_trade['profit'] <= 0:
                                continue
                            elif profit_status == 'loss' and mt5_trade['profit'] >= 0:
                                continue
                            elif profit_status == 'breakeven' and mt5_trade['profit'] != 0:
                                continue

                        # 转换MT5数据格式
                        trade_time = datetime.fromtimestamp(mt5_trade['time'])
                        trade_time_china = pytz.UTC.localize(trade_time).astimezone(china_tz)

                        trades.append({
                            'id': f"mt5_{mt5_trade['ticket']}",
                            'account_name': 'MT5账户',
                            'account_type': 'mt5',
                            'symbol': mt5_trade['symbol'],
                            'direction': 'buy' if mt5_trade['type'] == 0 else 'sell',
                            'volume': float(mt5_trade['volume']),
                            'open_price': float(mt5_trade['price']),
                            'close_price': None,  # MT5历史数据中可能没有平仓价
                            'profit': float(mt5_trade['profit']),
                            'open_time': trade_time_china.isoformat(),
                            'close_time': None,
                            'status': 'closed' if mt5_trade['entry'] == 1 else 'open',
                            'source': 'mt5'  # 标记数据来源
                        })

            except Exception as e:
                print(f"⚠️ 获取MT5数据失败: {e}")
                # 继续使用本地数据库数据

        # 按时间排序所有交易记录
        trades.sort(key=lambda x: x['open_time'], reverse=True)

        print(f"📊 总共查询到 {len(trades)} 条记录")

        # 计算统计信息
        statistics = calculate_trade_statistics(trades)

        return jsonify({
            'success': True,
            'trades': trades,
            'statistics': statistics,
            'total_count': len(trades)
        })

    except Exception as e:
        print(f"❌ 交易查询失败: {e}")
        return jsonify({'success': False, 'error': str(e)})

@app.route('/api/trading/export', methods=['GET'])
@login_required
def api_trading_export():
    """导出交易数据"""
    try:
        import csv
        import io
        from flask import make_response

        # 重用查询逻辑
        query_response = api_trading_query()
        query_data = query_response.get_json()

        if not query_data['success']:
            return jsonify({'success': False, 'error': '查询数据失败'})

        trades = query_data['trades']

        # 创建CSV内容
        output = io.StringIO()
        writer = csv.writer(output)

        # 写入表头
        writer.writerow([
            '账户名称', '交易品种', '交易方向', '交易量',
            '开仓价格', '平仓价格', '盈亏', '开仓时间', '平仓时间', '状态'
        ])

        # 写入数据
        for trade in trades:
            writer.writerow([
                trade['account_name'],
                trade['symbol'],
                '买入' if trade['direction'] == 'buy' else '卖出',
                trade['volume'],
                trade['open_price'],
                trade['close_price'] or '-',
                trade['profit'],
                trade['open_time'],
                trade['close_time'] or '-',
                '已平仓' if trade['status'] == 'closed' else '持仓中'
            ])

        # 创建响应
        response = make_response(output.getvalue())
        response.headers['Content-Type'] = 'text/csv; charset=utf-8'
        response.headers['Content-Disposition'] = f'attachment; filename=trading_history_{datetime.now().strftime("%Y%m%d")}.csv'

        return response

    except Exception as e:
        print(f"❌ 导出交易数据失败: {e}")
        return jsonify({'success': False, 'error': str(e)})

def calculate_trade_statistics(trades):
    """计算交易统计信息"""
    if not trades:
        return {
            'total_trades': 0,
            'total_profit': 0.0,
            'win_rate': 0.0,
            'avg_profit': 0.0,
            'max_profit': 0.0,
            'max_loss': 0.0
        }

    total_trades = len(trades)
    total_profit = sum(trade['profit'] for trade in trades)
    profitable_trades = [trade for trade in trades if trade['profit'] > 0]
    win_count = len(profitable_trades)
    win_rate = (win_count / total_trades) * 100 if total_trades > 0 else 0
    avg_profit = total_profit / total_trades if total_trades > 0 else 0

    profits = [trade['profit'] for trade in trades]
    max_profit = max(profits) if profits else 0
    max_loss = min(profits) if profits else 0

    return {
        'total_trades': total_trades,
        'total_profit': total_profit,
        'win_rate': win_rate,
        'avg_profit': avg_profit,
        'max_profit': max_profit,
        'max_loss': max_loss
    }

@app.route('/api/trading/create-test-data', methods=['POST'])
@login_required
def create_test_trading_data():
    """创建测试交易数据（仅用于测试）"""
    try:
        from datetime import datetime, timedelta
        import random

        # 检查是否已有测试账户
        test_account = TradingAccount.query.filter_by(
            user_id=current_user.id,
            account_name='测试账户'
        ).first()

        if not test_account:
            # 创建测试账户
            test_account = TradingAccount(
                user_id=current_user.id,
                account_name='测试账户',
                account_type='demo',
                broker='测试',
                balance=10000.0,
                is_active=True
            )
            db.session.add(test_account)
            db.session.commit()

        # 创建一些测试交易记录
        symbols = ['EURUSD', 'GBPUSD', 'USDJPY', 'XAUUSD', 'BTCUSD']
        trade_types = ['buy', 'sell']

        for i in range(20):  # 创建20条测试记录
            # 随机时间（最近30天内）
            days_ago = random.randint(0, 30)
            hours_ago = random.randint(0, 23)
            open_time = datetime.utcnow() - timedelta(days=days_ago, hours=hours_ago)

            symbol = random.choice(symbols)
            trade_type = random.choice(trade_types)
            volume = round(random.uniform(0.01, 0.5), 2)

            # 根据货币对设置价格范围
            if symbol == 'EURUSD':
                open_price = round(random.uniform(1.05, 1.12), 5)
            elif symbol == 'GBPUSD':
                open_price = round(random.uniform(1.20, 1.30), 5)
            elif symbol == 'USDJPY':
                open_price = round(random.uniform(140, 155), 3)
            elif symbol == 'XAUUSD':
                open_price = round(random.uniform(2600, 2700), 2)
            elif symbol == 'BTCUSD':
                open_price = round(random.uniform(40000, 50000), 2)

            # 随机决定是否已平仓
            is_closed = random.choice([True, False])
            close_price = None
            close_time = None
            profit = 0.0
            status = 'open'

            if is_closed:
                # 生成平仓价格和盈亏
                price_change = random.uniform(-0.02, 0.02)  # ±2%的价格变化
                close_price = round(open_price * (1 + price_change), 5)
                close_time = open_time + timedelta(hours=random.randint(1, 48))

                # 计算盈亏（简化计算）
                if trade_type == 'buy':
                    profit = (close_price - open_price) * volume * 100000  # 假设标准手
                else:
                    profit = (open_price - close_price) * volume * 100000

                profit = round(profit, 2)
                status = 'closed'

            trade = Trade(
                account_id=test_account.id,
                symbol=symbol,
                trade_type=trade_type,
                volume=volume,
                open_price=open_price,
                close_price=close_price,
                profit=profit,
                status=status,
                open_time=open_time,
                close_time=close_time,
                strategy_name='测试策略'
            )

            db.session.add(trade)

        db.session.commit()

        return jsonify({
            'success': True,
            'message': '测试数据创建成功',
            'account_id': test_account.id
        })

    except Exception as e:
        print(f"❌ 创建测试数据失败: {e}")
        return jsonify({'success': False, 'error': str(e)})

@app.route('/api/trading/clear-test-data', methods=['POST'])
@login_required
def clear_test_trading_data():
    """清除测试交易数据"""
    try:
        # 查找测试账户
        test_account = TradingAccount.query.filter_by(
            user_id=current_user.id,
            account_name='测试账户'
        ).first()

        if not test_account:
            return jsonify({
                'success': True,
                'message': '没有找到测试账户，无需清除',
                'deleted_trades': 0
            })

        # 删除测试账户下的所有交易记录
        deleted_trades = Trade.query.filter_by(account_id=test_account.id).delete()

        # 删除测试账户
        db.session.delete(test_account)

        db.session.commit()

        print(f"✅ 已清除测试数据: 删除了 {deleted_trades} 条交易记录和测试账户")

        return jsonify({
            'success': True,
            'message': f'测试数据清除成功，删除了 {deleted_trades} 条交易记录',
            'deleted_trades': deleted_trades
        })

    except Exception as e:
        print(f"❌ 清除测试数据失败: {e}")
        db.session.rollback()
        return jsonify({'success': False, 'error': str(e)})

@app.route('/api/dashboard/round-pnl', methods=['GET'])
@login_required
def api_round_pnl():
    """获取本轮次AI交易盈亏统计"""
    try:
        from datetime import datetime, timedelta
        import pytz

        # 获取用户的真实交易账户
        real_accounts = TradingAccount.query.filter_by(
            user_id=current_user.id,
            account_type='real',
            is_active=True
        ).all()

        if not real_accounts:
            return jsonify({
                'success': True,
                'round_pnl': 0.0,
                'round_trades': 0,
                'round_start_time': None,
                'round_status': 'inactive'
            })

        account_ids = [acc.id for acc in real_accounts]

        # 查找今日的AI交易轮次
        # 只统计当天的AI交易数据
        today = datetime.now().date()
        start_time = datetime.combine(today, datetime.min.time())
        end_time = datetime.combine(today, datetime.max.time())

        ai_trades = Trade.query.filter(
            Trade.account_id.in_(account_ids),
            Trade.strategy_name.like('%AI%'),
            Trade.open_time >= start_time,
            Trade.open_time <= end_time
        ).order_by(Trade.open_time.desc()).all()

        if not ai_trades:
            return jsonify({
                'success': True,
                'round_pnl': 0.0,
                'round_trades': 0,
                'round_start_time': None,
                'round_status': 'inactive',
                'note': '今日无AI交易'
            })

        # 简化轮次逻辑：当天的所有AI交易都算作一轮
        round_trades = ai_trades
        round_status = 'active' if any(trade.status == 'open' for trade in ai_trades) else 'completed'

        # 计算本轮次盈亏
        round_pnl = 0.0
        round_start_time = None

        if round_trades:
            round_start_time = round_trades[-1].open_time.isoformat()

            for trade in round_trades:
                if trade.status == 'closed' and trade.profit is not None:
                    round_pnl += trade.profit
                elif trade.status == 'open':
                    # 对于持仓交易，尝试获取实时盈亏
                    try:
                        from services.mt5_service import mt5_service
                        if trade.mt5_ticket and mt5_service.connected:
                            import MetaTrader5 as mt5
                            positions = mt5.positions_get(ticket=trade.mt5_ticket)
                            if positions:
                                position = positions[0]
                                round_pnl += position.profit
                            elif trade.profit is not None:
                                round_pnl += trade.profit
                        elif trade.profit is not None:
                            round_pnl += trade.profit
                    except Exception as e:
                        print(f"获取持仓盈亏失败: {e}")
                        if trade.profit is not None:
                            round_pnl += trade.profit

        return jsonify({
            'success': True,
            'round_pnl': round(round_pnl, 2),
            'round_trades': len(round_trades),
            'round_start_time': round_start_time,
            'round_status': round_status
        })

    except Exception as e:
        print(f"获取本轮次盈亏失败: {e}")
        return jsonify({'success': False, 'error': str(e)})

@app.route('/api/users', methods=['POST'])
@login_required
def api_add_user():
    """添加新用户"""
    if current_user.user_type != 'admin':
        return jsonify({'success': False, 'error': '权限不足'})

    try:
        data = request.json

        # 检查用户名是否已存在
        if User.query.filter_by(username=data['username']).first():
            return jsonify({'success': False, 'error': '用户名已存在'})

        # 检查邮箱是否已存在
        if User.query.filter_by(email=data['email']).first():
            return jsonify({'success': False, 'error': '邮箱已存在'})

        # 生成密码salt
        import secrets
        salt = secrets.token_hex(16)

        # 创建新用户
        user = User(
            username=data['username'],
            email=data['email'],
            phone=data.get('phone'),
            real_name=data.get('real_name'),
            password_hash=generate_password_hash(data['password'] + salt),
            password_salt=salt,
            user_type=data.get('user_type', 'normal'),
            is_approved=data.get('auto_approve', False),
            approved_by=current_user.id if data.get('auto_approve', False) else None,
            approved_at=datetime.utcnow() if data.get('auto_approve', False) else None
        )

        db.session.add(user)
        db.session.commit()

        return jsonify({'success': True, 'message': '用户添加成功'})

    except Exception as e:
        db.session.rollback()
        return jsonify({'success': False, 'error': str(e)})

@app.route('/api/users/<int:user_id>', methods=['PUT'])
@login_required
def api_update_user(user_id):
    """更新用户信息"""
    if current_user.user_type != 'admin':
        return jsonify({'success': False, 'error': '权限不足'})

    try:
        user = User.query.get(user_id)
        if not user:
            return jsonify({'success': False, 'error': '用户不存在'})

        data = request.json

        # 更新用户信息
        user.email = data.get('email', user.email)
        user.phone = data.get('phone', user.phone)
        user.real_name = data.get('real_name', user.real_name)
        user.user_type = data.get('user_type', user.user_type)

        # 处理审核状态变更
        new_approved = data.get('is_approved', user.is_approved)
        if new_approved != user.is_approved:
            user.is_approved = new_approved
            if new_approved:
                user.approved_by = current_user.id
                user.approved_at = datetime.utcnow()
            else:
                user.approved_by = None
                user.approved_at = None

        user.is_active = data.get('is_active', user.is_active)

        db.session.commit()

        return jsonify({'success': True, 'message': '用户信息更新成功'})

    except Exception as e:
        db.session.rollback()
        return jsonify({'success': False, 'error': str(e)})

@app.route('/api/users/<int:user_id>/approve', methods=['POST'])
@login_required
def api_approve_user(user_id):
    """审核通过用户"""
    if current_user.user_type != 'admin':
        return jsonify({'success': False, 'error': '权限不足'})

    try:
        user = User.query.get(user_id)
        if not user:
            return jsonify({'success': False, 'error': '用户不存在'})

        user.is_approved = True
        user.approved_by = current_user.id
        user.approved_at = datetime.utcnow()

        db.session.commit()

        return jsonify({'success': True, 'message': '用户审核通过'})

    except Exception as e:
        db.session.rollback()
        return jsonify({'success': False, 'error': str(e)})

@app.route('/api/users/<int:user_id>', methods=['DELETE'])
@login_required
def api_delete_user(user_id):
    """删除用户"""
    if current_user.user_type != 'admin':
        return jsonify({'success': False, 'error': '权限不足'})

    try:
        user = User.query.get(user_id)
        if not user:
            return jsonify({'success': False, 'error': '用户不存在'})

        # 不能删除自己
        if user.id == current_user.id:
            return jsonify({'success': False, 'error': '不能删除自己的账户'})

        db.session.delete(user)
        db.session.commit()

        return jsonify({'success': True, 'message': '用户删除成功'})

    except Exception as e:
        db.session.rollback()
        return jsonify({'success': False, 'error': str(e)})

# 系统设置API
@app.route('/api/system-settings/registration', methods=['GET'])
@login_required
def api_get_registration_settings():
    """获取注册设置"""
    if current_user.user_type != 'admin':
        return jsonify({'success': False, 'error': '权限不足'})

    try:
        from models import SystemSettings

        enable_registration = SystemSettings.get_setting('enable_user_registration', True)
        require_approval = SystemSettings.get_setting('require_user_approval', True)

        return jsonify({
            'success': True,
            'settings': {
                'enable_registration': enable_registration,
                'require_approval': require_approval
            }
        })

    except Exception as e:
        return jsonify({'success': False, 'error': str(e)})

@app.route('/api/system-settings/registration', methods=['POST'])
@login_required
def api_save_registration_settings():
    """保存注册设置"""
    if current_user.user_type != 'admin':
        return jsonify({'success': False, 'error': '权限不足'})

    try:
        from models import SystemSettings
        data = request.json

        SystemSettings.set_setting(
            'enable_user_registration',
            data.get('enable_registration', True),
            'boolean',
            '是否开启用户注册功能'
        )

        SystemSettings.set_setting(
            'require_user_approval',
            data.get('require_approval', True),
            'boolean',
            '新用户是否需要管理员审核'
        )

        return jsonify({'success': True, 'message': '设置保存成功'})

    except Exception as e:
        return jsonify({'success': False, 'error': str(e)})

@app.route('/api/system-settings/mt5', methods=['GET'])
@login_required
def api_get_mt5_settings():
    """获取MT5设置"""
    if current_user.user_type != 'admin':
        return jsonify({'success': False, 'error': '权限不足'})

    try:
        from models import SystemSettings

        mt5_demo_as_real = SystemSettings.get_setting('mt5_demo_as_real', True)
        enable_mt5_auto_connect = SystemSettings.get_setting('enable_mt5_auto_connect', True)
        enable_mt5_order_sync = SystemSettings.get_setting('enable_mt5_order_sync', True)

        return jsonify({
            'success': True,
            'settings': {
                'mt5_demo_as_real': mt5_demo_as_real,
                'enable_mt5_auto_connect': enable_mt5_auto_connect,
                'enable_mt5_order_sync': enable_mt5_order_sync
            }
        })

    except Exception as e:
        return jsonify({'success': False, 'error': str(e)})

@app.route('/api/system-settings/mt5', methods=['POST'])
@login_required
def api_save_mt5_settings():
    """保存MT5设置"""
    if current_user.user_type != 'admin':
        return jsonify({'success': False, 'error': '权限不足'})

    try:
        from models import SystemSettings
        data = request.json

        SystemSettings.set_setting(
            'mt5_demo_as_real',
            data.get('mt5_demo_as_real', True),
            'boolean',
            'MT5的Demo账户是否作为真实账户识别/处理'
        )

        SystemSettings.set_setting(
            'enable_mt5_auto_connect',
            data.get('enable_mt5_auto_connect', True),
            'boolean',
            '是否启用MT5自动连接'
        )

        SystemSettings.set_setting(
            'enable_mt5_order_sync',
            data.get('enable_mt5_order_sync', True),
            'boolean',
            '是否启用MT5订单同步'
        )

        return jsonify({'success': True, 'message': 'MT5设置保存成功'})

    except Exception as e:
        return jsonify({'success': False, 'error': str(e)})

@app.route('/api/mt5/connection-status', methods=['GET'])
@login_required
def api_mt5_connection_status_check():
    """获取MT5连接状态和重连信息"""
    try:
        from services.mt5_service import mt5_service
        import MetaTrader5 as mt5

        # 检查基本连接状态
        is_connected = mt5_service.connected
        error_message = None
        account_info = None

        if is_connected:
            # 验证连接质量
            account_info = mt5.account_info()
            if account_info is None:
                is_connected = False
                error_message = "MT5账户信息获取失败"
            else:
                # 测试数据获取能力
                test_rates = mt5.copy_rates_from_pos('XAUUSD', mt5.TIMEFRAME_H1, 0, 1)
                if test_rates is None or len(test_rates) == 0:
                    is_connected = False
                    error_message = "MT5数据获取失败"
        else:
            error_message = "MT5未连接"

        # 返回兼容的格式
        return jsonify({
            'success': True,
            'connected': is_connected,
            'error': error_message,
            'status': {
                'connected': is_connected,
                'error': error_message
            },
            'account_info': {
                'login': account_info.login if is_connected and account_info else None,
                'server': account_info.server if is_connected and account_info else None,
                'balance': account_info.balance if is_connected and account_info else None
            } if is_connected else None
        })

    except Exception as e:
        return jsonify({
            'success': False,
            'connected': False,
            'error': f'连接检查异常: {str(e)}'
        })

@app.route('/api/mt5/force-reconnect', methods=['POST'])
@login_required
def api_mt5_force_reconnect():
    """强制MT5重连"""
    try:
        from services.mt5_service import mt5_service

        mt5_service.force_reconnect()

        return jsonify({
            'success': True,
            'message': '已触发强制重连'
        })

    except Exception as e:
        return jsonify({'success': False, 'error': str(e)})

@app.route('/api/mt5/auto-reconnect', methods=['POST'])
@login_required
def api_mt5_auto_reconnect():
    """设置自动重连开关"""
    try:
        from services.mt5_service import mt5_service
        data = request.json

        enabled = data.get('enabled', True)
        mt5_service.set_auto_reconnect(enabled)

        return jsonify({
            'success': True,
            'message': f'自动重连已{"启用" if enabled else "禁用"}'
        })

    except Exception as e:
        return jsonify({'success': False, 'error': str(e)})

# 智能体交易相关API
@app.route('/api/ai-models/list')
@login_required
def api_ai_models_list():
    """获取系统配置的AI模型列表"""
    try:
        # 首先尝试从数据库获取用户配置的AI模型
        user_ai_models = AIModelConfig.query.all()  # 获取所有配置的AI模型

        models = []

        # 添加用户配置的AI模型
        for model in user_ai_models:
            models.append({
                'id': model.model_name,
                'name': model.name,
                'provider': getattr(model, 'provider', 'Custom'),
                'status': 'active' if model.is_active else 'inactive',
                'api_key_configured': bool(model.api_key),
                'source': 'user_config'
            })

        # 添加系统默认的5个AI模型
        default_models = [
            {
                'id': 'deepseek_v3',
                'name': 'DeepSeek V3',
                'provider': 'DeepSeek',
                'status': 'active',
                'api_key_configured': True,
                'source': 'system_default'
            },
            {
                'id': 'openai_gpt4',
                'name': 'OpenAI GPT-4',
                'provider': 'OpenAI',
                'status': 'active',
                'api_key_configured': True,
                'source': 'system_default'
            },
            {
                'id': 'claude_3',
                'name': 'Claude 3',
                'provider': 'Anthropic',
                'status': 'active',
                'api_key_configured': True,
                'source': 'system_default'
            },
            {
                'id': 'qwen_max',
                'name': '通义千问 Max',
                'provider': 'Alibaba',
                'status': 'active',
                'api_key_configured': True,
                'source': 'system_default'
            },
            {
                'id': 'gemini_pro',
                'name': 'Gemini Pro',
                'provider': 'Google',
                'status': 'active',
                'api_key_configured': True,
                'source': 'system_default'
            }
        ]

        # 检查是否已存在相同ID的模型，避免重复
        existing_ids = {model['id'] for model in models}
        for default_model in default_models:
            if default_model['id'] not in existing_ids:
                models.append(default_model)

        # 如果没有配置的模型，返回默认选项
        if not models:
            models = [
                {
                    'id': 'deepseek_v3',
                    'name': 'DeepSeek V3',
                    'provider': 'DeepSeek',
                    'status': 'inactive',
                    'api_key_configured': False
                },
                {
                    'id': 'openai_gpt4',
                    'name': 'OpenAI GPT-4',
                    'provider': 'OpenAI',
                    'status': 'inactive',
                    'api_key_configured': False
                },
                {
                    'id': 'claude_3',
                    'name': 'Claude 3',
                    'provider': 'Anthropic',
                    'status': 'inactive',
                    'api_key_configured': False
                }
            ]

        return jsonify({
            'success': True,
            'models': models
        })
    except Exception as e:
        return jsonify({
            'success': False,
            'error': str(e)
        })

@app.route('/api/ai-strategies/trained')
@login_required
def api_ai_strategies_trained():
    """获取已训练完成的AI策略列表"""
    try:
        # 使用原生SQL获取用户已训练完成的AI策略
        import sqlite3
        conn = sqlite3.connect('trading_system.db')
        cursor = conn.cursor()

        cursor.execute("""
            SELECT id, user_id, name, description, status, is_shared, ai_model,
                   timeframe, symbols, performance_metrics, training_results,
                   training_data, created_at, updated_at
            FROM strategy
            WHERE user_id = ? AND strategy_type = ? AND status = ?
            ORDER BY created_at DESC
        """, (current_user.id, 'ai', 'completed'))

        strategy_rows = cursor.fetchall()
        conn.close()

        strategy_list = []
        for row in strategy_rows:
            # 解析数据
            strategy_id, user_id, name, description, status, is_shared, ai_model = row[:7]
            timeframe, symbols_str, performance_metrics_str, training_results_str = row[7:11]
            training_data_str, created_at, updated_at = row[11:14]

            # 解析JSON字段
            import json
            try:
                performance_metrics = json.loads(performance_metrics_str) if performance_metrics_str else {}
            except:
                performance_metrics = {}

            try:
                training_data = json.loads(training_data_str) if training_data_str else {}
                symbols = training_data.get('symbols', [])
            except:
                symbols = []

            training_config = {}

            strategy_data = {
                'id': strategy_id,
                'name': name,
                'status': status,
                'description': description,
                'training_date': created_at[:10] if created_at else '',
                'ai_model': ai_model,
                'accuracy': performance_metrics.get('win_rate', 0) if performance_metrics else 0,
                'symbols': symbols,
                'is_active': bool(is_shared),  # 临时使用is_shared字段
                # 新增训练详情
                'training_details': {
                    'analysis_dimensions': training_config.get('analysis_dimensions', [
                        '技术指标分析', '价格趋势分析', '成交量分析', '市场情绪分析'
                    ]),
                    'time_frame': training_config.get('time_frame', 'M15'),
                    'data_period': training_config.get('data_period', '30天'),
                    'training_samples': training_config.get('training_samples', 1000),
                    'strategy_type': training_config.get('strategy_type', '趋势跟踪'),
                    'risk_level': training_config.get('risk_level', '中等'),
                    'market_conditions': training_config.get('market_conditions', ['趋势市场', '震荡市场']),
                    'performance_summary': {
                        'win_rate': performance_metrics.get('win_rate', 0) if performance_metrics else 0,
                        'avg_profit': performance_metrics.get('avg_profit', 0) if performance_metrics else 0,
                        'max_drawdown': performance_metrics.get('max_drawdown', 0) if performance_metrics else 0,
                        'sharpe_ratio': performance_metrics.get('sharpe_ratio', 0) if performance_metrics else 0
                    }
                }
            }
            strategy_list.append(strategy_data)

        # 如果没有训练完成的策略，提供一些示例策略
        if not strategy_list:
            strategy_list = [
                {
                    'id': 'demo_1',
                    'name': '演示策略：趋势跟踪',
                    'status': 'completed',
                    'description': '基于移动平均线的趋势跟踪策略（演示）',
                    'training_date': '2024-01-15',
                    'ai_model': 'deepseek_v3',
                    'accuracy': 0.75,
                    'symbols': ['EURUSD', 'GBPUSD'],
                    'is_active': False
                },
                {
                    'id': 'demo_2',
                    'name': '演示策略：突破交易',
                    'status': 'completed',
                    'description': '基于价格突破的交易策略（演示）',
                    'training_date': '2024-01-10',
                    'ai_model': 'gpt4',
                    'accuracy': 0.68,
                    'symbols': ['USDJPY', 'AUDUSD'],
                    'is_active': False
                }
            ]

        return jsonify({
            'success': True,
            'strategies': strategy_list,
            'total': len(strategy_list),
            'message': f'找到 {len(strategy_list)} 个已训练的AI策略'
        })
    except Exception as e:
        return jsonify({
            'success': False,
            'error': str(e)
        })

# 模拟交易数据清空API
@app.route('/api/demo-trading/clear-positions', methods=['POST'])
@login_required
def api_demo_clear_positions():
    """清空模拟交易持仓"""
    try:
        data = request.get_json()
        account_id = data.get('account_id')

        print(f"收到清空持仓请求，账户ID: {account_id}")

        if not account_id:
            return jsonify({
                'success': False,
                'error': '缺少账户ID'
            })

        # 实际清空持仓的逻辑
        cleared_count = 0

        try:
            # 获取用户的模拟账户
            accounts = TradingAccount.query.filter_by(
                user_id=current_user.id,
                account_type='demo',
                is_active=True
            ).all()

            if accounts:
                account_ids = [acc.id for acc in accounts]

                # 清空数据库中的开放持仓（将状态为'open'的交易设为'closed'）
                open_trades = Trade.query.filter(
                    Trade.account_id.in_(account_ids),
                    Trade.status == 'open'
                ).all()

                cleared_count = len(open_trades)

                # 强制平仓所有开放交易
                for trade in open_trades:
                    trade.status = 'closed'
                    trade.close_time = datetime.utcnow()
                    trade.close_price = trade.open_price  # 以开仓价平仓（无盈亏）
                    trade.profit = 0.0

                db.session.commit()
                print(f"强制平仓了 {cleared_count} 个持仓")

            # 同时清空session中的持仓数据
            if 'demo_positions' in session:
                session['demo_positions'] = []
                session.modified = True
                print(f"同时清空了session中的持仓数据")

        except Exception as e:
            print(f"清空持仓时出错: {e}")
            db.session.rollback()
            cleared_count = 0

        return jsonify({
            'success': True,
            'cleared_count': cleared_count,
            'message': f'成功清空 {cleared_count} 个持仓'
        })

    except Exception as e:
        print(f"清空持仓API异常: {e}")
        return jsonify({
            'success': False,
            'error': str(e)
        })

@app.route('/api/demo-trading/clear-history', methods=['POST'])
@login_required
def api_demo_clear_history():
    """清空模拟交易历史"""
    try:
        data = request.get_json()
        account_id = data.get('account_id')

        print(f"收到清空历史请求，账户ID: {account_id}")

        if not account_id:
            return jsonify({
                'success': False,
                'error': '缺少账户ID'
            })

        # 实际清空交易历史的逻辑
        cleared_count = 0

        try:
            # 获取用户的模拟账户
            accounts = TradingAccount.query.filter_by(
                user_id=current_user.id,
                account_type='demo',
                is_active=True
            ).all()

            if accounts:
                account_ids = [acc.id for acc in accounts]

                # 清空数据库中的交易历史
                trades_to_delete = Trade.query.filter(
                    Trade.account_id.in_(account_ids)
                ).all()

                cleared_count = len(trades_to_delete)

                # 删除交易记录
                for trade in trades_to_delete:
                    db.session.delete(trade)

                db.session.commit()
                print(f"从数据库中清空了 {cleared_count} 条交易历史记录")

            # 同时清空session中的数据
            if 'demo_trade_history' in session:
                session['demo_trade_history'] = []
                session.modified = True
                print(f"同时清空了session中的历史记录")

        except Exception as e:
            print(f"清空历史记录时出错: {e}")
            db.session.rollback()
            cleared_count = 0

        return jsonify({
            'success': True,
            'cleared_count': cleared_count,
            'message': f'成功清空 {cleared_count} 条交易历史'
        })

    except Exception as e:
        print(f"清空历史API异常: {e}")
        return jsonify({
            'success': False,
            'error': str(e)
        })

@app.route('/api/demo-trading/clear-all', methods=['POST'])
@login_required
def api_demo_clear_all():
    """清空所有模拟交易数据"""
    try:
        data = request.get_json()
        account_id = data.get('account_id')

        print(f"收到清空所有数据请求，账户ID: {account_id}")

        if not account_id:
            return jsonify({
                'success': False,
                'error': '缺少账户ID'
            })

        # 实际清空所有数据的逻辑
        positions_cleared = 0
        history_cleared = 0

        try:
            # 获取用户的模拟账户
            accounts = TradingAccount.query.filter_by(
                user_id=current_user.id,
                account_type='demo',
                is_active=True
            ).all()

            if accounts:
                account_ids = [acc.id for acc in accounts]

                # 1. 强制平仓所有开放持仓
                open_trades = Trade.query.filter(
                    Trade.account_id.in_(account_ids),
                    Trade.status == 'open'
                ).all()

                positions_cleared = len(open_trades)

                for trade in open_trades:
                    trade.status = 'closed'
                    trade.close_time = datetime.utcnow()
                    trade.close_price = trade.open_price
                    trade.profit = 0.0

                # 2. 删除所有交易历史
                all_trades = Trade.query.filter(
                    Trade.account_id.in_(account_ids)
                ).all()

                history_cleared = len(all_trades)

                for trade in all_trades:
                    db.session.delete(trade)

                # 3. 重置账户余额
                for account in accounts:
                    account.balance = 100000.0  # 重置为10万美元
                    account.equity = 100000.0

                db.session.commit()
                print(f"数据库操作完成：平仓 {positions_cleared} 个，删除 {history_cleared} 条历史")

            # 同时清空session中的数据
            session['demo_positions'] = []
            session['demo_trade_history'] = []
            session['demo_balance'] = 100000.0
            session.modified = True

            print(f"重置账户到初始状态完成")

        except Exception as e:
            print(f"清空所有数据时出错: {e}")
            db.session.rollback()
            positions_cleared = 0
            history_cleared = 0

        return jsonify({
            'success': True,
            'positions_cleared': positions_cleared,
            'history_cleared': history_cleared,
            'message': f'成功清空所有数据：{positions_cleared} 个持仓，{history_cleared} 条历史记录'
        })

    except Exception as e:
        print(f"清空所有数据API异常: {e}")
        return jsonify({
            'success': False,
            'error': str(e)
        })

# 获取单个智能体交易策略API
@app.route('/api/agent-trading/strategies/<int:strategy_id>', methods=['GET'])
@login_required
def api_get_agent_strategy(strategy_id):
    """获取单个智能体交易策略详情"""
    try:
        from models import AgentTradingStrategy

        # 查找策略
        strategy = AgentTradingStrategy.query.filter_by(
            id=strategy_id,
            user_id=current_user.id
        ).first()

        if not strategy:
            return jsonify({
                'success': False,
                'error': '策略不存在或无权限访问'
            })

        return jsonify({
            'success': True,
            'strategy': {
                'id': strategy.id,
                'name': strategy.name,
                'description': strategy.description,
                'user_strategy': strategy.user_strategy,
                'risk_tolerance': strategy.risk_tolerance,
                'max_daily_trades': strategy.max_daily_trades,
                'max_position_size': strategy.max_position_size,
                'trading_symbols': strategy.trading_symbols,
                'trading_hours': strategy.trading_hours,
                'stop_loss_percent': strategy.stop_loss_percent,
                'take_profit_percent': strategy.take_profit_percent,
                'dynamic_stop_loss': strategy.dynamic_stop_loss,
                'is_active': strategy.is_active,
                'created_at': strategy.created_at.isoformat() if strategy.created_at else None
            }
        })

    except Exception as e:
        print(f"获取策略详情失败: {e}")
        return jsonify({
            'success': False,
            'error': '获取策略详情失败，请重试'
        })

# 修改智能体交易策略止盈止损设置API
@app.route('/api/agent-trading/strategies/<int:strategy_id>/stop-loss', methods=['PUT'])
@login_required
def api_update_agent_strategy_stop_loss(strategy_id):
    """修改智能体交易策略的止盈止损设置"""
    try:
        from models import AgentTradingStrategy

        # 查找策略
        strategy = AgentTradingStrategy.query.filter_by(
            id=strategy_id,
            user_id=current_user.id
        ).first()

        if not strategy:
            return jsonify({
                'success': False,
                'error': '策略不存在或无权限修改'
            })

        data = request.json

        # 验证参数
        stop_loss_percent = data.get('stop_loss_percent')
        take_profit_percent = data.get('take_profit_percent')
        dynamic_stop_loss = data.get('dynamic_stop_loss', False)

        if stop_loss_percent is not None:
            if not (0.5 <= stop_loss_percent <= 10):
                return jsonify({
                    'success': False,
                    'error': '止损比例必须在0.5%-10%之间'
                })
            strategy.stop_loss_percent = stop_loss_percent

        if take_profit_percent is not None:
            if not (1 <= take_profit_percent <= 20):
                return jsonify({
                    'success': False,
                    'error': '止盈比例必须在1%-20%之间'
                })
            strategy.take_profit_percent = take_profit_percent

        strategy.dynamic_stop_loss = dynamic_stop_loss

        # 保存更改
        db.session.commit()

        print(f"用户 {current_user.username} 更新了策略 {strategy.name} 的止盈止损设置")

        return jsonify({
            'success': True,
            'message': '止盈止损设置更新成功',
            'strategy': {
                'stop_loss_percent': strategy.stop_loss_percent,
                'take_profit_percent': strategy.take_profit_percent,
                'dynamic_stop_loss': strategy.dynamic_stop_loss
            }
        })

    except Exception as e:
        print(f"更新止盈止损设置失败: {e}")
        return jsonify({
            'success': False,
            'error': '更新设置失败，请重试'
        })

# 删除智能体交易策略API
@app.route('/api/agent-trading/strategies/<int:strategy_id>', methods=['DELETE'])
@login_required
def api_delete_agent_strategy(strategy_id):
    """删除智能体交易策略"""
    try:
        from models import AgentTradingStrategy

        # 查找策略
        strategy = AgentTradingStrategy.query.filter_by(
            id=strategy_id,
            user_id=current_user.id
        ).first()

        if not strategy:
            return jsonify({
                'success': False,
                'error': '策略不存在或无权限删除'
            })

        # 检查策略是否正在运行
        if strategy.is_active:
            return jsonify({
                'success': False,
                'error': '无法删除正在运行的策略，请先停止交易'
            })

        # 删除策略
        strategy_name = strategy.name
        db.session.delete(strategy)
        db.session.commit()

        print(f"用户 {current_user.username} 删除了策略: {strategy_name}")

        return jsonify({
            'success': True,
            'message': f'策略 "{strategy_name}" 删除成功'
        })

    except Exception as e:
        print(f"删除策略异常: {e}")
        db.session.rollback()
        return jsonify({
            'success': False,
            'error': str(e)
        })

# 获取MT5账户列表API
@app.route('/api/mt5/accounts', methods=['GET'])
@login_required
def api_get_mt5_accounts():
    """获取MT5账户列表"""
    try:
        from services.mt5_service import mt5_service

        print("🔍 正在获取MT5账户信息...")

        # 获取MT5账户信息
        account_info = mt5_service.get_account_info()

        if account_info:
            print(f"✅ 成功获取MT5账户: {account_info.get('login')} - {account_info.get('server')}")

            # 返回账户列表（这里简化为单个账户）
            accounts = [{
                'login': account_info.get('login'),
                'server': account_info.get('server'),
                'balance': account_info.get('balance', 0),
                'equity': account_info.get('equity', 0),
                'currency': account_info.get('currency', 'USD'),
                'company': account_info.get('company', ''),
                'name': account_info.get('name', ''),
                'account_type': account_info.get('account_type', 'demo')
            }]

            return jsonify({
                'success': True,
                'accounts': accounts,
                'message': f'找到 {len(accounts)} 个MT5账户'
            })
        else:
            print("❌ 无法获取MT5账户信息")
            return jsonify({
                'success': False,
                'error': 'MT5未连接或无法获取账户信息。请确保：\n1. MT5客户端已启动\n2. 已登录到交易账户\n3. 允许自动交易和DLL导入'
            })

    except ImportError as e:
        print(f"❌ MT5库导入失败: {e}")
        return jsonify({
            'success': False,
            'error': 'MetaTrader5库未安装。请运行: pip install MetaTrader5'
        })
    except Exception as e:
        print(f"❌ 获取MT5账户异常: {e}")
        return jsonify({
            'success': False,
            'error': f'获取MT5账户失败: {str(e)}'
        })

# ==================== AI策略分享管理API ====================

@app.route('/api/ai-strategies/<int:strategy_id>/share', methods=['POST'])
@login_required
@require_admin
def api_share_ai_strategy(strategy_id):
    """管理员分享AI策略给所有用户"""
    try:
        # 使用原生SQL查询策略（避免缓存问题）
        import sqlite3
        conn = sqlite3.connect('trading_system.db')
        cursor = conn.cursor()
        cursor.execute("SELECT * FROM strategy WHERE id = ?", (strategy_id,))
        strategy_row = cursor.fetchone()

        if not strategy_row:
            conn.close()
            return jsonify({'success': False, 'error': '策略不存在'})

        # 检查策略状态（假设status在第11列，索引为10）
        strategy_status = strategy_row[10] if len(strategy_row) > 10 else 'completed'
        if strategy_status != 'completed':
            conn.close()
            return jsonify({'success': False, 'error': '只能分享已完成训练的策略'})

        # 更新策略分享状态
        from datetime import datetime
        cursor.execute("""
            UPDATE strategy
            SET is_shared = 1, shared_by = ?, shared_at = ?
            WHERE id = ?
        """, (current_user.id, datetime.utcnow(), strategy_id))

        conn.commit()

        # 获取策略名称用于返回消息
        strategy_name = strategy_row[2] if len(strategy_row) > 2 else '未知策略'
        conn.close()

        return jsonify({
            'success': True,
            'message': f'策略 "{strategy_name}" 已成功分享给所有用户'
        })

    except Exception as e:
        db.session.rollback()
        return jsonify({'success': False, 'error': str(e)})

@app.route('/api/ai-strategies/<int:strategy_id>/unshare', methods=['POST'])
@login_required
@require_admin
def api_unshare_ai_strategy(strategy_id):
    """管理员取消分享AI策略"""
    try:
        # 使用原生SQL查询和更新策略
        import sqlite3
        conn = sqlite3.connect('trading_system.db')
        cursor = conn.cursor()
        cursor.execute("SELECT * FROM strategy WHERE id = ?", (strategy_id,))
        strategy_row = cursor.fetchone()

        if not strategy_row:
            conn.close()
            return jsonify({'success': False, 'error': '策略不存在'})

        # 取消分享
        cursor.execute("""
            UPDATE strategy
            SET is_shared = 0, shared_by = NULL, shared_at = NULL
            WHERE id = ?
        """, (strategy_id,))

        conn.commit()

        # 获取策略名称用于返回消息
        strategy_name = strategy_row[2] if len(strategy_row) > 2 else '未知策略'
        conn.close()

        return jsonify({
            'success': True,
            'message': f'策略 "{strategy_name}" 已取消分享'
        })

    except Exception as e:
        db.session.rollback()
        return jsonify({'success': False, 'error': str(e)})

# ==================== 形态监测相关路由 ====================

# 初始化形态识别器和测试数据生成器
pattern_recognizer = PatternRecognizer()
test_data_generator = PatternTestDataGenerator()

@app.route('/pattern-monitoring')
@login_required
def pattern_monitoring():
    """形态监测页面"""
    return render_template('pattern_monitoring.html')

@app.route('/pattern-auto-trading')
@login_required
def pattern_auto_trading():
    """形态监测自动交易页面"""
    return render_template('pattern_auto_trading.html')

@app.route('/low-risk-trading')
@login_required
def low_risk_trading():
    """低风险交易页面"""
    return render_template('low_risk_trading.html')

@app.route('/strategy-trading')
@login_required
def strategy_trading():
    """策略交易页面"""
    return render_template('strategy_trading.html')

@app.route('/callback-trading')
@login_required
def callback_trading():
    """回调交易页面"""
    return render_template('callback_trading.html')

@app.route('/strategy-backtest')
@login_required
def ai_strategy_backtest():
    """AI策略回测页面"""
    return render_template('strategy_backtest.html')

# ==================== AI策略API ====================

@app.route('/api/ai-strategies/list', methods=['GET'])
@login_required
def api_get_ai_strategies():
    """获取AI策略列表"""
    try:
        from services.ai_strategy_loader import ai_strategy_loader
        strategies = ai_strategy_loader.get_available_strategies()

        return jsonify({
            'success': True,
            'strategies': strategies
        })

    except Exception as e:
        logger.error(f"获取AI策略列表失败: {e}")
        return jsonify({'success': False, 'error': str(e)}), 500

@app.route('/api/ai-strategies/<strategy_id>/status', methods=['GET'])
@login_required
def api_ai_strategy_status(strategy_id):
    """获取AI策略模型加载状态"""
    try:
        from services.ai_strategy_loader import ai_strategy_loader

        print(f"🔍 检查策略模型状态: {strategy_id}")

        # 尝试加载策略
        strategy = ai_strategy_loader.load_strategy_model(strategy_id)

        if strategy:
            status = {
                'loaded': True,
                'name': strategy.get('name', strategy_id),
                'type': strategy.get('type', 'unknown'),
                'parameters': strategy.get('parameters', {}),
                'model_available': strategy.get('model') is not None,
                'last_updated': strategy.get('created_at', 'unknown'),
                'description': strategy.get('description', ''),
                'error': None
            }

            print(f"✅ 策略模型加载成功: {strategy['name']}")

            return jsonify({
                'success': True,
                'status': status
            })
        else:
            print(f"❌ 策略模型加载失败: {strategy_id}")

            return jsonify({
                'success': True,
                'status': {
                    'loaded': False,
                    'name': strategy_id,
                    'error': '策略模型不存在或加载失败'
                }
            })

    except Exception as e:
        print(f"❌ 检查策略状态异常: {e}")
        return jsonify({
            'success': False,
            'error': f'检查策略状态失败: {str(e)}'
        })

@app.route('/api/ai-strategies/<strategy_id>/backtest', methods=['POST'])
@login_required
def api_run_strategy_backtest(strategy_id):
    """运行策略回测"""
    try:
        data = request.get_json()

        # 获取回测参数
        symbol = data.get('symbol', 'XAUUSD')
        timeframe = data.get('timeframe', '1h')
        days = data.get('days', 30)
        initial_balance = data.get('initial_balance', 10000.0)
        lot_size = data.get('lot_size', 0.01)
        custom_config = data.get('custom_config')  # 获取自定义配置
        allow_auto_connect = data.get('allow_auto_connect', False)  # 获取连接控制参数

        print(f"📊 回测参数: symbol={symbol}, timeframe={timeframe}, days={days}")
        print(f"💰 资金参数: initial_balance={initial_balance}, lot_size={lot_size}")
        print(f"🔌 MT5连接: allow_auto_connect={allow_auto_connect}")
        if custom_config:
            print(f"⚙️ 使用自定义配置: {custom_config}")
        else:
            print(f"🤖 使用自动配置")

        # 运行回测
        from services.backtest_service import backtest_service
        result = backtest_service.run_backtest(
            strategy_id, symbol, timeframe, days, initial_balance, lot_size,
            custom_config, allow_auto_connect
        )

        return jsonify(result)

    except Exception as e:
        logger.error(f"运行策略回测失败: {e}")
        return jsonify({'success': False, 'error': str(e)}), 500

# ==================== 策略交易API ====================

@app.route('/api/strategy-trading/start', methods=['POST'])
@login_required
def api_start_strategy_trading():
    """启动策略交易"""
    try:
        data = request.get_json()

        from services.strategy_trading_service import strategy_trading_service
        result = strategy_trading_service.start_trading(data)

        return jsonify(result)

    except Exception as e:
        logger.error(f"启动策略交易失败: {e}")
        return jsonify({'success': False, 'error': str(e)}), 500

@app.route('/api/strategy-trading/stop', methods=['POST'])
@login_required
def api_stop_strategy_trading():
    """停止策略交易"""
    try:
        from services.strategy_trading_service import strategy_trading_service
        result = strategy_trading_service.stop_trading()

        return jsonify(result)

    except Exception as e:
        logger.error(f"停止策略交易失败: {e}")
        return jsonify({'success': False, 'error': str(e)}), 500

@app.route('/api/strategy-trading/status', methods=['GET'])
@login_required
def api_get_strategy_trading_status():
    """获取策略交易状态"""
    try:
        from services.strategy_trading_service import strategy_trading_service
        result = strategy_trading_service.get_trading_status()

        return jsonify(result)

    except Exception as e:
        logger.error(f"获取策略交易状态失败: {e}")
        return jsonify({'success': False, 'error': str(e)}), 500

@app.route('/api/strategy-trading/stats', methods=['GET'])
@login_required
def api_get_strategy_trading_stats():
    """获取策略交易统计"""
    try:
        from services.strategy_trading_service import strategy_trading_service
        result = strategy_trading_service.get_trading_stats()

        return jsonify(result)

    except Exception as e:
        logger.error(f"获取策略交易统计失败: {e}")
        return jsonify({'success': False, 'error': str(e)}), 500

@app.route('/api/strategy-trading/positions', methods=['GET'])
@login_required
def api_get_strategy_trading_positions():
    """获取策略交易持仓"""
    try:
        # 获取策略交易相关的持仓
        # 这里可以复用低风险交易的持仓获取逻辑，但过滤策略交易的持仓

        from services.mt5_service import mt5_service

        if not mt5_service.connected:
            return jsonify({'success': False, 'error': 'MT5未连接'})

        import MetaTrader5 as mt5

        # 获取所有持仓
        positions = mt5.positions_get()
        if positions is None:
            return jsonify({'success': True, 'positions': []})

        strategy_positions = []

        for pos in positions:
            # 过滤策略交易的持仓（通过comment识别）
            if pos.comment and ('AI_' in pos.comment or 'Strategy_' in pos.comment):
                # 计算盈亏
                if pos.type == mt5.POSITION_TYPE_BUY:
                    pnl = (pos.price_current - pos.price_open) * pos.volume * 100
                else:
                    pnl = (pos.price_open - pos.price_current) * pos.volume * 100

                # 提取策略名称
                strategy_name = 'AI策略'
                if 'AI_' in pos.comment:
                    parts = pos.comment.split('_')
                    if len(parts) >= 2:
                        strategy_name = parts[1]

                strategy_positions.append({
                    'ticket': pos.ticket,
                    'symbol': pos.symbol,
                    'type': 'buy' if pos.type == mt5.POSITION_TYPE_BUY else 'sell',
                    'volume': pos.volume,
                    'entry_price': pos.price_open,
                    'current_price': pos.price_current,
                    'pnl': round(pnl, 2),
                    'strategy_name': strategy_name,
                    'comment': pos.comment
                })

        return jsonify({'success': True, 'positions': strategy_positions})

    except Exception as e:
        logger.error(f"获取策略交易持仓失败: {e}")
        return jsonify({'success': False, 'error': str(e)}), 500

@app.route('/api/strategy-trading/close-position', methods=['POST'])
@login_required
def api_close_strategy_position():
    """平仓策略交易持仓"""
    try:
        data = request.get_json()
        ticket = data.get('ticket')

        if not ticket:
            return jsonify({'success': False, 'error': '缺少持仓票据号'})

        from services.mt5_service import mt5_service

        if not mt5_service.connected:
            return jsonify({'success': False, 'error': 'MT5未连接'})

        # 平仓
        result = mt5_service.close_position(ticket)

        if result['success']:
            return jsonify({'success': True, 'message': '持仓已平仓'})
        else:
            return jsonify({'success': False, 'error': result['error']})

    except Exception as e:
        logger.error(f"平仓策略交易持仓失败: {e}")
        return jsonify({'success': False, 'error': str(e)}), 500

@app.route('/api/strategy-trading/close-all-positions', methods=['POST'])
@login_required
def api_close_all_strategy_positions():
    """一键平仓所有策略交易持仓"""
    try:
        from services.mt5_service import mt5_service

        if not mt5_service.connected:
            return jsonify({'success': False, 'error': 'MT5未连接'})

        import MetaTrader5 as mt5

        # 获取所有策略交易持仓
        positions = mt5.positions_get()
        if positions is None:
            return jsonify({'success': True, 'closedCount': 0})

        closed_count = 0
        errors = []

        for pos in positions:
            # 只平仓策略交易的持仓
            if pos.comment and ('AI_' in pos.comment or 'Strategy_' in pos.comment):
                result = mt5_service.close_position(pos.ticket)
                if result['success']:
                    closed_count += 1
                else:
                    errors.append(f"持仓{pos.ticket}: {result['error']}")

        if errors:
            return jsonify({
                'success': False,
                'error': f'部分平仓失败: {"; ".join(errors)}',
                'closedCount': closed_count
            })
        else:
            return jsonify({'success': True, 'closedCount': closed_count})

    except Exception as e:
        logger.error(f"一键平仓失败: {e}")
        return jsonify({'success': False, 'error': str(e)}), 500

@app.route('/api/strategy-analysis', methods=['POST'])
@login_required
def api_strategy_analysis():
    """获取策略分析结果 - 用于入场信息显示"""
    try:
        data = request.get_json()
        strategy_id = data.get('strategy_id')
        symbol = data.get('symbol', 'XAUUSD')
        account_type = data.get('account_type', 'real')

        if not strategy_id:
            return jsonify({'success': False, 'error': '策略ID不能为空'})

        logger.info(f"🔍 获取策略分析结果: 策略={strategy_id}, 品种={symbol}")

        # 获取AI策略加载器
        from services.ai_strategy_loader import ai_strategy_loader

        # 获取策略信息
        strategy = ai_strategy_loader.load_strategy_model(str(strategy_id))
        if not strategy:
            return jsonify({'success': False, 'error': '策略不存在或未完成训练'})

        # 获取市场数据
        from services.mt5_service import mt5_service
        if not mt5_service.connected:
            return jsonify({'success': False, 'error': 'MT5未连接，无法获取市场数据'})

        import MetaTrader5 as mt5
        import pandas as pd

        # 获取最新市场数据
        rates = mt5.copy_rates_from_pos(symbol, mt5.TIMEFRAME_H1, 0, 100)
        if rates is None or len(rates) == 0:
            return jsonify({'success': False, 'error': f'无法获取{symbol}的市场数据'})

        # 转换为DataFrame
        rates_df = pd.DataFrame(rates)
        rates_df['time'] = pd.to_datetime(rates_df['time'], unit='s')

        # 生成交易信号
        signal = ai_strategy_loader.predict_signal(str(strategy_id), rates_df)

        # 计算市场数据
        current_price = rates_df.iloc[-1]['close']
        prev_price = rates_df.iloc[-2]['close'] if len(rates_df) > 1 else current_price
        price_change = current_price - prev_price
        price_change_percent = (price_change / prev_price * 100) if prev_price != 0 else 0

        # 计算波动率（简单方法：最近20个周期的价格标准差）
        recent_prices = rates_df.tail(20)['close']
        volatility = recent_prices.std() / recent_prices.mean() if len(recent_prices) > 1 else 0

        # 判断市场趋势
        sma_short = rates_df.tail(10)['close'].mean()
        sma_long = rates_df.tail(20)['close'].mean()
        if sma_short > sma_long:
            trend = '上涨'
        elif sma_short < sma_long:
            trend = '下跌'
        else:
            trend = '震荡'

        # 构建响应数据
        response_data = {
            'success': True,
            'signals': [signal] if signal else [],
            'strategy_status': {
                'name': strategy['name'],
                'confidence': signal['confidence'] if signal else 0,
                'trend': trend,
                'last_analysis': datetime.now().isoformat()
            },
            'market_data': {
                'price': current_price,
                'change': price_change,
                'changePercent': price_change_percent,
                'volatility': volatility,
                'symbol': symbol
            }
        }

        logger.info(f"✅ 策略分析完成: 信号={'有' if signal else '无'}, 趋势={trend}")
        return jsonify(response_data)

    except Exception as e:
        logger.error(f"❌ 策略分析失败: {e}")
        import traceback
        logger.error(f"详细错误: {traceback.format_exc()}")
        return jsonify({'success': False, 'error': str(e)}), 500

# 支撑线时机页面已删除

# ==================== 回调交易API ====================

@app.route('/api/callback-trading/start', methods=['POST'])
@login_required
def api_start_callback_trading():
    """启动回调交易"""
    try:
        data = request.get_json()

        from services.callback_trading_service import callback_trading_service
        result = callback_trading_service.start_trading(data)

        return jsonify(result)

    except Exception as e:
        logger.error(f"启动回调交易失败: {e}")
        return jsonify({'success': False, 'error': str(e)}), 500

@app.route('/api/callback-trading/stop', methods=['POST'])
@login_required
def api_stop_callback_trading():
    """停止回调交易"""
    try:
        from services.callback_trading_service import callback_trading_service
        result = callback_trading_service.stop_trading()

        return jsonify(result)

    except Exception as e:
        logger.error(f"停止回调交易失败: {e}")
        return jsonify({'success': False, 'error': str(e)}), 500

@app.route('/api/callback-trading/status', methods=['GET'])
@login_required
def api_get_callback_trading_status():
    """获取回调交易状态"""
    try:
        from services.callback_trading_service import callback_trading_service
        result = callback_trading_service.get_status()

        return jsonify(result)

    except Exception as e:
        logger.error(f"获取回调交易状态失败: {e}")
        return jsonify({'success': False, 'error': str(e)}), 500

@app.route('/api/callback-trading/history', methods=['GET'])
@login_required
def api_get_callback_trading_history():
    """获取回调交易历史"""
    try:
        from services.callback_trading_service import callback_trading_service
        result = callback_trading_service.get_trade_history()

        return jsonify(result)

    except Exception as e:
        logger.error(f"获取回调交易历史失败: {e}")
        return jsonify({'success': False, 'error': str(e)}), 500

@app.route('/api/callback-trading/backtest', methods=['POST'])
@login_required
def api_callback_trading_backtest():
    """回调交易回测"""
    try:
        data = request.get_json()

        # 这里应该实现回测逻辑
        # 为了简化，返回模拟结果
        import random

        # 模拟回测结果
        total_trades = random.randint(50, 200)
        winning_trades = int(total_trades * random.uniform(0.4, 0.7))
        losing_trades = total_trades - winning_trades
        win_rate = (winning_trades / total_trades * 100) if total_trades > 0 else 0
        total_profit = random.uniform(-500, 1000)
        max_drawdown = random.uniform(5, 25)

        result = {
            'success': True,
            'results': {
                'total_trades': total_trades,
                'winning_trades': winning_trades,
                'losing_trades': losing_trades,
                'win_rate': win_rate,
                'total_profit': total_profit,
                'max_drawdown': max_drawdown
            }
        }

        return jsonify(result)

    except Exception as e:
        logger.error(f"回调交易回测失败: {e}")
        return jsonify({'success': False, 'error': str(e)}), 500

@app.route('/api/pattern-auto-trading/signals', methods=['GET'])
@login_required
def api_pattern_auto_trading_signals():
    """获取形态监测信号用于自动交易"""
    try:
        symbol = request.args.get('symbol', 'XAUUSD')
        timeframes = request.args.getlist('timeframes') or ['15m']
        confidence_threshold = float(request.args.get('confidence', 0.7))

        # 这里应该调用形态监测服务获取实时信号
        # 暂时返回模拟数据
        from datetime import datetime
        import random

        patterns = ['双底', '头肩顶', '上升三角形', '下降楔形', '矩形整理', '旗形', '三角收敛']
        signals = []

        for timeframe in timeframes:
            if random.random() > 0.7:  # 30%概率有信号
                is_bullish = random.random() > 0.5
                confidence = random.uniform(0.5, 0.95)

                if confidence >= confidence_threshold:
                    signals.append({
                        'id': f"{symbol}_{timeframe}_{int(datetime.now().timestamp())}",
                        'symbol': symbol,
                        'timeframe': timeframe,
                        'pattern_name': random.choice(patterns),
                        'pattern_type': 'bullish' if is_bullish else 'bearish',
                        'confidence': round(confidence, 3),
                        'price': round(2650 + random.uniform(-10, 10), 2),
                        'timestamp': datetime.now().isoformat(),
                        'entry_price': round(2650 + random.uniform(-5, 5), 2),
                        'stop_loss': round(2650 + random.uniform(-20, -10) if is_bullish else 2650 + random.uniform(10, 20), 2),
                        'take_profit': round(2650 + random.uniform(10, 20) if is_bullish else 2650 + random.uniform(-20, -10), 2)
                    })

        return jsonify({
            'success': True,
            'signals': signals,
            'count': len(signals),
            'timestamp': datetime.now().isoformat()
        })

    except Exception as e:
        return jsonify({
            'success': False,
            'error': str(e)
        })

@app.route('/api/pattern-auto-trading/execute', methods=['POST'])
@login_required
def api_pattern_auto_trading_execute():
    """执行形态信号交易"""
    try:
        data = request.json
        signal_id = data.get('signal_id')
        signal_data = data.get('signal_data')

        if not signal_data:
            return jsonify({'success': False, 'error': '缺少信号数据'})

        # 检查MT5连接
        from services.mt5_service import mt5_service
        if not mt5_service.connected:
            return jsonify({'success': False, 'error': 'MT5未连接'})

        # 构建交易参数
        symbol = signal_data['symbol']
        pattern_type = signal_data['pattern_type']
        volume = data.get('volume', 0.01)

        # 转换交易方向
        import MetaTrader5 as mt5
        order_type = mt5.ORDER_TYPE_BUY if pattern_type == 'bullish' else mt5.ORDER_TYPE_SELL

        # 计算止损止盈
        entry_price = signal_data.get('entry_price', signal_data['price'])
        stop_loss = signal_data.get('stop_loss')
        take_profit = signal_data.get('take_profit')

        # 获取当前价格
        current_price = signal_data.get('entry_price', signal_data['price'])

        # 强制计算止损止盈，确保都有设置
        if pattern_type == 'bullish':  # 买入
            stop_loss = current_price * 0.98  # 2%止损
            take_profit = current_price * 1.04  # 4%止盈
        else:  # 卖出
            stop_loss = current_price * 1.02  # 2%止损
            take_profit = current_price * 0.96  # 4%止盈

        print(f"📊 形态交易止盈止损设置:")
        print(f"   当前价格: {current_price}")
        print(f"   止损价格: {stop_loss}")
        print(f"   止盈价格: {take_profit}")
        print(f"   交易方向: {pattern_type}")

        # 执行交易
        result = mt5_service.send_order(
            symbol=symbol,
            order_type=order_type,
            volume=volume,
            sl=stop_loss,
            tp=take_profit,
            comment=f"形态信号:{signal_data['pattern_name']}"
        )

        if result and result.get('success'):
            # 记录交易到数据库
            from models import Trade
            from datetime import datetime

            trade = Trade(
                account_id=1,  # 默认账户ID，实际应该从MT5获取
                symbol=symbol,
                trade_type='buy' if pattern_type == 'bullish' else 'sell',
                volume=volume,
                open_price=entry_price,
                stop_loss=stop_loss,
                take_profit=take_profit,
                open_time=datetime.utcnow(),
                status='open',
                strategy_name=f"形态监测:{signal_data['pattern_name']}",
                mt5_ticket=result.get('order_id')
            )

            db.session.add(trade)
            db.session.commit()

            return jsonify({
                'success': True,
                'trade_id': trade.id,
                'mt5_ticket': result.get('order_id'),
                'message': f"形态信号交易已执行: {signal_data['pattern_name']}"
            })
        else:
            return jsonify({
                'success': False,
                'error': result.get('error', '交易执行失败')
            })

    except Exception as e:
        return jsonify({
            'success': False,
            'error': str(e)
        })

@app.route('/api/pattern-auto-trading/stats', methods=['GET'])
@login_required
def api_pattern_auto_trading_stats():
    """获取形态自动交易统计"""
    try:
        from datetime import datetime, timedelta
        from services.mt5_service import mt5_service

        # 获取今日开始时间
        today = datetime.now().date()
        start_time = datetime.combine(today, datetime.min.time())

        # 查询今日形态信号交易（Trade模型没有user_id字段）
        pattern_trades = Trade.query.filter(
            Trade.strategy_name.like('%形态监测%'),
            Trade.open_time >= start_time
        ).all()

        # 统计数据
        total_trades = len(pattern_trades)
        closed_trades = [t for t in pattern_trades if t.status == 'closed']
        open_trades = [t for t in pattern_trades if t.status == 'open']

        winning_trades = [t for t in closed_trades if t.profit and t.profit > 0]
        win_rate = len(winning_trades) / len(closed_trades) if closed_trades else 0

        # 计算今日盈亏
        total_pnl = sum(t.profit for t in closed_trades if t.profit)

        # 获取浮动盈亏
        floating_pnl = 0
        if mt5_service.connected:
            import MetaTrader5 as mt5
            for trade in open_trades:
                if trade.mt5_ticket:
                    positions = mt5.positions_get(ticket=trade.mt5_ticket)
                    if positions:
                        floating_pnl += positions[0].profit

        return jsonify({
            'success': True,
            'stats': {
                'today_signals': total_trades,  # 今日信号数量
                'executed_trades': total_trades,  # 执行交易数量
                'current_positions': len(open_trades),  # 当前持仓
                'today_pnl': round(total_pnl + floating_pnl, 2),  # 今日盈亏
                'win_rate': round(win_rate * 100, 1),  # 胜率
                'closed_pnl': round(total_pnl, 2),  # 已平仓盈亏
                'floating_pnl': round(floating_pnl, 2)  # 浮动盈亏
            }
        })

    except Exception as e:
        return jsonify({
            'success': False,
            'error': str(e)
        })

@app.route('/api/pattern-analysis', methods=['POST'])
@login_required
def pattern_analysis():
    """形态分析API"""
    try:
        data = request.get_json()
        symbol = data.get('symbol', 'EURUSD')
        timeframe = data.get('timeframe', '15m')
        chart_data = data.get('data', [])
        confidence_threshold = data.get('confidence_threshold', 0.6)

        if not chart_data:
            return jsonify({'success': False, 'error': '没有提供K线数据'})

        # 转换数据格式为DataFrame
        df = pd.DataFrame(chart_data)
        if 'timestamp' in df.columns:
            df['timestamp'] = pd.to_datetime(df['timestamp'])
            df.set_index('timestamp', inplace=True)

        # 确保数据列存在
        required_columns = ['open', 'high', 'low', 'close', 'volume']
        for col in required_columns:
            if col not in df.columns:
                df[col] = df.get('close', 0)  # 使用收盘价作为默认值

        # 识别形态
        signals = pattern_recognizer.identify_patterns(df, symbol, timeframe)

        # 过滤低置信度信号
        filtered_signals = [s for s in signals if s.confidence >= confidence_threshold]

        # 转换为JSON格式
        signals_data = []
        for signal in filtered_signals:
            signals_data.append({
                'pattern_name': signal.pattern_name,
                'pattern_type': signal.pattern_type,
                'confidence': signal.confidence,
                'entry_point': signal.entry_point,
                'stop_loss': signal.stop_loss,
                'take_profit': signal.take_profit,
                'timestamp': signal.timestamp.isoformat(),
                'timeframe': signal.timeframe,
                'symbol': signal.symbol,
                'description': signal.description
            })

        return jsonify({
            'success': True,
            'signals': signals_data,
            'total_signals': len(signals_data),
            'analysis_time': datetime.now().isoformat()
        })

    except Exception as e:
        return jsonify({'success': False, 'error': str(e)})

@app.route('/api/market-data/<symbol>')
@login_required
def get_market_data(symbol):
    """获取市场数据API - 支持真实数据和测试数据"""
    try:
        timeframe = request.args.get('timeframe', '15m')
        limit = int(request.args.get('limit', 100))
        pattern_type = request.args.get('pattern_type', 'random')
        data_type = request.args.get('data_type', 'live')  # 新增：数据类型参数

        if data_type == 'test':
            # 测试数据模式：仅支持形态测试数据
            if pattern_type == 'random':
                return jsonify({
                    'success': False,
                    'error': '随机数据模式已禁用，请使用真实数据或特定形态测试'
                })
            else:
                # 生成包含特定形态的测试数据（保留用于形态查看）
                base_price = 1.1000 if 'USD' in symbol else 2000.0
                test_data_generator.base_price = base_price
                data = test_data_generator.generate_mixed_pattern_data(pattern_type, limit)

                # 转换时间戳格式
                for item in data:
                    if isinstance(item['timestamp'], str):
                        continue
                    item['timestamp'] = item['timestamp'].isoformat()
        else:
            # 实时数据模式：仅使用真实市场数据
            data = get_real_market_data(symbol, timeframe, limit)
            if not data:
                return jsonify({
                    'success': False,
                    'error': f'无法获取 {symbol} 的真实市场数据，请检查数据源连接'
                })

        return jsonify({
            'success': True,
            'data': data,
            'symbol': symbol,
            'timeframe': timeframe,
            'pattern_type': pattern_type,
            'data_type': data_type,
            'count': len(data),
            'is_real_data': data_type == 'live' and data is not None
        })

    except Exception as e:
        return jsonify({'success': False, 'error': str(e)})

@app.route('/api/pattern-verification', methods=['POST'])
@login_required
def pattern_verification():
    """形态验证API"""
    try:
        data = request.get_json()
        signal_data = data.get('signal')
        current_price = data.get('current_price')
        bars_since_signal = data.get('bars_since_signal', 0)

        if not signal_data or current_price is None:
            return jsonify({'success': False, 'error': '缺少必要参数'})

        # 重建PatternSignal对象
        signal = PatternSignal(
            pattern_name=signal_data['pattern_name'],
            pattern_type=signal_data['pattern_type'],
            confidence=signal_data['confidence'],
            entry_point=signal_data['entry_point'],
            stop_loss=signal_data['stop_loss'],
            take_profit=signal_data['take_profit'],
            timestamp=datetime.fromisoformat(signal_data['timestamp']),
            timeframe=signal_data['timeframe'],
            symbol=signal_data['symbol'],
            description=signal_data['description']
        )

        # 验证信号
        verification = pattern_recognizer.verify_signal(signal, current_price, bars_since_signal)

        # 保存验证结果
        pattern_recognizer.verification_results.append(verification)

        return jsonify({
            'success': True,
            'verification': verification
        })

    except Exception as e:
        return jsonify({'success': False, 'error': str(e)})

@app.route('/api/pattern-statistics')
@login_required
def pattern_statistics():
    """获取形态统计信息API"""
    try:
        stats = pattern_recognizer.get_pattern_statistics()

        return jsonify({
            'success': True,
            'statistics': stats,
            'total_verifications': len(pattern_recognizer.verification_results)
        })

    except Exception as e:
        return jsonify({'success': False, 'error': str(e)})

def get_real_market_data(symbol, timeframe, limit):
    """获取真实市场数据"""
    try:
        # 首先尝试MT5数据源
        real_data = get_mt5_market_data(symbol, timeframe, limit)
        if real_data:
            return real_data

        # 如果MT5失败，尝试Yahoo Finance
        real_data = get_yahoo_market_data(symbol, timeframe, limit)
        if real_data:
            return real_data

        # 如果都失败，返回None
        return None

    except Exception as e:
        print(f"获取真实市场数据失败: {e}")
        return None

def get_mt5_market_data(symbol, timeframe, limit):
    """从MT5获取真实市场数据"""
    try:
        from services.mt5_service import mt5_service
        import MetaTrader5 as mt5

        # 确保MT5连接
        if not mt5_service.connected:
            if not mt5_service.connect():
                return None

        # 时间框架映射
        tf_map = {
            '1m': mt5.TIMEFRAME_M1,
            '5m': mt5.TIMEFRAME_M5,
            '15m': mt5.TIMEFRAME_M15,
            '30m': mt5.TIMEFRAME_M30,
            '1h': mt5.TIMEFRAME_H1,
            '4h': mt5.TIMEFRAME_H4,
            '1d': mt5.TIMEFRAME_D1
        }

        tf = tf_map.get(timeframe, mt5.TIMEFRAME_M15)

        # 获取历史数据
        rates = mt5.copy_rates_from_pos(symbol, tf, 0, limit)
        if rates is None or len(rates) == 0:
            return None

        # 转换为所需格式
        data = []
        for rate in rates:
            data.append({
                'timestamp': datetime.fromtimestamp(rate['time']).isoformat(),
                'open': float(rate['open']),
                'high': float(rate['high']),
                'low': float(rate['low']),
                'close': float(rate['close']),
                'volume': int(rate['tick_volume'])
            })

        print(f"✅ 成功从MT5获取 {symbol} 数据: {len(data)} 条记录")
        return data

    except Exception as e:
        print(f"MT5数据获取失败: {e}")
        return None

def get_yahoo_market_data(symbol, timeframe, limit):
    """从Yahoo Finance获取真实市场数据"""
    try:
        import yfinance as yf
        from datetime import datetime, timedelta

        # 符号映射
        symbol_map = {
            'XAUUSD': 'GC=F',  # 黄金期货
            'EURUSD': 'EURUSD=X',
            'GBPUSD': 'GBPUSD=X',
            'USDJPY': 'USDJPY=X',
            'AUDUSD': 'AUDUSD=X',
            'USDCAD': 'USDCAD=X',
            'BTCUSD': 'BTC-USD'
        }

        yahoo_symbol = symbol_map.get(symbol, symbol)

        # 时间间隔映射
        interval_map = {
            '1m': '1m',
            '5m': '5m',
            '15m': '15m',
            '30m': '30m',
            '1h': '1h',
            '4h': '1h',  # Yahoo不支持4h，使用1h
            '1d': '1d'
        }

        interval = interval_map.get(timeframe, '15m')

        # 计算需要的时间周期
        if timeframe in ['1m', '5m']:
            period = '1d'
        elif timeframe in ['15m', '30m']:
            period = '5d'
        elif timeframe == '1h':
            period = '1mo'
        else:
            period = '3mo'

        # 获取数据
        ticker = yf.Ticker(yahoo_symbol)
        hist = ticker.history(period=period, interval=interval)

        if hist.empty:
            return None

        # 取最新的limit条记录
        hist = hist.tail(limit)

        # 转换为所需格式
        data = []
        for index, row in hist.iterrows():
            data.append({
                'timestamp': index.isoformat(),
                'open': float(row['Open']),
                'high': float(row['High']),
                'low': float(row['Low']),
                'close': float(row['Close']),
                'volume': int(row['Volume']) if not pd.isna(row['Volume']) else 1000
            })

        print(f"✅ 成功从Yahoo Finance获取 {yahoo_symbol} 数据: {len(data)} 条记录")
        return data

    except Exception as e:
        print(f"Yahoo Finance数据获取失败: {e}")
        return None

# generate_sample_market_data函数已删除 - 不再使用模拟数据

@app.route('/api/pattern-auto-trading/positions', methods=['GET'])
@login_required
def api_pattern_auto_trading_positions():
    """获取形态自动交易的当前持仓"""
    try:
        from services.mt5_service import mt5_service

        if not mt5_service.connected:
            return jsonify({'success': False, 'error': 'MT5未连接'})

        # 获取所有持仓
        import MetaTrader5 as mt5
        positions = mt5.positions_get()

        if positions is None:
            return jsonify({'success': True, 'positions': []})

        # 过滤形态信号交易的持仓（扩大过滤范围）
        pattern_positions = []
        for pos in positions:
            # 检查多种可能的标识
            is_pattern_trade = False
            comment = pos.comment or ''

            # 检查comment中是否包含形态相关关键词
            pattern_keywords = ['形态', 'pattern', 'AUTO', '锤子', '十字星', '吞没', '流星']
            for keyword in pattern_keywords:
                if keyword in comment:
                    is_pattern_trade = True
                    break

            # 检查magic number（如果使用特定的magic number）
            if pos.magic == 234000:  # 形态交易的magic number
                is_pattern_trade = True

            # 如果没有comment或者comment为空，也包含在内（可能是手动交易或其他形态交易）
            if not comment.strip():
                is_pattern_trade = True

            if is_pattern_trade:
                pattern_positions.append({
                    'ticket': pos.ticket,
                    'symbol': pos.symbol,
                    'type': 'buy' if pos.type == mt5.POSITION_TYPE_BUY else 'sell',
                    'volume': pos.volume,
                    'open_price': pos.price_open,
                    'current_price': pos.price_current,
                    'stop_loss': pos.sl if pos.sl > 0 else None,
                    'take_profit': pos.tp if pos.tp > 0 else None,
                    'profit': pos.profit,
                    'open_time': pos.time.isoformat() if hasattr(pos.time, 'isoformat') else str(pos.time),
                    'pattern_signal': comment.replace('形态信号:', '') if '形态信号:' in comment else comment,
                    'magic': pos.magic
                })

        print(f"✅ 找到 {len(pattern_positions)} 个形态交易持仓（总持仓数: {len(positions)}）")

        return jsonify({
            'success': True,
            'positions': pattern_positions
        })

    except Exception as e:
        return jsonify({
            'success': False,
            'error': str(e)
        })

@app.route('/api/pattern-auto-trading/history', methods=['GET'])
@login_required
def api_pattern_auto_trading_history():
    """获取形态自动交易的历史记录"""
    try:
        from datetime import datetime, timedelta

        # 获取今日开始时间
        today = datetime.now().date()
        start_time = datetime.combine(today, datetime.min.time())

        # 查询今日形态信号交易（Trade模型没有user_id字段）
        pattern_trades = Trade.query.filter(
            Trade.strategy_name.like('%形态%'),
            Trade.open_time >= start_time
        ).order_by(Trade.open_time.desc()).all()

        trades_data = []
        for trade in pattern_trades:
            trades_data.append({
                'id': trade.id,
                'symbol': trade.symbol,
                'type': trade.trade_type,
                'volume': trade.volume,
                'open_price': trade.open_price,
                'close_price': trade.close_price,
                'profit': trade.profit or 0,
                'open_time': trade.open_time.isoformat(),
                'close_time': trade.close_time.isoformat() if trade.close_time else None,
                'status': trade.status,
                'pattern_signal': trade.strategy_name.replace('形态监测:', '') if '形态监测:' in trade.strategy_name else trade.strategy_name,
                'confidence': 0.75,  # 默认置信度，实际应该从交易记录中获取
                'mt5_ticket': trade.mt5_ticket
            })

        return jsonify({
            'success': True,
            'trades': trades_data
        })

    except Exception as e:
        return jsonify({
            'success': False,
            'error': str(e)
        })

@app.route('/api/pattern-auto-trading/close-position', methods=['POST'])
@login_required
def api_pattern_auto_trading_close_position():
    """平仓单个形态交易持仓"""
    try:
        data = request.json
        ticket = data.get('ticket')

        if not ticket:
            return jsonify({'success': False, 'error': '缺少订单号'})

        from services.mt5_service import mt5_service

        if not mt5_service.connected:
            mt5_service.connect()
            if not mt5_service.connected:
                return jsonify({'success': False, 'error': 'MT5未连接'})

        # 检查MT5状态和市场状态
        def check_mt5_status():
            try:
                import MetaTrader5 as mt5

                # 检查终端信息
                terminal_info = mt5.terminal_info()
                if not terminal_info:
                    return False, "无法获取MT5终端信息"

                if not terminal_info.trade_allowed:
                    return False, "MT5终端不允许交易"

                # 检查账户信息
                account_info = mt5.account_info()
                if not account_info:
                    return False, "无法获取账户信息"

                if not account_info.trade_allowed:
                    return False, "账户不允许交易"

                if account_info.trade_mode != mt5.ACCOUNT_TRADE_MODE_DEMO and account_info.trade_mode != mt5.ACCOUNT_TRADE_MODE_REAL:
                    return False, f"账户交易模式异常: {account_info.trade_mode}"

                print(f"✅ MT5状态检查通过:")
                print(f"   终端: {terminal_info.name}")
                print(f"   账户: {account_info.login}")
                print(f"   余额: ${account_info.balance:.2f}")
                print(f"   交易允许: {account_info.trade_allowed}")

                return True, "MT5状态正常"

            except Exception as e:
                return False, f"MT5状态检查异常: {str(e)}"

        # 执行状态检查
        status_ok, status_msg = check_mt5_status()
        if not status_ok:
            return jsonify({'success': False, 'error': status_msg})

        # 执行平仓
        import MetaTrader5 as mt5

        # 确保ticket是整数类型
        try:
            ticket_int = int(ticket)
            print(f"🔍 尝试平仓订单: {ticket_int}")
            position = mt5.positions_get(ticket=ticket_int)
        except ValueError:
            return jsonify({'success': False, 'error': '订单号格式无效，应为整数'})

        if not position:
            print(f"❌ 持仓不存在: ticket={ticket_int}")
            # 尝试获取所有持仓进行调试
            all_positions = mt5.positions_get()
            if all_positions:
                print(f"📊 当前所有持仓票号: {[pos.ticket for pos in all_positions]}")
            else:
                print("📊 当前没有任何持仓")
            return jsonify({'success': False, 'error': f'持仓不存在，票号: {ticket_int}'})

        position = position[0]

        print(f"📊 持仓信息: 品种={position.symbol}, 类型={'买入' if position.type == mt5.POSITION_TYPE_BUY else '卖出'}, 手数={position.volume}")

        # 获取支持的填充模式
        symbol_info = mt5.symbol_info(position.symbol)
        if not symbol_info:
            return jsonify({'success': False, 'error': f'无法获取品种 {position.symbol} 的信息'})

        filling_mode = mt5.ORDER_FILLING_FOK
        if symbol_info.filling_mode & 2:
            filling_mode = mt5.ORDER_FILLING_IOC
        elif symbol_info.filling_mode & 1:
            filling_mode = mt5.ORDER_FILLING_FOK
        else:
            filling_mode = mt5.ORDER_FILLING_RETURN

        print(f"📊 填充模式: {filling_mode}")

        # 构建平仓请求
        close_type = mt5.ORDER_TYPE_SELL if position.type == mt5.POSITION_TYPE_BUY else mt5.ORDER_TYPE_BUY

        # 获取当前价格
        tick_info = mt5.symbol_info_tick(position.symbol)
        if not tick_info:
            return jsonify({'success': False, 'error': f'无法获取品种 {position.symbol} 的价格信息'})

        close_price = tick_info.bid if position.type == mt5.POSITION_TYPE_BUY else tick_info.ask

        print(f"📊 平仓价格: {close_price} ({'卖出' if close_type == mt5.ORDER_TYPE_SELL else '买入'})")

        # 验证关键参数
        if not position.symbol:
            return jsonify({'success': False, 'error': '持仓品种为空'})
        if position.volume <= 0:
            return jsonify({'success': False, 'error': f'持仓手数无效: {position.volume}'})
        if close_price <= 0:
            return jsonify({'success': False, 'error': f'平仓价格无效: {close_price}'})

        # 检查品种是否可交易
        if not symbol_info.trade_mode == mt5.SYMBOL_TRADE_MODE_FULL:
            return jsonify({'success': False, 'error': f'品种 {position.symbol} 当前不可交易'})

        request_close = {
            "action": mt5.TRADE_ACTION_DEAL,
            "symbol": position.symbol,
            "volume": position.volume,
            "type": close_type,
            "position": ticket,
            "price": close_price,
            "deviation": 20,
            "magic": 234000,
            "comment": f"平仓形态交易 {ticket}",
            "type_time": mt5.ORDER_TIME_GTC,
            "type_filling": filling_mode,
        }

        print(f"📊 平仓请求详情:")
        print(f"   品种: {position.symbol}")
        print(f"   手数: {position.volume}")
        print(f"   类型: {'卖出' if close_type == mt5.ORDER_TYPE_SELL else '买入'}")
        print(f"   持仓票号: {position.ticket}")
        print(f"   价格: {close_price}")
        print(f"   填充模式: {filling_mode}")
        print(f"   完整请求: {request_close}")

        # 检查MT5连接状态
        if not mt5.terminal_info():
            return jsonify({'success': False, 'error': 'MT5终端连接断开'})

        # 尝试发送平仓请求
        print("📤 发送平仓请求...")
        result = mt5.order_send(request_close)

        print(f"📊 平仓请求结果: {result}")

        # 检查result是否为None
        if result is None:
            print("❌ MT5返回None，尝试使用替代平仓方法...")

            # 尝试使用更简单的平仓请求
            simple_request = {
                "action": mt5.TRADE_ACTION_DEAL,
                "symbol": position.symbol,
                "volume": position.volume,
                "type": close_type,
                "position": position.ticket,
                "deviation": 50,  # 增加滑点容忍度
                "magic": 234000,
                "comment": "形态交易平仓",
                "type_filling": mt5.ORDER_FILLING_IOC,  # 使用IOC填充模式
            }

            print(f"📊 尝试简化平仓请求: {simple_request}")
            result = mt5.order_send(simple_request)
            print(f"📊 简化请求结果: {result}")

            if result is None:
                # 获取最后的错误信息
                last_error = mt5.last_error()
                error_msg = f"平仓请求失败: MT5返回None。最后错误: {last_error}"
                print(f"❌ {error_msg}")

                # 尝试获取更多调试信息
                terminal_info = mt5.terminal_info()
                account_info = mt5.account_info()

                print(f"📊 终端信息: {terminal_info}")
                print(f"📊 账户信息: {account_info}")

                # 最后尝试：使用直接平仓方法
                print("🔄 尝试最后的平仓方法...")
                try:
                    # 构建最基本的平仓请求
                    basic_request = {
                        "action": mt5.TRADE_ACTION_DEAL,
                        "symbol": position.symbol,
                        "volume": position.volume,
                        "type": close_type,
                        "position": position.ticket,
                        "deviation": 100,  # 更大的滑点容忍度
                        "type_filling": mt5.ORDER_FILLING_RETURN,  # 使用RETURN填充模式
                    }

                    print(f"📊 基本平仓请求: {basic_request}")
                    result = mt5.order_send(basic_request)
                    print(f"📊 基本请求结果: {result}")

                    if result is None:
                        return jsonify({'success': False, 'error': error_msg})

                except Exception as basic_error:
                    print(f"❌ 基本平仓请求也失败: {basic_error}")
                    return jsonify({'success': False, 'error': f'所有平仓方法都失败: {error_msg}'})

        if result.retcode == mt5.TRADE_RETCODE_DONE:
            # MT5平仓成功，更新数据库中的交易记录状态
            try:
                from models import Trade, db
                from datetime import datetime

                # 查找对应的交易记录
                trade = Trade.query.filter_by(mt5_ticket=ticket).first()
                if trade:
                    # 更新交易状态
                    trade.status = 'closed'
                    trade.close_time = datetime.utcnow()
                    trade.close_price = close_price

                    # 计算盈亏（根据品种使用不同的乘数）
                    if trade.trade_type == 'buy':
                        price_diff = close_price - trade.open_price
                    else:
                        price_diff = trade.open_price - close_price

                    # 根据品种计算盈亏
                    if 'XAU' in trade.symbol:
                        # 黄金：1手=100盎司
                        trade.profit = price_diff * trade.volume * 100
                    elif 'JPY' in trade.symbol:
                        # 日元对：点值不同
                        trade.profit = price_diff * trade.volume * 1000
                    else:
                        # 标准外汇对：1手=100,000基础货币
                        trade.profit = price_diff * trade.volume * 100000

                    db.session.commit()
                    print(f"✅ 已更新数据库中交易记录 {trade.id} 的状态为已平仓")
                else:
                    print(f"⚠️ 未找到MT5订单号 {ticket} 对应的数据库记录")

            except Exception as e:
                print(f"❌ 更新数据库交易状态失败: {e}")
                # 即使数据库更新失败，MT5平仓已成功，仍返回成功

            return jsonify({
                'success': True,
                'message': f'持仓 {ticket} 已平仓'
            })
        else:
            # 获取错误信息
            error_msg = "未知错误"
            if hasattr(result, 'comment') and result.comment:
                error_msg = result.comment
            elif hasattr(result, 'retcode'):
                error_msg = f"错误代码: {result.retcode}"

            print(f"❌ 平仓失败: {error_msg}")
            print(f"📊 完整结果: {result}")

            return jsonify({
                'success': False,
                'error': f'平仓失败: {error_msg}'
            })

    except Exception as e:
        print(f"❌ 平仓异常: {str(e)}")
        print(f"📊 异常类型: {type(e)}")
        import traceback
        print(f"📊 异常堆栈: {traceback.format_exc()}")

        return jsonify({
            'success': False,
            'error': f'平仓异常: {str(e)}'
        })

@app.route('/api/pattern-auto-trading/close-all-positions', methods=['POST'])
@login_required
def api_pattern_auto_trading_close_all_positions():
    """一键平仓所有形态交易持仓"""
    try:
        from services.mt5_service import mt5_service

        if not mt5_service.connected:
            return jsonify({'success': False, 'error': 'MT5未连接'})

        import MetaTrader5 as mt5
        positions = mt5.positions_get()

        if not positions:
            return jsonify({'success': True, 'closed_count': 0, 'message': '无持仓需要平仓'})

        # 过滤形态信号交易的持仓
        pattern_positions = [pos for pos in positions if pos.comment and '形态' in pos.comment]

        closed_count = 0
        failed_count = 0

        for position in pattern_positions:
            try:
                # 获取支持的填充模式
                symbol_info = mt5.symbol_info(position.symbol)
                filling_mode = mt5.ORDER_FILLING_FOK
                if symbol_info:
                    if symbol_info.filling_mode & 2:
                        filling_mode = mt5.ORDER_FILLING_IOC
                    elif symbol_info.filling_mode & 1:
                        filling_mode = mt5.ORDER_FILLING_FOK
                    else:
                        filling_mode = mt5.ORDER_FILLING_RETURN

                close_type = mt5.ORDER_TYPE_SELL if position.type == mt5.POSITION_TYPE_BUY else mt5.ORDER_TYPE_BUY
                close_price = mt5.symbol_info_tick(position.symbol).bid if position.type == mt5.POSITION_TYPE_BUY else mt5.symbol_info_tick(position.symbol).ask

                request_close = {
                    "action": mt5.TRADE_ACTION_DEAL,
                    "symbol": position.symbol,
                    "volume": position.volume,
                    "type": close_type,
                    "position": position.ticket,
                    "price": close_price,
                    "deviation": 20,
                    "magic": 234000,
                    "comment": f"一键平仓形态交易 {position.ticket}",
                    "type_time": mt5.ORDER_TIME_GTC,
                    "type_filling": filling_mode,
                }

                result = mt5.order_send(request_close)

                print(f"📊 平仓结果 {position.ticket}: {result}")

                if result and result.retcode == mt5.TRADE_RETCODE_DONE:
                    closed_count += 1
                    print(f"✅ 成功平仓 {position.ticket}")
                elif result:
                    print(f"❌ 平仓失败 {position.ticket}: 错误代码 {result.retcode}")
                    failed_count += 1
                else:
                    print(f"❌ 平仓失败 {position.ticket}: 无返回结果")
                    failed_count += 1

                    # 更新数据库中的交易记录状态
                    try:
                        from models import Trade, db
                        from datetime import datetime

                        trade = Trade.query.filter_by(mt5_ticket=position.ticket).first()
                        if trade:
                            trade.status = 'closed'
                            trade.close_time = datetime.utcnow()
                            trade.close_price = close_price

                            # 计算盈亏（根据品种使用不同的乘数）
                            if trade.trade_type == 'buy':
                                price_diff = close_price - trade.open_price
                            else:
                                price_diff = trade.open_price - close_price

                            # 根据品种计算盈亏
                            if 'XAU' in trade.symbol:
                                # 黄金：1手=100盎司
                                trade.profit = price_diff * trade.volume * 100
                            elif 'JPY' in trade.symbol:
                                # 日元对：点值不同
                                trade.profit = price_diff * trade.volume * 1000
                            else:
                                # 标准外汇对：1手=100,000基础货币
                                trade.profit = price_diff * trade.volume * 100000

                            db.session.commit()
                            print(f"✅ 已更新数据库中交易记录 {trade.id} 的状态为已平仓")
                        else:
                            print(f"⚠️ 未找到MT5订单号 {position.ticket} 对应的数据库记录")

                    except Exception as db_error:
                        print(f"❌ 更新数据库交易状态失败: {db_error}")
                        # 继续处理其他持仓

            except Exception as e:
                failed_count += 1
                print(f"平仓失败 {position.ticket}: {e}")

        return jsonify({
            'success': True,
            'closed_count': closed_count,
            'failed_count': failed_count,
            'message': f'成功平仓 {closed_count} 个持仓' + (f'，{failed_count} 个失败' if failed_count > 0 else '')
        })

    except Exception as e:
        return jsonify({
            'success': False,
            'error': str(e)
        })

@app.route('/api/pattern-auto-trading/execute-trade', methods=['POST'])
@login_required
def api_execute_pattern_trade():
    """执行形态/技术面信号交易"""
    try:
        # 导入MT5模块
        from services.mt5_service import mt5_service
        import MetaTrader5 as mt5

        data = request.get_json()
        symbol = data.get('symbol', 'XAUUSD')
        action = data.get('action', 'buy')  # buy 或 sell
        volume = float(data.get('volume', 0.01))
        entry_price = float(data.get('entry_price', 0))
        stop_loss = float(data.get('stop_loss', 0))
        take_profit = float(data.get('take_profit', 0))
        comment = data.get('comment', '形态信号交易')
        signal_id = data.get('signal_id', '')
        signal_type = data.get('signal_type', 'pattern')

        print(f"📊 执行{signal_type}信号交易:")
        print(f"   品种: {symbol}")
        print(f"   方向: {action}")
        print(f"   手数: {volume}")
        print(f"   入场价: {entry_price}")
        print(f"   止损: {stop_loss}")
        print(f"   止盈: {take_profit}")
        print(f"   备注: {comment}")

        # 验证参数
        if not symbol or not action:
            return jsonify({
                'success': False,
                'error': '缺少必要的交易参数'
            })

        if volume <= 0:
            return jsonify({
                'success': False,
                'error': '交易手数必须大于0'
            })

        if entry_price <= 0:
            return jsonify({
                'success': False,
                'error': '入场价格无效'
            })

        # 确保MT5连接
        if not mt5_service.connected:
            if not mt5_service.connect():
                return jsonify({
                    'success': False,
                    'error': 'MT5连接失败'
                })

        # 再次检查MT5初始化
        if not mt5.initialize():
            return jsonify({
                'success': False,
                'error': 'MT5初始化失败'
            })

        # 获取品种信息
        symbol_info = mt5.symbol_info(symbol)
        if symbol_info is None:
            return jsonify({
                'success': False,
                'error': f'品种 {symbol} 不存在'
            })

        if not symbol_info.visible:
            if not mt5.symbol_select(symbol, True):
                return jsonify({
                    'success': False,
                    'error': f'无法选择品种 {symbol}'
                })

        # 获取当前价格作为参考
        tick = mt5.symbol_info_tick(symbol)
        if not tick:
            return jsonify({
                'success': False,
                'error': f'无法获取{symbol}的当前价格'
            })

        # 如果入场价与当前价格差距过大，使用当前价格
        current_price = tick.ask if action.lower() == 'buy' else tick.bid
        if abs(entry_price - current_price) > current_price * 0.01:  # 超过1%差距
            print(f"⚠️ 入场价{entry_price}与当前价{current_price}差距过大，使用当前价格")
            entry_price = current_price

        # 准备交易请求
        order_type = mt5.ORDER_TYPE_BUY if action.lower() == 'buy' else mt5.ORDER_TYPE_SELL

        request_dict = {
            "action": mt5.TRADE_ACTION_DEAL,
            "symbol": symbol,
            "volume": volume,
            "type": order_type,
            "price": entry_price,
            "sl": stop_loss if stop_loss > 0 else 0,
            "tp": take_profit if take_profit > 0 else 0,
            "deviation": 20,
            "magic": 234000,
            "comment": comment,
            "type_time": mt5.ORDER_TIME_GTC,
            "type_filling": mt5.ORDER_FILLING_IOC,
        }

        print(f"📤 发送交易请求: {request_dict}")

        # 发送交易请求
        result = mt5.order_send(request_dict)

        if result is None:
            return jsonify({
                'success': False,
                'error': 'MT5交易请求失败'
            })

        print(f"📥 交易结果: {result}")

        if result.retcode != mt5.TRADE_RETCODE_DONE:
            error_msg = f'交易失败: {result.comment} (代码: {result.retcode})'
            print(f"❌ {error_msg}")

            # 提供更友好的错误信息
            if result.retcode == 10004:
                error_msg = '交易失败: 资金不足'
            elif result.retcode == 10006:
                error_msg = '交易失败: 请求被拒绝'
            elif result.retcode == 10007:
                error_msg = '交易失败: 请求被取消'
            elif result.retcode == 10008:
                error_msg = '交易失败: 订单已存在'
            elif result.retcode == 10009:
                error_msg = '交易失败: 订单不存在'
            elif result.retcode == 10013:
                error_msg = '交易失败: 无效请求'
            elif result.retcode == 10014:
                error_msg = '交易失败: 无效手数'
            elif result.retcode == 10015:
                error_msg = '交易失败: 无效价格'
            elif result.retcode == 10016:
                error_msg = '交易失败: 无效止损'
            elif result.retcode == 10017:
                error_msg = '交易失败: 无效止盈'
            elif result.retcode == 10018:
                error_msg = '交易失败: 市场关闭'
            elif result.retcode == 10019:
                error_msg = '交易失败: 资金不足'
            elif result.retcode == 10020:
                error_msg = '交易失败: 自动交易被禁用'
            elif result.retcode == 10021:
                error_msg = '交易失败: 专家顾问被禁用'
            elif result.retcode == 10025:
                error_msg = '交易失败: 没有连接'

            return jsonify({
                'success': False,
                'error': error_msg,
                'retcode': result.retcode
            })

        # 交易成功
        return jsonify({
            'success': True,
            'message': '交易执行成功',
            'order_id': result.order,
            'ticket': result.order,
            'volume': result.volume,
            'price': result.price
        })

    except Exception as e:
        print(f"❌ 执行交易异常: {e}")
        return jsonify({
            'success': False,
            'error': f'执行交易失败: {str(e)}'
        })

@app.route('/api/pattern-auto-trading/test-trade', methods=['POST'])
@login_required
def api_test_pattern_trade():
    """测试形态/技术面信号交易（简化版）"""
    try:
        print("🧪 开始测试形态交易API...")

        # 尝试导入MT5
        try:
            import MetaTrader5 as mt5
            print("✅ MetaTrader5模块导入成功")
        except ImportError as e:
            print(f"❌ MetaTrader5模块导入失败: {e}")
            return jsonify({
                'success': False,
                'error': f'MetaTrader5模块未安装: {e}'
            })

        # 测试MT5初始化
        try:
            init_result = mt5.initialize()
            print(f"🔗 MT5初始化结果: {init_result}")

            if not init_result:
                error_code = mt5.last_error()
                print(f"❌ MT5初始化失败，错误代码: {error_code}")
                return jsonify({
                    'success': False,
                    'error': f'MT5初始化失败，错误代码: {error_code}'
                })

            # 获取账户信息
            account_info = mt5.account_info()
            if account_info:
                print(f"✅ MT5账户信息: 账户号={account_info.login}, 余额={account_info.balance}")
            else:
                print("⚠️ 无法获取MT5账户信息")

            # 测试获取价格
            symbol = 'XAUUSD'
            tick = mt5.symbol_info_tick(symbol)
            if tick:
                print(f"✅ {symbol}价格: 买价={tick.bid}, 卖价={tick.ask}")
            else:
                print(f"⚠️ 无法获取{symbol}价格")

            return jsonify({
                'success': True,
                'message': 'MT5连接测试成功',
                'account_info': {
                    'login': account_info.login if account_info else None,
                    'balance': account_info.balance if account_info else None
                },
                'price_info': {
                    'symbol': symbol,
                    'bid': tick.bid if tick else None,
                    'ask': tick.ask if tick else None
                }
            })

        except Exception as mt5_error:
            print(f"❌ MT5操作异常: {mt5_error}")
            return jsonify({
                'success': False,
                'error': f'MT5操作失败: {mt5_error}'
            })

    except Exception as e:
        print(f"❌ 测试API异常: {e}")
        return jsonify({
            'success': False,
            'error': f'测试失败: {str(e)}'
        })

# ==================== 低风险交易API ====================

@app.route('/api/low-risk-trading/yearly-trend', methods=['GET'])
@login_required
def api_low_risk_yearly_trend():
    """获取年度趋势分析"""
    try:
        symbol = request.args.get('symbol', 'XAUUSD')
        days = int(request.args.get('days', 365))

        print(f"📊 分析{symbol}的{days}天年度趋势...")

        # 确保MT5连接
        from services.mt5_service import mt5_service
        import MetaTrader5 as mt5

        if not mt5_service.connected:
            if not mt5_service.connect():
                return jsonify({'success': False, 'error': 'MT5连接失败'})

        # 获取历史数据
        from datetime import datetime, timedelta
        end_time = datetime.now()
        start_time = end_time - timedelta(days=days)

        rates = mt5.copy_rates_range(symbol, mt5.TIMEFRAME_D1, start_time, end_time)

        if rates is None or len(rates) < 30:
            return jsonify({'success': False, 'error': '历史数据不足'})

        # 分析趋势
        closes = [rate['close'] for rate in rates]
        highest_price = max(closes)
        lowest_price = min(closes)
        current_price = closes[-1]

        # 计算当前价格在年度区间的位置
        current_position = ((current_price - lowest_price) / (highest_price - lowest_price)) * 100

        # 趋势分析
        first_half = closes[:len(closes)//2]
        second_half = closes[len(closes)//2:]
        first_avg = sum(first_half) / len(first_half)
        second_avg = sum(second_half) / len(second_half)

        trend_strength = abs((second_avg - first_avg) / first_avg) * 100
        trend_direction = 'bullish' if second_avg > first_avg else 'bearish' if second_avg < first_avg else 'neutral'

        return jsonify({
            'success': True,
            'trend_direction': trend_direction,
            'trend_strength': min(trend_strength, 100),
            'highest_price': highest_price,
            'lowest_price': lowest_price,
            'current_position': current_position,
            'data_points': len(rates)
        })

    except Exception as e:
        print(f"❌ 年度趋势分析失败: {e}")
        return jsonify({'success': False, 'error': str(e)})

@app.route('/api/low-risk-trading/weekly-range', methods=['GET'])
@login_required
def api_low_risk_weekly_range():
    """获取周波段分析"""
    try:
        symbol = request.args.get('symbol', 'XAUUSD')
        days = int(request.args.get('days', 7))

        print(f"📊 分析{symbol}的{days}天波段...")

        # 确保MT5连接
        from services.mt5_service import mt5_service
        import MetaTrader5 as mt5

        if not mt5_service.connected:
            if not mt5_service.connect():
                return jsonify({'success': False, 'error': 'MT5连接失败'})

        # 获取小时数据
        from datetime import datetime, timedelta
        end_time = datetime.now()
        start_time = end_time - timedelta(days=days)

        rates = mt5.copy_rates_range(symbol, mt5.TIMEFRAME_H1, start_time, end_time)

        if rates is None or len(rates) < 24:
            return jsonify({'success': False, 'error': '历史数据不足'})

        # 按天分组分析
        daily_ranges = []
        current_day_data = []
        current_date = None

        for rate in rates:
            rate_date = datetime.fromtimestamp(rate['time']).date()

            if current_date != rate_date:
                # 处理前一天的数据
                if current_day_data:
                    day_high = max(r['high'] for r in current_day_data)
                    day_low = min(r['low'] for r in current_day_data)
                    day_range = ((day_high - day_low) / day_low) * 100
                    daily_ranges.append(day_range)

                current_day_data = []
                current_date = rate_date

            current_day_data.append(rate)

        # 处理最后一天
        if current_day_data:
            day_high = max(r['high'] for r in current_day_data)
            day_low = min(r['low'] for r in current_day_data)
            current_day_range = ((day_high - day_low) / day_low) * 100
        else:
            current_day_range = 0

        average_range = sum(daily_ranges) / len(daily_ranges) if daily_ranges else 0
        range_breakout = current_day_range > average_range * 1.2

        return jsonify({
            'success': True,
            'daily_ranges': daily_ranges,
            'average_range': average_range,
            'current_day_range': current_day_range,
            'range_breakout': range_breakout
        })

    except Exception as e:
        print(f"❌ 周波段分析失败: {e}")
        return jsonify({'success': False, 'error': str(e)})

@app.route('/api/low-risk-trading/overnight-extreme', methods=['GET'])
@login_required
def api_low_risk_overnight_extreme():
    """获取昨夜极值分析"""
    try:
        symbol = request.args.get('symbol', 'XAUUSD')
        start_time_str = request.args.get('start_time', '21:00')

        print(f"📊 分析{symbol}从昨夜{start_time_str}的极值...")

        # 确保MT5连接
        from services.mt5_service import mt5_service
        import MetaTrader5 as mt5

        if not mt5_service.connected:
            if not mt5_service.connect():
                return jsonify({'success': False, 'error': 'MT5连接失败'})

        # 计算时间范围
        from datetime import datetime, timedelta
        now = datetime.now()
        yesterday = now - timedelta(days=1)

        # 解析起始时间
        start_hour, start_minute = map(int, start_time_str.split(':'))
        start_time = yesterday.replace(hour=start_hour, minute=start_minute, second=0, microsecond=0)

        # 获取数据
        rates = mt5.copy_rates_range(symbol, mt5.TIMEFRAME_M15, start_time, now)

        if rates is None or len(rates) < 4:
            return jsonify({'success': False, 'error': '历史数据不足'})

        # 找到极值
        highs = [rate['high'] for rate in rates]
        lows = [rate['low'] for rate in rates]
        closes = [rate['close'] for rate in rates]

        highest_price = max(highs)
        lowest_price = min(lows)
        current_price = closes[-1]

        # 判断是高点还是低点更重要
        high_distance = abs(current_price - highest_price) / current_price
        low_distance = abs(current_price - lowest_price) / current_price

        if low_distance < high_distance:
            extreme_type = 'low'
            extreme_price = lowest_price
            price_change = ((current_price - lowest_price) / lowest_price) * 100
        else:
            extreme_type = 'high'
            extreme_price = highest_price
            price_change = ((current_price - highest_price) / highest_price) * 100

        # 检测反转信号
        reversal_signal = abs(price_change) > 0.1

        return jsonify({
            'success': True,
            'extreme_type': extreme_type,
            'extreme_price': extreme_price,
            'extreme_time': start_time.isoformat(),
            'current_price': current_price,
            'price_change': price_change,
            'reversal_signal': reversal_signal
        })

    except Exception as e:
        print(f"❌ 昨夜极值分析失败: {e}")
        return jsonify({'success': False, 'error': str(e)})

@app.route('/api/low-risk-trading/trend-detection', methods=['GET'])
@login_required
def api_low_risk_trend_detection():
    """单边行情检测"""
    try:
        symbol = request.args.get('symbol', 'XAUUSD')

        print(f"📊 检测{symbol}的单边行情...")

        # 确保MT5连接
        from services.mt5_service import mt5_service
        import MetaTrader5 as mt5

        if not mt5_service.connected:
            if not mt5_service.connect():
                return jsonify({'success': False, 'error': 'MT5连接失败'})

        # 获取最近数据
        from datetime import datetime, timedelta
        end_time = datetime.now()
        start_time = end_time - timedelta(hours=12)  # 最近12小时

        rates = mt5.copy_rates_range(symbol, mt5.TIMEFRAME_M15, start_time, end_time)

        if rates is None or len(rates) < 20:
            return jsonify({'success': False, 'error': '历史数据不足'})

        closes = [rate['close'] for rate in rates]

        # 计算移动平均线
        def calculate_sma(data, period):
            if len(data) < period:
                return []
            return [sum(data[i:i+period])/period for i in range(len(data)-period+1)]

        sma10 = calculate_sma(closes, 10)
        sma20 = calculate_sma(closes, 20)

        if len(sma10) < 5 or len(sma20) < 5:
            return jsonify({'success': False, 'error': '数据不足以计算趋势'})

        current_price = closes[-1]
        current_sma10 = sma10[-1]
        current_sma20 = sma20[-1]

        # 判断趋势方向
        trend_direction = 'neutral'
        trend_strength = 0

        if current_price > current_sma10 and current_sma10 > current_sma20:
            trend_direction = 'bullish'
            trend_strength = ((current_price - current_sma20) / current_sma20) * 100
        elif current_price < current_sma10 and current_sma10 < current_sma20:
            trend_direction = 'bearish'
            trend_strength = ((current_sma20 - current_price) / current_sma20) * 100

        # 计算动量
        momentum = 0
        if len(closes) >= 5:
            momentum = ((closes[-1] - closes[-5]) / closes[-5]) * 100

        # 判断是否为单边行情
        is_trending = abs(trend_strength) > 0.5 and abs(momentum) > 0.2

        return jsonify({
            'success': True,
            'trend_direction': trend_direction,
            'trend_strength': abs(trend_strength),
            'trend_duration': len(rates) * 15,  # 分钟数
            'momentum': momentum,
            'is_trending': is_trending
        })

    except Exception as e:
        print(f"❌ 单边行情检测失败: {e}")
        return jsonify({'success': False, 'error': str(e)})

@app.route('/api/low-risk-trading/execute-trade', methods=['POST'])
@login_required
def api_low_risk_execute_trade():
    """执行低风险交易"""
    try:
        data = request.get_json()
        symbol = data.get('symbol', 'XAUUSD')
        action = data.get('action', 'buy')
        volume = float(data.get('volume', 0.01))
        entry_price = float(data.get('entry_price', 0))
        stop_loss = float(data.get('stop_loss', 0))
        take_profit = float(data.get('take_profit', 0))

        # MT5注释字段限制：最大31个字符，只能包含ASCII字符
        raw_comment = data.get('comment', 'LowRisk_Trade')
        # 清理注释：移除特殊字符，限制长度
        comment = ''.join(c for c in raw_comment if c.isalnum() or c in '_-').replace(':', '')[:31]
        if not comment:  # 如果清理后为空，使用默认值
            comment = 'LowRisk_Trade'

        print(f"🛡️ 执行低风险交易:")
        print(f"   品种: {symbol}")
        print(f"   方向: {action}")
        print(f"   手数: {volume}")
        print(f"   入场价: {entry_price}")
        print(f"   止损: {stop_loss}")
        print(f"   止盈: {take_profit}")

        # 确保MT5连接
        from services.mt5_service import mt5_service
        import MetaTrader5 as mt5

        if not mt5_service.connected:
            if not mt5_service.connect():
                return jsonify({'success': False, 'error': 'MT5连接失败'})

        # 获取品种信息
        symbol_info = mt5.symbol_info(symbol)
        if symbol_info is None:
            return jsonify({'success': False, 'error': f'品种 {symbol} 不存在'})

        if not symbol_info.visible:
            if not mt5.symbol_select(symbol, True):
                return jsonify({'success': False, 'error': f'无法选择品种 {symbol}'})

        # 获取当前市场价格
        tick = mt5.symbol_info_tick(symbol)
        if tick is None:
            return jsonify({'success': False, 'error': f'无法获取{symbol}当前价格'})

        # 根据交易方向选择价格
        if action.lower() == 'buy':
            current_price = tick.ask
            order_type = mt5.ORDER_TYPE_BUY
        else:
            current_price = tick.bid
            order_type = mt5.ORDER_TYPE_SELL

        # 如果没有提供入场价，使用当前市场价
        if entry_price <= 0:
            entry_price = current_price

        print(f"💰 当前市场价格: {current_price}, 使用入场价: {entry_price}")

        # 准备交易请求
        request_dict = {
            "action": mt5.TRADE_ACTION_DEAL,
            "symbol": symbol,
            "volume": volume,
            "type": order_type,
            "price": current_price,  # 使用当前市场价格
            "sl": stop_loss if stop_loss > 0 else 0,
            "tp": take_profit if take_profit > 0 else 0,
            "deviation": 20,
            "magic": 235000,  # 低风险交易专用魔术号
            "comment": comment,
            "type_time": mt5.ORDER_TIME_GTC,
            "type_filling": mt5.ORDER_FILLING_IOC,
        }

        print(f"📤 发送低风险交易请求: {request_dict}")

        # 检查交易品种信息
        symbol_info = mt5.symbol_info(symbol)
        if symbol_info is None:
            return jsonify({'success': False, 'error': f'品种{symbol}不存在或未启用'})

        if not symbol_info.visible:
            # 尝试启用品种
            if not mt5.symbol_select(symbol, True):
                return jsonify({'success': False, 'error': f'无法启用品种{symbol}'})

        print(f"📊 品种信息: {symbol_info.description}, 最小手数: {symbol_info.volume_min}")

        # 发送交易请求
        result = mt5.order_send(request_dict)

        if result is None:
            last_error = mt5.last_error()
            error_msg = f'MT5交易请求失败: {last_error}'
            print(f"❌ MT5错误: {last_error}")
            return jsonify({'success': False, 'error': error_msg})

        print(f"📥 交易结果: retcode={result.retcode}, comment={result.comment}")

        if result.retcode != mt5.TRADE_RETCODE_DONE:
            # 提供更详细的错误信息
            error_codes = {
                mt5.TRADE_RETCODE_REQUOTE: '价格重新报价',
                mt5.TRADE_RETCODE_REJECT: '请求被拒绝',
                mt5.TRADE_RETCODE_CANCEL: '请求被取消',
                mt5.TRADE_RETCODE_PLACED: '订单已下达',
                mt5.TRADE_RETCODE_DONE_PARTIAL: '部分成交',
                mt5.TRADE_RETCODE_ERROR: '通用错误',
                mt5.TRADE_RETCODE_TIMEOUT: '超时',
                mt5.TRADE_RETCODE_INVALID: '无效请求',
                mt5.TRADE_RETCODE_INVALID_VOLUME: '无效手数',
                mt5.TRADE_RETCODE_INVALID_PRICE: '无效价格',
                mt5.TRADE_RETCODE_INVALID_STOPS: '无效止损止盈',
                mt5.TRADE_RETCODE_TRADE_DISABLED: '交易被禁用',
                mt5.TRADE_RETCODE_MARKET_CLOSED: '市场关闭',
                mt5.TRADE_RETCODE_NO_MONEY: '资金不足',
                mt5.TRADE_RETCODE_PRICE_CHANGED: '价格变化',
                mt5.TRADE_RETCODE_PRICE_OFF: '价格偏离',
                mt5.TRADE_RETCODE_INVALID_EXPIRATION: '无效到期时间',
                mt5.TRADE_RETCODE_ORDER_CHANGED: '订单状态改变',
                mt5.TRADE_RETCODE_TOO_MANY_REQUESTS: '请求过多',
                mt5.TRADE_RETCODE_NO_CHANGES: '无变化',
                mt5.TRADE_RETCODE_SERVER_DISABLES_AT: '服务器禁用自动交易',
                mt5.TRADE_RETCODE_CLIENT_DISABLES_AT: '客户端禁用自动交易',
                mt5.TRADE_RETCODE_LOCKED: '锁定',
                mt5.TRADE_RETCODE_FROZEN: '冻结',
                mt5.TRADE_RETCODE_INVALID_FILL: '无效成交类型',
                mt5.TRADE_RETCODE_CONNECTION: '连接问题',
                mt5.TRADE_RETCODE_ONLY_REAL: '仅限真实账户',
                mt5.TRADE_RETCODE_LIMIT_ORDERS: '订单数量限制',
                mt5.TRADE_RETCODE_LIMIT_VOLUME: '手数限制',
            }

            error_description = error_codes.get(result.retcode, f'未知错误代码: {result.retcode}')
            error_msg = f'交易失败: {result.comment} ({error_description})'
            print(f"❌ 交易失败详情: {error_msg}")
            return jsonify({'success': False, 'error': error_msg})

        # 交易成功，记录到数据库（如果需要）
        try:
            from models import LowRiskTrade

            # 判断操作类型：根据comment判断是自动还是手动交易
            operation_type = 'auto' if (comment and ('Auto_' in comment or '自动' in comment)) else 'manual'

            trade_record = LowRiskTrade(
                user_id=current_user.id,
                symbol=symbol,
                action=action,
                volume=volume,
                entry_price=entry_price,
                stop_loss=stop_loss,
                take_profit=take_profit,
                mt5_order_id=result.order,
                comment=comment,
                operation_type=operation_type  # 设置操作类型
            )
            db.session.add(trade_record)
            db.session.commit()
            print(f"✅ 低风险交易记录已保存到数据库 (操作类型: {operation_type})")
        except Exception as db_error:
            print(f"⚠️ 保存交易记录失败: {db_error}")

        return jsonify({
            'success': True,
            'message': '低风险交易执行成功',
            'order_id': result.order,
            'ticket': result.order,
            'volume': result.volume,
            'price': result.price
        })

    except Exception as e:
        print(f"❌ 执行低风险交易异常: {e}")
        return jsonify({'success': False, 'error': f'执行交易失败: {str(e)}'})

@app.route('/api/low-risk-trading/close-all-positions', methods=['POST'])
@login_required
def api_low_risk_close_all_positions():
    """一键平仓所有低风险持仓"""
    try:
        print("🛡️ 一键平仓所有低风险持仓...")

        from services.mt5_service import mt5_service
        import MetaTrader5 as mt5

        if not mt5_service.connected:
            if not mt5_service.connect():
                return jsonify({'success': False, 'error': 'MT5连接失败'})

        # 获取所有低风险持仓
        positions = mt5.positions_get()
        if positions is None:
            positions = []

        low_risk_positions = [pos for pos in positions if pos.magic == 235000]

        if not low_risk_positions:
            return jsonify({'success': True, 'message': '没有需要平仓的低风险持仓', 'closed_count': 0})

        closed_count = 0
        failed_count = 0

        for position in low_risk_positions:
            try:
                # 准备平仓请求
                close_type = mt5.ORDER_TYPE_SELL if position.type == mt5.POSITION_TYPE_BUY else mt5.ORDER_TYPE_BUY
                tick = mt5.symbol_info_tick(position.symbol)

                close_request = {
                    "action": mt5.TRADE_ACTION_DEAL,
                    "symbol": position.symbol,
                    "volume": position.volume,
                    "type": close_type,
                    "position": position.ticket,
                    "price": tick.bid if close_type == mt5.ORDER_TYPE_SELL else tick.ask,
                    "deviation": 20,
                    "magic": 235000,
                    "comment": "低风险交易批量平仓",
                    "type_time": mt5.ORDER_TIME_GTC,
                    "type_filling": mt5.ORDER_FILLING_IOC,
                }

                # 发送平仓请求
                result = mt5.order_send(close_request)

                if result and result.retcode == mt5.TRADE_RETCODE_DONE:
                    closed_count += 1
                    print(f"✅ 成功平仓持仓 {position.ticket}")
                else:
                    failed_count += 1
                    print(f"❌ 平仓失败 {position.ticket}: {result.comment if result else '无响应'}")

            except Exception as pos_error:
                failed_count += 1
                print(f"❌ 平仓持仓 {position.ticket} 异常: {pos_error}")

        return jsonify({
            'success': True,
            'message': f'批量平仓完成，成功{closed_count}个，失败{failed_count}个',
            'closed_count': closed_count,
            'failed_count': failed_count
        })

    except Exception as e:
        print(f"❌ 批量平仓失败: {e}")
        return jsonify({'success': False, 'error': str(e)})

# ==================== 时间序列回测引擎 ====================

def run_time_series_backtest(symbol, start_date, end_date, config, total_intervals):
    """
    时间序列回测引擎 - 逐步投递数据，避免前瞻性偏差
    """
    from datetime import datetime, timedelta
    import random
    import math

    print(f"🕐 开始时间序列回测引擎...")
    print(f"📅 回测时间范围: {start_date.strftime('%Y-%m-%d')} 至 {end_date.strftime('%Y-%m-%d')}")

    # 初始化回测状态
    backtest_state = {
        'current_time': start_date,
        'end_time': end_date,
        'positions': [],  # 当前持仓
        'trades': [],     # 已完成交易
        'daily_trade_count': {},  # 每日交易计数
        'market_data_buffer': [],  # 市场数据缓冲区
        'signal_history': [],      # 信号历史
        'total_pnl': 0,
        'win_trades': 0,
        'loss_trades': 0
    }

    # 获取MT5真实历史价格数据
    print(f"📡 从MT5获取真实历史数据...")
    price_history = get_mt5_historical_data(symbol, backtest_state['current_time'], backtest_state['end_time'], config['interval_minutes'], total_intervals)

    # 根据实际获取的数据调整回测范围
    actual_intervals = len(price_history)
    if actual_intervals < total_intervals:
        print(f"⚠️ 调整回测范围: {total_intervals} -> {actual_intervals} 个数据点")
        total_intervals = actual_intervals

    # 逐个时间点进行回测
    for i in range(total_intervals):
        current_time = backtest_state['current_time'] + timedelta(minutes=config['interval_minutes'] * i)

        # 安全的索引访问
        if i >= len(price_history):
            print(f"⚠️ 数据索引超出范围: {i} >= {len(price_history)}，停止回测")
            break

        current_price = price_history[i]

        # 更新市场数据缓冲区（只能看到当前及之前的数据）
        market_data_point = {
            'timestamp': current_time,
            'price': current_price,
            'high': current_price * (1 + random.uniform(0, 0.002)),
            'low': current_price * (1 - random.uniform(0, 0.002)),
            'volume': random.randint(100, 1000)
        }
        backtest_state['market_data_buffer'].append(market_data_point)

        # 保持缓冲区大小（只保留最近100个数据点）
        if len(backtest_state['market_data_buffer']) > 100:
            backtest_state['market_data_buffer'].pop(0)

        # 检查并更新现有持仓
        update_positions(backtest_state, current_price, current_time, config)

        # 生成交易信号（基于当前可用数据）
        signal = generate_trading_signal(backtest_state, current_time, config)

        # 执行交易决策
        if signal and should_execute_trade(backtest_state, signal, current_time, config):
            execute_backtest_trade(backtest_state, signal, current_price, current_time, config)

        # 每100个时间点输出一次进度
        if i % 100 == 0:
            progress = (i / total_intervals) * 100
            print(f"📈 回测进度: {progress:.1f}% ({i}/{total_intervals})")

    # 强制平仓所有剩余持仓
    final_price = price_history[-1]
    close_all_positions(backtest_state, final_price, backtest_state['end_time'], config)

    print(f"✅ 时间序列回测完成: {len(backtest_state['trades'])}笔交易")

    return {
        'trades': backtest_state['trades'],
        'total_pnl': backtest_state['total_pnl'],
        'win_trades': backtest_state['win_trades'],
        'loss_trades': backtest_state['loss_trades'],
        'price_history': price_history[-50:],  # 返回最后50个价格点
        'backtest_details': {
            'total_intervals': total_intervals,
            'interval_minutes': config['interval_minutes'],
            'max_positions': max(len(backtest_state['positions']) for _ in [0]) if backtest_state['trades'] else 0
        }
    }

def generate_realistic_price_series(base_price, length):
    """生成真实的价格序列（模拟市场波动）"""
    import math
    import random

    prices = [base_price]

    trend_direction = 1  # 1为上涨，-1为下跌
    trend_duration = 0
    max_trend_duration = random.randint(50, 150)  # 趋势持续时间

    print(f"📊 生成价格序列: {length}个点, 基础价格: {base_price}")

    for i in range(1, length):
        # 趋势变化逻辑
        if trend_duration >= max_trend_duration:
            # 改变趋势方向
            trend_direction *= -1
            trend_duration = 0
            max_trend_duration = random.randint(50, 150)
            print(f"📈 第{i}点: 趋势转换为{'上涨' if trend_direction > 0 else '下跌'}")

        # 增强趋势强度，确保能触发信号
        trend = trend_direction * 0.0008 * (1 + trend_duration / 100)  # 趋势随时间增强
        volatility = random.gauss(0, 0.001)  # 减少随机噪音

        # 计算动量（最近价格与10期前价格的比较）
        if len(prices) > 10:
            momentum = (prices[-1] - prices[-10]) / base_price * 0.05
        else:
            momentum = 0

        # 偶尔添加突破性变动
        breakthrough = 0
        if random.random() < 0.02:  # 2%概率出现突破
            breakthrough = random.choice([-0.003, 0.003])  # ±0.3%的突破
            print(f"🚀 第{i}点: 突破性变动 {breakthrough*100:.2f}%")

        price_change = trend + volatility + momentum + breakthrough
        new_price = prices[-1] * (1 + price_change)

        # 限制价格变动范围
        new_price = max(base_price * 0.85, min(base_price * 1.15, new_price))
        prices.append(new_price)

        trend_duration += 1

        # 每500个点输出一次价格信息
        if i % 500 == 0:
            price_change_percent = (new_price - base_price) / base_price * 100
            print(f"📊 第{i}点: 价格={new_price:.4f}, 变化={price_change_percent:.2f}%")

    final_change = (prices[-1] - base_price) / base_price * 100
    print(f"📊 价格序列生成完成: 最终价格={prices[-1]:.4f}, 总变化={final_change:.2f}%")

    return prices

def generate_trading_signal(backtest_state, current_time, config):
    """
    全新多指标黄金交易信号生成系统
    结合RSI、MACD、布林带和市场环境识别
    """
    # 导入新策略函数
    from new_gold_strategy import generate_new_trading_signal

    # 使用新策略生成信号
    return generate_new_trading_signal(backtest_state['market_data_buffer'], current_time, config)

def should_execute_trade(backtest_state, signal, current_time, config):
    """判断是否应该执行交易"""
    # 检查每日交易限制
    current_date = current_time.date()
    daily_count = backtest_state['daily_trade_count'].get(current_date, 0)

    if daily_count >= config['daily_limit']:
        print(f"🚫 交易限制: 今日已达到交易限制 {daily_count}/{config['daily_limit']}")
        return False

    # 检查是否已有同方向持仓
    for position in backtest_state['positions']:
        if position['type'] == signal['type']:
            print(f"🚫 重复持仓: 已有{signal['type']}方向持仓")
            return False  # 避免同方向重复开仓

    # 使用优化策略的信号强度要求
    min_strength = config.get('min_strength_execution', 0.18)  # 使用配置中的优化值
    if signal['strength'] < min_strength:
        print(f"🚫 信号强度不足: {signal['strength']:.3f} < {min_strength}")
        return False

    print(f"✅ 交易条件满足: {signal['type']}, 强度={signal['strength']:.3f}")
    return True

def execute_backtest_trade(backtest_state, signal, current_price, current_time, config):
    """执行回测交易"""
    # 计算止损止盈价格
    if signal['type'] == 'buy':
        stop_loss = current_price * (1 - config['stop_loss_percent'] / 100)
        take_profit = current_price * (1 + config['take_profit_percent'] / 100)
    else:
        stop_loss = current_price * (1 + config['stop_loss_percent'] / 100)
        take_profit = current_price * (1 - config['take_profit_percent'] / 100)

    # 创建持仓
    position = {
        'id': len(backtest_state['trades']) + len(backtest_state['positions']) + 1,
        'type': signal['type'],
        'entry_price': current_price,
        'stop_loss': stop_loss,
        'take_profit': take_profit,
        'lot_size': config['lot_size'],
        'entry_time': current_time,
        'signal_reason': signal['reason'],
        'signal_strength': signal['strength']
    }

    backtest_state['positions'].append(position)

    # 更新每日交易计数
    current_date = current_time.date()
    backtest_state['daily_trade_count'][current_date] = backtest_state['daily_trade_count'].get(current_date, 0) + 1

    print(f"📈 开仓: {signal['type']} @ {current_price:.4f}, SL: {stop_loss:.4f}, TP: {take_profit:.4f}")

def update_positions(backtest_state, current_price, current_time, config):
    """更新持仓状态，检查止损止盈"""
    positions_to_close = []

    for position in backtest_state['positions']:
        should_close = False
        close_reason = ""

        if position['type'] == 'buy':
            if current_price <= position['stop_loss']:
                should_close = True
                close_reason = "STOP_LOSS"
            elif current_price >= position['take_profit']:
                should_close = True
                close_reason = "TAKE_PROFIT"
        else:  # sell
            if current_price >= position['stop_loss']:
                should_close = True
                close_reason = "STOP_LOSS"
            elif current_price <= position['take_profit']:
                should_close = True
                close_reason = "TAKE_PROFIT"

        if should_close:
            close_position(backtest_state, position, current_price, current_time, close_reason)
            positions_to_close.append(position)

    # 移除已平仓的持仓
    for position in positions_to_close:
        backtest_state['positions'].remove(position)

def close_position(backtest_state, position, exit_price, exit_time, reason):
    """平仓并记录交易"""
    # 计算盈亏
    if position['type'] == 'buy':
        pnl = (exit_price - position['entry_price']) * position['lot_size'] * 100
    else:
        pnl = (position['entry_price'] - exit_price) * position['lot_size'] * 100

    # 记录交易
    trade = {
        'id': position['id'],
        'type': position['type'],
        'entry_price': position['entry_price'],
        'exit_price': exit_price,
        'entry_time': position['entry_time'].isoformat(),
        'exit_time': exit_time.isoformat(),
        'duration_minutes': int((exit_time - position['entry_time']).total_seconds() / 60),
        'pnl': round(pnl, 2),
        'lot_size': position['lot_size'],
        'close_reason': reason,
        'signal_reason': position['signal_reason'],
        'signal_strength': position['signal_strength']
    }

    backtest_state['trades'].append(trade)
    backtest_state['total_pnl'] += pnl

    if pnl > 0:
        backtest_state['win_trades'] += 1
    else:
        backtest_state['loss_trades'] += 1

    print(f"📉 平仓: {position['type']} @ {exit_price:.4f}, PnL: ${pnl:.2f}, 原因: {reason}")

def close_all_positions(backtest_state, final_price, final_time, config):
    """强制平仓所有剩余持仓"""
    for position in backtest_state['positions'][:]:  # 使用切片复制避免修改列表时的问题
        close_position(backtest_state, position, final_price, final_time, "BACKTEST_END")

    backtest_state['positions'].clear()

def get_mt5_historical_data(symbol, start_date, end_date, interval_minutes, total_intervals):
    """
    从MT5获取真实的历史市场数据 - 支持具体日期范围
    返回价格历史列表，长度不超过total_intervals
    """
    from datetime import datetime, timedelta

    try:
        print(f"🔍 从MT5获取 {symbol} 的真实历史数据...")
        print(f"📅 时间范围: {start_date.strftime('%Y-%m-%d')} 至 {end_date.strftime('%Y-%m-%d')}")

        # 导入MT5服务
        from services.mt5_service import mt5_service
        import MetaTrader5 as mt5

        # 确保MT5连接 - 必须使用真实数据
        if not mt5_service.connected:
            print("🔗 MT5未连接，尝试连接...")
            if not mt5_service.connect():
                print("❌ MT5连接失败，无法获取真实数据")
                raise Exception("MT5连接失败，系统要求必须使用真实历史数据")
            print("✅ MT5连接成功")

        # 转换时间间隔到MT5时间框架
        timeframe_map = {
            1: mt5.TIMEFRAME_M1,
            5: mt5.TIMEFRAME_M5,
            15: mt5.TIMEFRAME_M15,
            30: mt5.TIMEFRAME_M30,
            60: mt5.TIMEFRAME_H1,
            240: mt5.TIMEFRAME_H4,
            1440: mt5.TIMEFRAME_D1
        }

        mt5_timeframe = timeframe_map.get(interval_minutes, mt5.TIMEFRAME_M15)
        print(f"📊 使用MT5时间框架: {interval_minutes}分钟 -> {mt5_timeframe}")

        # 使用具体的日期范围获取历史数据
        rates = mt5.copy_rates_range(symbol, mt5_timeframe, start_date, end_date)

        # 如果范围查询失败，回退到位置查询
        if rates is None or len(rates) == 0:
            print("⚠️ 日期范围查询失败，尝试位置查询...")
            rates = mt5.copy_rates_from_pos(symbol, mt5_timeframe, 0, total_intervals)

        if rates is None or len(rates) == 0:
            print(f"❌ 无法从MT5获取 {symbol} 数据")
            raise Exception(f"无法从MT5获取{symbol}的历史数据，系统要求必须使用真实数据")

        # 提取收盘价格
        price_history = [float(rate[4]) for rate in rates]  # rate[4] 是收盘价

        print(f"✅ 成功从MT5获取 {len(price_history)} 个真实价格点")
        print(f"   价格范围: {min(price_history):.4f} - {max(price_history):.4f}")
        print(f"   最新价格: {price_history[-1]:.4f}")

        # 确保返回的数据不超过请求的数量
        available_data = len(price_history)
        if available_data < total_intervals:
            print(f"⚠️ MT5数据不足 ({available_data}/{total_intervals})，使用可用数据")
            print(f"📊 实际可用数据点: {available_data}")
        else:
            print(f"✅ 数据充足，使用请求的 {total_intervals} 个数据点")

        # 返回不超过total_intervals的数据
        result_data = price_history[:total_intervals]
        print(f"📊 返回数据长度: {len(result_data)}")

        return result_data

    except Exception as e:
        print(f"❌ MT5数据获取失败: {e}")
        raise Exception(f"MT5数据获取失败: {e}，系统要求必须使用真实数据")

def generate_fallback_data(symbol, count, base_price=None):
    """
    生成备用模拟数据（当MT5数据不可用时）
    """
    import random

    # 根据品种设置基础价格
    if base_price is None:
        price_defaults = {
            'XAUUSD': 2650.0,
            'EURUSD': 1.0850,
            'GBPUSD': 1.2700,
            'USDJPY': 148.50,
            'USDCHF': 0.8750,
            'AUDUSD': 0.6650,
            'USDCAD': 1.3600
        }
        base_price = price_defaults.get(symbol, 2650.0)

    print(f"📊 生成 {symbol} 备用数据: {count}个点, 基础价格: {base_price}")

    prices = [base_price]

    for i in range(1, count):
        # 简单的随机游走
        change_percent = random.gauss(0, 0.001)  # 0.1%标准差
        new_price = prices[-1] * (1 + change_percent)

        # 限制价格变动范围
        min_price = base_price * 0.95
        max_price = base_price * 1.05
        new_price = max(min_price, min(max_price, new_price))

        prices.append(new_price)

    return prices[1:]  # 返回除第一个价格外的所有价格

def try_get_real_market_data(symbol, days, interval_minutes):
    """
    尝试从多个数据源获取真实市场数据
    """
    # 数据源1: 尝试从本地数据库获取
    local_data = get_local_historical_data(symbol, days, interval_minutes)
    if local_data:
        return local_data

    # 数据源2: 尝试从API获取（如果有配置）
    api_data = get_api_historical_data(symbol, days, interval_minutes)
    if api_data:
        return api_data

    # 数据源3: 使用缓存的历史数据
    cached_data = get_cached_historical_data(symbol, days, interval_minutes)
    if cached_data:
        return cached_data

    return None

def get_local_historical_data(symbol, days, interval_minutes):
    """从本地数据库获取历史数据"""
    try:
        # 这里应该连接到您的数据库
        # 示例代码（需要根据实际数据库结构调整）

        from datetime import datetime, timedelta
        end_time = datetime.now()
        start_time = end_time - timedelta(days=days)

        # 如果有数据库连接，可以执行类似这样的查询：
        # query = """
        # SELECT timestamp, close_price
        # FROM market_data
        # WHERE symbol = %s
        # AND timestamp BETWEEN %s AND %s
        # AND interval_minutes = %s
        # ORDER BY timestamp
        # """
        # cursor.execute(query, (symbol, start_time, end_time, interval_minutes))
        # results = cursor.fetchall()
        # return [row[1] for row in results]  # 返回价格列表

        print(f"📊 本地数据库暂未配置")
        return None

    except Exception as e:
        print(f"❌ 本地数据获取失败: {e}")
        return None

def get_api_historical_data(symbol, days, interval_minutes):
    """从外部API获取历史数据"""
    try:
        # 这里可以集成真实的金融数据API
        # 例如：Alpha Vantage, Yahoo Finance, MetaTrader等

        print(f"📡 外部API暂未配置")
        return None

    except Exception as e:
        print(f"❌ API数据获取失败: {e}")
        return None

def get_cached_historical_data(symbol, days, interval_minutes):
    """获取缓存的历史数据"""
    try:
        # 可以从文件系统读取预先下载的历史数据
        import os

        cache_file = f"historical_data_{symbol}_{interval_minutes}m.json"
        if os.path.exists(cache_file):
            import json
            with open(cache_file, 'r') as f:
                cached_data = json.load(f)

            # 根据时间范围筛选数据
            # 这里需要根据实际缓存格式调整
            print(f"📁 找到缓存数据文件")
            return None  # 需要实现具体的数据解析逻辑

        print(f"📁 未找到缓存数据")
        return None

    except Exception as e:
        print(f"❌ 缓存数据获取失败: {e}")
        return None

def generate_improved_realistic_data(symbol, total_intervals):
    """
    生成改进的模拟数据，更接近真实市场行为
    基于真实市场特征：趋势、震荡、突破、回调等
    """
    import random
    import math

    # 根据不同品种设置不同的基础价格和波动特征
    market_config = {
        'XAUUSD': {'base_price': 2650.0, 'volatility': 0.015, 'trend_strength': 0.8},
        'EURUSD': {'base_price': 1.0850, 'volatility': 0.008, 'trend_strength': 0.6},
        'GBPUSD': {'base_price': 1.2700, 'volatility': 0.012, 'trend_strength': 0.7},
        'USDJPY': {'base_price': 148.50, 'volatility': 0.010, 'trend_strength': 0.7}
    }

    config = market_config.get(symbol, market_config['XAUUSD'])
    base_price = config['base_price']
    volatility_factor = config['volatility']
    trend_strength = config['trend_strength']

    print(f"📊 生成改进的{symbol}模拟数据: {total_intervals}个点")
    print(f"   基础价格: {base_price}, 波动率: {volatility_factor:.3f}")

    prices = [base_price]

    # 市场状态机
    market_state = 'consolidation'  # consolidation, uptrend, downtrend
    state_duration = 0
    max_state_duration = random.randint(50, 200)

    # 支撑阻力位
    support_level = base_price * 0.98
    resistance_level = base_price * 1.02

    for i in range(1, total_intervals):
        current_price = prices[-1]

        # 状态转换逻辑
        if state_duration >= max_state_duration:
            # 随机选择新状态
            states = ['consolidation', 'uptrend', 'downtrend']
            market_state = random.choice(states)
            state_duration = 0
            max_state_duration = random.randint(50, 200)
            print(f"📈 第{i}点: 市场状态转换为 {market_state}")

        # 根据市场状态生成价格变动
        if market_state == 'uptrend':
            trend_component = volatility_factor * 0.3 * trend_strength
            noise_component = random.gauss(0, volatility_factor * 0.5)
        elif market_state == 'downtrend':
            trend_component = -volatility_factor * 0.3 * trend_strength
            noise_component = random.gauss(0, volatility_factor * 0.5)
        else:  # consolidation
            trend_component = 0
            noise_component = random.gauss(0, volatility_factor * 0.3)

        # 支撑阻力反弹
        bounce_component = 0
        if current_price <= support_level:
            bounce_component = volatility_factor * 0.5  # 支撑反弹
        elif current_price >= resistance_level:
            bounce_component = -volatility_factor * 0.5  # 阻力回落

        # 动量效应
        momentum_component = 0
        if len(prices) >= 5:
            recent_change = (prices[-1] - prices[-5]) / prices[-5]
            momentum_component = recent_change * 0.1  # 动量延续

        # 随机事件（新闻、数据发布等）
        event_component = 0
        if random.random() < 0.01:  # 1%概率
            event_component = random.choice([-1, 1]) * volatility_factor * random.uniform(1, 3)
            print(f"📰 第{i}点: 市场事件影响 {event_component*100:.2f}%")

        # 合成价格变动
        total_change = trend_component + noise_component + bounce_component + momentum_component + event_component
        new_price = current_price * (1 + total_change)

        # 价格限制（防止极端值）
        min_price = base_price * 0.80
        max_price = base_price * 1.20
        new_price = max(min_price, min(max_price, new_price))

        prices.append(new_price)
        state_duration += 1

        # 更新支撑阻力位（动态调整）
        if i % 100 == 0:
            recent_prices = prices[-100:]
            support_level = min(recent_prices) * 0.999
            resistance_level = max(recent_prices) * 1.001

        # 定期输出进度
        if i % 500 == 0:
            price_change = (new_price - base_price) / base_price * 100
            print(f"📊 第{i}点: 价格={new_price:.4f}, 累计变化={price_change:.2f}%, 状态={market_state}")

    final_change = (prices[-1] - base_price) / base_price * 100
    price_range = (max(prices) - min(prices)) / base_price * 100
    print(f"📊 改进模拟数据生成完成:")
    print(f"   最终价格: {prices[-1]:.4f}")
    print(f"   总变化: {final_change:.2f}%")
    print(f"   价格区间: {price_range:.2f}%")

    return prices


@app.route('/api/low-risk-trading/backtest', methods=['POST'])
@login_required
def api_low_risk_backtest():
    """低风险交易策略回测 - 时间序列逐步回测"""
    try:
        data = request.get_json()
        symbol = data.get('symbol', 'XAUUSD')
        timeframe = data.get('timeframe', '15m')  # 回测时间间隔

        # 支持两种时间范围输入方式
        start_date_str = data.get('start_date')
        end_date_str = data.get('end_date')
        days = data.get('days')

        print(f"🔄 开始时间序列回测: 品种: {symbol}, 间隔: {timeframe}")

        # 强制检查MT5连接状态
        print(f"🔍 检查MT5连接状态...")
        from services.mt5_service import mt5_service
        import MetaTrader5 as mt5

        if not mt5_service.connected:
            print("❌ MT5未连接")
            return jsonify({
                'success': False,
                'error': 'MT5未连接。回测需要真实的历史数据，请确保MT5终端正在运行并已登录。'
            }), 400

        # 验证连接质量
        account_info = mt5.account_info()
        if account_info is None:
            print("❌ MT5账户信息获取失败")
            return jsonify({
                'success': False,
                'error': 'MT5连接异常，无法获取账户信息。请检查MT5终端状态。'
            }), 400

        # 测试数据获取能力
        test_rates = mt5.copy_rates_from_pos(symbol, mt5.TIMEFRAME_H1, 0, 1)
        if test_rates is None or len(test_rates) == 0:
            print(f"❌ 无法获取{symbol}的测试数据")
            return jsonify({
                'success': False,
                'error': f'MT5无法获取{symbol}的历史数据。请检查符号名称和数据权限。'
            }), 400

        print(f"✅ MT5连接验证通过，账户: {account_info.login}, 服务器: {account_info.server}")

        # 获取历史数据
        from datetime import datetime, timedelta
        import random
        import math

        # 计算回测时间范围
        if start_date_str and end_date_str:
            # 使用具体的开始和结束日期
            start_date = datetime.strptime(start_date_str, '%Y-%m-%d')
            end_date = datetime.strptime(end_date_str, '%Y-%m-%d')
            days = (end_date - start_date).days
            print(f"📅 使用指定日期范围: {start_date_str} 至 {end_date_str} ({days}天)")
        else:
            # 使用天数参数（向后兼容）
            days = int(days) if days else 7
            end_date = datetime.now()
            start_date = end_date - timedelta(days=days)
            print(f"📅 使用天数参数: 最近{days}天")

        # 计算回测的时间间隔（分钟）
        interval_minutes = {
            '1m': 1, '5m': 5, '15m': 15, '30m': 30,
            '1h': 60, '4h': 240, '1d': 1440
        }.get(timeframe, 15)

        # 计算总的时间点数
        total_minutes = days * 24 * 60
        total_intervals = total_minutes // interval_minutes

        print(f"📊 回测参数: {total_intervals}个时间点, 每{interval_minutes}分钟一个")
        print(f"📊 实际时间范围: {start_date.strftime('%Y-%m-%d')} 至 {end_date.strftime('%Y-%m-%d')}")

        # 根据用户选择的策略预设获取配置
        strategy_preset = data.get('strategy_preset', 'optimized')
        config = get_strategy_config_by_preset(strategy_preset, data)

        # 添加回测专用参数
        config.update({
            'timeframe': timeframe,
            'interval_minutes': interval_minutes
        })

        print(f"📊 回测配置参数:")
        print(f"   基础参数: 手数={config['lot_size']}, 止损={config['stop_loss_percent']}%, 止盈={config['take_profit_percent']}%")
        print(f"   交易控制: 每日限制={config['daily_limit']}, 最小信号={config['min_signals']}")
        print(f"   单边行情: 启用={config['trend_detection_enabled']}, 强度阈值={config['trend_strength_threshold']}%")
        print(f"   交易时间: {config['trading_hours_start']} - {config['trading_hours_end']}")
        print(f"   调试模式: 已启用宽松条件以增加交易机会")

        # 执行时间序列回测
        backtest_result = run_time_series_backtest(
            symbol=symbol,
            start_date=start_date,
            end_date=end_date,
            config=config,
            total_intervals=total_intervals
        )

        # 从回测结果中提取数据
        trades = backtest_result['trades']
        total_pnl = backtest_result['total_pnl']
        win_trades = backtest_result['win_trades']
        loss_trades = backtest_result['loss_trades']

        # 计算统计指标
        total_trades = len(trades)
        win_rate = (win_trades / total_trades * 100) if total_trades > 0 else 0
        avg_pnl_per_trade = total_pnl / total_trades if total_trades > 0 else 0

        # 计算最大回撤
        running_pnl = 0
        peak_pnl = 0
        max_drawdown = 0

        for trade in trades:
            running_pnl += trade['pnl']
            if running_pnl > peak_pnl:
                peak_pnl = running_pnl
            drawdown = peak_pnl - running_pnl
            if drawdown > max_drawdown:
                max_drawdown = drawdown

        # 计算盈亏比
        avg_win = sum(t['pnl'] for t in trades if t['pnl'] > 0) / win_trades if win_trades > 0 else 0
        avg_loss = abs(sum(t['pnl'] for t in trades if t['pnl'] < 0)) / loss_trades if loss_trades > 0 else 0
        profit_loss_ratio = avg_win / avg_loss if avg_loss > 0 else 0

        # 计算夏普比率（简化版）
        returns = [t['pnl'] for t in trades]
        avg_return = sum(returns) / len(returns) if returns else 0
        return_std = math.sqrt(sum((r - avg_return) ** 2 for r in returns) / len(returns)) if len(returns) > 1 else 0
        sharpe_ratio = avg_return / return_std if return_std > 0 else 0

        results = {
            'period_days': days,
            'start_date': start_date.isoformat(),
            'end_date': end_date.isoformat(),
            'symbol': symbol,
            'config': config,
            'summary': {
                'total_trades': total_trades,
                'win_trades': win_trades,
                'loss_trades': loss_trades,
                'win_rate': round(win_rate, 2),
                'total_pnl': round(total_pnl, 2),
                'avg_pnl_per_trade': round(avg_pnl_per_trade, 2),
                'max_drawdown': round(max_drawdown, 2),
                'profit_loss_ratio': round(profit_loss_ratio, 2),
                'sharpe_ratio': round(sharpe_ratio, 3)
            },
            'trades': trades[-20:] if len(trades) > 20 else trades,  # 最近20笔交易
            'total_trades_count': len(trades)
        }

        print(f"✅ 回测完成: {total_trades}笔交易, 胜率{win_rate:.1f}%, 总盈亏${total_pnl:.2f}")

        return jsonify({
            'success': True,
            'results': results
        })

    except Exception as e:
        import traceback
        error_details = traceback.format_exc()
        print(f"❌ 回测失败: {e}")
        print(f"❌ 详细错误信息: {error_details}")

        # 提供更具体的错误信息
        if "list index out of range" in str(e):
            error_msg = "数据索引错误，可能是历史数据不足或时间范围设置过大"
        elif "趋势强度不足" in str(e):
            error_msg = f"市场条件不满足交易要求: {str(e)}"
        else:
            error_msg = f'回测失败: {str(e)}'

        return jsonify({
            'success': False,
            'error': error_msg,
            'debug_info': {
                'error_type': type(e).__name__,
                'error_details': str(e)
            }
        })

# ==================== 低风险交易API结束 ====================

@app.route('/api/market-data/current-price', methods=['GET'])
@login_required
def api_get_current_price():
    """获取当前市场价格"""
    try:
        symbol = request.args.get('symbol')
        if not symbol:
            return jsonify({'success': False, 'error': '缺少交易品种参数'})

        from services.mt5_service import mt5_service

        if not mt5_service.connected:
            return jsonify({'success': False, 'error': 'MT5未连接'})

        # 获取当前价格
        import MetaTrader5 as mt5
        tick = mt5.symbol_info_tick(symbol)

        if tick is None:
            return jsonify({'success': False, 'error': f'无法获取{symbol}的价格数据'})

        return jsonify({
            'success': True,
            'symbol': symbol,
            'price': (tick.bid + tick.ask) / 2,  # 中间价
            'bid': tick.bid,
            'ask': tick.ask,
            'timestamp': tick.time
        })

    except Exception as e:
        return jsonify({'success': False, 'error': str(e)})

@app.route('/api/realtime-price', methods=['GET'])
@login_required
def api_realtime_price():
    """获取实时价格 - 兼容性API"""
    try:
        symbol = request.args.get('symbol', 'XAUUSD')

        from services.mt5_service import mt5_service

        if not mt5_service.connected:
            return jsonify({'success': False, 'error': 'MT5未连接'})

        # 获取当前价格
        import MetaTrader5 as mt5
        tick = mt5.symbol_info_tick(symbol)

        if tick is None:
            return jsonify({'success': False, 'error': f'无法获取{symbol}的价格数据'})

        return jsonify({
            'success': True,
            'symbol': symbol,
            'price': (tick.bid + tick.ask) / 2,  # 中间价
            'bid': tick.bid,
            'ask': tick.ask,
            'timestamp': tick.time,
            'time': tick.time
        })

    except Exception as e:
        return jsonify({'success': False, 'error': str(e)})

@app.route('/api/comprehensive-trading/execute', methods=['POST'])
@login_required
def api_comprehensive_trading_execute():
    """执行综合交易"""
    try:
        data = request.get_json()

        symbol = data.get('symbol')
        action = data.get('action')  # 'buy' or 'sell'
        volume = data.get('volume', 0.01)
        price = data.get('price')
        sl = data.get('sl')
        tp = data.get('tp')
        comment = data.get('comment', 'Comprehensive Trading')
        magic = data.get('magic', 888888)

        if not all([symbol, action, price]):
            return jsonify({'success': False, 'error': '缺少必要参数'})

        from services.mt5_service import mt5_service

        if not mt5_service.connected:
            return jsonify({'success': False, 'error': 'MT5未连接'})

        import MetaTrader5 as mt5

        # 构建交易请求
        request_dict = {
            "action": mt5.TRADE_ACTION_DEAL,
            "symbol": symbol,
            "volume": volume,
            "type": mt5.ORDER_TYPE_BUY if action == 'buy' else mt5.ORDER_TYPE_SELL,
            "price": price,
            "sl": sl,
            "tp": tp,
            "deviation": 20,
            "magic": magic,
            "comment": comment,
            "type_time": mt5.ORDER_TIME_GTC,
            "type_filling": mt5.ORDER_FILLING_IOC,
        }

        # 发送交易请求
        result = mt5.order_send(request_dict)

        if result and result.retcode == mt5.TRADE_RETCODE_DONE:
            # 记录交易到数据库
            try:
                import sqlite3
                conn = sqlite3.connect('trading_system.db')
                cursor = conn.cursor()

                cursor.execute("""
                    INSERT INTO trades (
                        user_id, symbol, action, volume, entry_price,
                        stop_loss, take_profit, status, trade_type,
                        mt5_ticket, comment, created_at
                    ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
                """, (
                    current_user.id, symbol, action, volume, price,
                    sl, tp, 'open', 'comprehensive',
                    result.order, comment, datetime.utcnow()
                ))

                conn.commit()
                conn.close()

            except Exception as db_error:
                print(f"❌ 记录综合交易到数据库失败: {db_error}")

            return jsonify({
                'success': True,
                'ticket': result.order,
                'price': result.price,
                'volume': result.volume,
                'message': f'综合交易执行成功: {symbol} {action.upper()}'
            })
        else:
            error_msg = f'交易失败: {result.comment if result else "未知错误"}'
            return jsonify({'success': False, 'error': error_msg})

    except Exception as e:
        return jsonify({'success': False, 'error': str(e)})

@app.route('/api/comprehensive-trading/positions', methods=['GET'])
@login_required
def api_comprehensive_trading_positions():
    """获取综合交易持仓"""
    try:
        from services.mt5_service import mt5_service

        if not mt5_service.connected:
            return jsonify({'success': False, 'error': 'MT5未连接'})

        import MetaTrader5 as mt5

        # 获取所有持仓
        positions = mt5.positions_get()

        if positions is None:
            return jsonify({'success': True, 'positions': []})

        # 筛选综合交易系统的持仓（通过magic number识别）
        comprehensive_positions = []
        for pos in positions:
            if pos.magic == 888888:  # 综合交易系统的魔术号
                comprehensive_positions.append({
                    'ticket': pos.ticket,
                    'symbol': pos.symbol,
                    'type': pos.type,
                    'volume': pos.volume,
                    'price_open': pos.price_open,
                    'sl': pos.sl,
                    'tp': pos.tp,
                    'profit': pos.profit,
                    'comment': pos.comment,
                    'time': pos.time
                })

        return jsonify({
            'success': True,
            'positions': comprehensive_positions
        })

    except Exception as e:
        return jsonify({'success': False, 'error': str(e)})

@app.route('/api/comprehensive-trading/close-position', methods=['POST'])
@login_required
def api_comprehensive_trading_close_position():
    """关闭综合交易持仓"""
    try:
        data = request.get_json()
        ticket = data.get('ticket')
        reason = data.get('reason', '手动平仓')

        if not ticket:
            return jsonify({'success': False, 'error': '缺少持仓票号'})

        from services.mt5_service import mt5_service

        if not mt5_service.connected:
            return jsonify({'success': False, 'error': 'MT5未连接'})

        import MetaTrader5 as mt5

        # 获取持仓信息
        position = None
        positions = mt5.positions_get(ticket=ticket)
        if positions and len(positions) > 0:
            position = positions[0]

        if not position:
            return jsonify({'success': False, 'error': '找不到指定持仓'})

        # 构建平仓请求
        close_request = {
            "action": mt5.TRADE_ACTION_DEAL,
            "symbol": position.symbol,
            "volume": position.volume,
            "type": mt5.ORDER_TYPE_SELL if position.type == mt5.ORDER_TYPE_BUY else mt5.ORDER_TYPE_BUY,
            "position": ticket,
            "deviation": 20,
            "magic": 888888,
            "comment": f"Close: {reason}",
            "type_time": mt5.ORDER_TIME_GTC,
            "type_filling": mt5.ORDER_FILLING_IOC,
        }

        # 执行平仓
        result = mt5.order_send(close_request)

        if result and result.retcode == mt5.TRADE_RETCODE_DONE:
            # 更新数据库记录
            try:
                import sqlite3
                conn = sqlite3.connect('trading_system.db')
                cursor = conn.cursor()

                cursor.execute("""
                    UPDATE trades
                    SET status = ?, exit_price = ?, profit = ?,
                        close_reason = ?, updated_at = ?
                    WHERE mt5_ticket = ? AND user_id = ?
                """, (
                    'closed', result.price, position.profit,
                    reason, datetime.utcnow(), ticket, current_user.id
                ))

                conn.commit()
                conn.close()

            except Exception as db_error:
                print(f"❌ 更新综合交易记录失败: {db_error}")

            return jsonify({
                'success': True,
                'ticket': ticket,
                'profit': position.profit,
                'message': f'持仓已平仓: {reason}'
            })
        else:
            error_msg = f'平仓失败: {result.comment if result else "未知错误"}'
            return jsonify({'success': False, 'error': error_msg})

    except Exception as e:
        return jsonify({'success': False, 'error': str(e)})

@app.route('/api/comprehensive-trading/historical-data', methods=['GET'])
@login_required
def api_comprehensive_trading_historical_data():
    """获取综合交易历史数据用于机器学习优化"""
    try:
        import sqlite3
        conn = sqlite3.connect('trading_system.db')
        cursor = conn.cursor()

        # 获取最近30天的综合交易记录
        cursor.execute("""
            SELECT symbol, action, volume, entry_price, exit_price,
                   stop_loss, take_profit, profit, status, trade_type,
                   created_at, updated_at, close_reason
            FROM trades
            WHERE user_id = ? AND trade_type = 'comprehensive'
            AND created_at >= datetime('now', '-30 days')
            ORDER BY created_at DESC
        """, (current_user.id,))

        trades = []
        for row in cursor.fetchall():
            trade = {
                'symbol': row[0],
                'action': row[1],
                'volume': row[2],
                'entry_price': row[3],
                'exit_price': row[4],
                'stop_loss': row[5],
                'take_profit': row[6],
                'profit': row[7] or 0,
                'status': row[8],
                'trade_type': row[9],
                'created_at': row[10],
                'updated_at': row[11],
                'close_reason': row[12]
            }
            trades.append(trade)

        conn.close()

        return jsonify({
            'success': True,
            'trades': trades,
            'count': len(trades)
        })

    except Exception as e:
        return jsonify({'success': False, 'error': str(e)})

@app.route('/api/economic-calendar', methods=['GET'])
@login_required
def api_economic_calendar():
    """获取经济日历数据"""
    try:
        import requests
        from datetime import datetime, timedelta

        # 模拟经济日历数据（实际应用中应该从真实的经济日历API获取）
        # 可以集成 ForexFactory, Investing.com, FXStreet 等API

        now = datetime.now()
        events = []

        # 生成一些模拟的经济事件
        sample_events = [
            {
                'title': '美国非农就业人数',
                'currency': 'USD',
                'impact': 'high',
                'datetime': (now + timedelta(hours=2)).isoformat(),
                'forecast': '180K',
                'previous': '175K'
            },
            {
                'title': '欧元区CPI年率',
                'currency': 'EUR',
                'impact': 'high',
                'datetime': (now + timedelta(hours=6)).isoformat(),
                'forecast': '2.4%',
                'previous': '2.6%'
            },
            {
                'title': '英国GDP季率',
                'currency': 'GBP',
                'impact': 'medium',
                'datetime': (now + timedelta(hours=12)).isoformat(),
                'forecast': '0.2%',
                'previous': '0.1%'
            },
            {
                'title': '美联储利率决议',
                'currency': 'USD',
                'impact': 'high',
                'datetime': (now + timedelta(days=1, hours=3)).isoformat(),
                'forecast': '5.25%',
                'previous': '5.25%'
            },
            {
                'title': '日本央行利率决议',
                'currency': 'JPY',
                'impact': 'high',
                'datetime': (now + timedelta(days=2)).isoformat(),
                'forecast': '-0.10%',
                'previous': '-0.10%'
            }
        ]

        # 在实际应用中，这里应该调用真实的经济日历API
        # 例如：
        # response = requests.get('https://api.forexfactory.com/calendar', params={...})
        # events = response.json()

        events = sample_events

        return jsonify({
            'success': True,
            'events': events,
            'count': len(events),
            'source': 'simulated'  # 实际应用中应该是真实数据源
        })

    except Exception as e:
        return jsonify({'success': False, 'error': str(e)})

@app.route('/api/economic-calendar/real', methods=['GET'])
@login_required
def api_economic_calendar_real():
    """获取真实的经济日历数据（需要配置API密钥）"""
    try:
        # 这里可以集成真实的经济日历API
        # 例如：ForexFactory, Investing.com, Alpha Vantage等

        # 示例：使用Alpha Vantage的经济指标API
        # api_key = app.config.get('ALPHA_VANTAGE_API_KEY')
        # if not api_key:
        #     return jsonify({'success': False, 'error': '未配置API密钥'})

        # import requests
        # url = f'https://www.alphavantage.co/query?function=NEWS_SENTIMENT&apikey={api_key}'
        # response = requests.get(url)
        # data = response.json()

        return jsonify({
            'success': False,
            'error': '真实经济日历API需要配置，当前使用模拟数据',
            'suggestion': '请在配置文件中设置经济日历API密钥'
        })

    except Exception as e:
        return jsonify({'success': False, 'error': str(e)})

@app.route('/api/pattern-auto-trading/sync-status', methods=['POST'])
@login_required
def api_pattern_auto_trading_sync_status():
    """同步形态交易的状态，修复历史数据显示问题"""
    try:
        from services.mt5_service import mt5_service
        from models import Trade, db
        from datetime import datetime

        if not mt5_service.connected:
            return jsonify({'success': False, 'error': 'MT5未连接'})

        import MetaTrader5 as mt5

        # 获取所有形态交易记录
        pattern_trades = Trade.query.filter(
            Trade.strategy_name.like('%形态%'),
            Trade.status == 'open'  # 只处理状态为open的记录
        ).all()

        updated_count = 0

        for trade in pattern_trades:
            if not trade.mt5_ticket:
                continue

            # 检查MT5中是否还有这个持仓
            positions = mt5.positions_get(ticket=trade.mt5_ticket)

            if not positions:
                # MT5中没有这个持仓，说明已经平仓，更新数据库状态
                try:
                    # 尝试从历史记录中获取平仓信息
                    from_date = trade.open_time
                    to_date = datetime.utcnow()

                    deals = mt5.history_deals_get(from_date, to_date, position=trade.mt5_ticket)

                    if deals:
                        # 找到平仓交易
                        close_deal = None
                        for deal in deals:
                            if deal.entry == mt5.DEAL_ENTRY_OUT:  # 平仓交易
                                close_deal = deal
                                break

                        if close_deal:
                            trade.status = 'closed'
                            trade.close_time = datetime.fromtimestamp(close_deal.time)
                            trade.close_price = close_deal.price
                            trade.profit = close_deal.profit
                            updated_count += 1
                            print(f"✅ 同步交易 {trade.id} 状态: 已平仓，盈亏 ${trade.profit:.2f}")
                        else:
                            # 没找到平仓记录，可能是系统外平仓，标记为已平仓但没有详细信息
                            trade.status = 'closed'
                            trade.close_time = datetime.utcnow()
                            updated_count += 1
                            print(f"⚠️ 同步交易 {trade.id} 状态: 已平仓（无详细记录）")
                    else:
                        # 没有历史记录，可能是很久之前的交易，标记为已平仓
                        trade.status = 'closed'
                        trade.close_time = datetime.utcnow()
                        updated_count += 1
                        print(f"⚠️ 同步交易 {trade.id} 状态: 已平仓（历史记录不可用）")

                except Exception as e:
                    print(f"❌ 同步交易 {trade.id} 失败: {e}")
                    continue

        # 提交所有更改
        if updated_count > 0:
            db.session.commit()

        return jsonify({
            'success': True,
            'updated_count': updated_count,
            'message': f'成功同步 {updated_count} 个交易状态'
        })

    except Exception as e:
        return jsonify({
            'success': False,
            'error': str(e)
        })

@app.route('/api/mt5/calculate-margin', methods=['POST'])
@login_required
def api_mt5_calculate_margin():
    """计算MT5交易保证金需求"""
    try:
        data = request.json
        symbol = data.get('symbol', 'EURUSD')
        volume = float(data.get('volume', 0.01))
        order_type = data.get('order_type', 'buy')

        from services.mt5_service import mt5_service

        if not mt5_service.connected:
            return jsonify({'success': False, 'error': 'MT5未连接'})

        import MetaTrader5 as mt5

        # 获取账户信息
        account_info = mt5.account_info()
        if not account_info:
            return jsonify({'success': False, 'error': '无法获取账户信息'})

        # 获取品种信息
        symbol_info = mt5.symbol_info(symbol)
        if not symbol_info:
            return jsonify({'success': False, 'error': f'无法获取{symbol}品种信息'})

        # 获取当前价格
        tick = mt5.symbol_info_tick(symbol)
        if not tick:
            return jsonify({'success': False, 'error': f'无法获取{symbol}价格'})

        price = tick.ask if order_type == 'buy' else tick.bid

        # 计算保证金需求
        try:
            margin_required = mt5.order_calc_margin(
                mt5.ORDER_TYPE_BUY if order_type == 'buy' else mt5.ORDER_TYPE_SELL,
                symbol,
                volume,
                price
            )

            if margin_required is None:
                # 使用估算方法
                if symbol.startswith('XAU'):  # 黄金
                    contract_size = symbol_info.trade_contract_size or 100
                    margin_required = (volume * contract_size * price) / account_info.leverage
                else:  # 外汇对
                    contract_size = symbol_info.trade_contract_size or 100000
                    margin_required = (volume * contract_size) / account_info.leverage

        except Exception as e:
            return jsonify({'success': False, 'error': f'保证金计算失败: {e}'})

        # 检查是否可以交易
        can_trade = margin_required <= account_info.margin_free
        shortage = max(0, margin_required - account_info.margin_free)
        max_volume = (account_info.margin_free / margin_required) * volume if margin_required > 0 else 0

        return jsonify({
            'success': True,
            'symbol': symbol,
            'volume': volume,
            'price': price,
            'margin_required': margin_required,
            'margin_available': account_info.margin_free,
            'can_trade': can_trade,
            'shortage': shortage,
            'max_volume': max_volume
        })

    except Exception as e:
        return jsonify({
            'success': False,
            'error': str(e)
        })

@app.route('/mt5-funds-checker')
@login_required
def mt5_funds_checker():
    """MT5资金检查工具页面"""
    return render_template('mt5_funds_checker.html')

@app.route('/api/risk-events/filtered', methods=['POST'])
@login_required
def api_get_filtered_risk_events():
    """获取过滤后的风险事件"""
    try:
        data = request.json
        selected_symbols = data.get('symbols', [])
        analysis_mode = data.get('analysis_mode', 'directional')

        from services.risk_event_service import RiskEventService

        risk_service = RiskEventService()
        all_events = risk_service.get_current_risk_events()

        # 应用品种过滤
        filtered_events = filter_risk_events_by_symbols(all_events, selected_symbols)

        # 为每个事件添加影响分析
        analyzed_events = []
        for event in filtered_events:
            event_analysis = {
                'event': event,
                'impacts': []
            }

            for symbol in selected_symbols:
                impact = analyze_risk_impact_for_symbol(event, symbol, analysis_mode)
                event_analysis['impacts'].append({
                    'symbol': symbol,
                    'impact': impact
                })

            analyzed_events.append(event_analysis)

        return jsonify({
            'success': True,
            'events': analyzed_events,
            'filter_settings': {
                'symbols': selected_symbols,
                'analysis_mode': analysis_mode
            }
        })

    except Exception as e:
        return jsonify({
            'success': False,
            'error': str(e)
        })

def filter_risk_events_by_symbols(events, selected_symbols):
    """根据选择的品种过滤风险事件"""
    if not selected_symbols:
        return events

    filtered_events = []

    for event in events:
        # 检查事件是否与选择的品种相关
        event_text = (event.get('title', '') + ' ' + event.get('description', '') + ' ' +
                     event.get('currency', '') + ' ' + event.get('symbol', '')).lower()

        is_relevant = False
        for symbol in selected_symbols:
            symbol_lower = symbol.lower()

            # 品种关键词映射
            symbol_keywords = {
                'xauusd': ['gold', 'xau', '黄金', '贵金属', 'precious metal', 'fed', 'inflation', 'dollar'],
                'eurusd': ['eur', 'euro', '欧元', 'european', 'ecb', 'eurozone'],
                'gbpusd': ['gbp', 'pound', '英镑', 'sterling', 'uk', 'britain', 'boe'],
                'usdjpy': ['jpy', 'yen', '日元', 'japan', 'boj', 'intervention'],
                'crude_oil': ['oil', 'crude', '原油', '石油', 'wti', 'brent', 'opec']
            }

            # 检查直接匹配
            if symbol_lower in event_text:
                is_relevant = True
                break

            # 检查关键词匹配
            keywords = symbol_keywords.get(symbol_lower, [])
            if any(keyword in event_text for keyword in keywords):
                is_relevant = True
                break

        if is_relevant:
            filtered_events.append(event)

    return filtered_events

def analyze_risk_impact_for_symbol(event, symbol, analysis_mode):
    """分析风险事件对特定品种的影响"""
    event_text = (event.get('title', '') + ' ' + event.get('description', '')).lower()
    symbol_lower = symbol.lower()
    risk_level = event.get('risk_level', 1)

    # 基础影响分析
    impact = {
        'direction': 'neutral',  # 'bullish', 'bearish', 'neutral'
        'confidence': 0.5,
        'reasoning': '',
        'action': 'monitor',  # 'buy', 'sell', 'close', 'monitor'
        'risk_score': risk_level
    }

    # 黄金特定分析
    if symbol_lower == 'xauusd':
        impact = analyze_gold_risk_impact(event_text, risk_level, analysis_mode)
    # 欧元特定分析
    elif symbol_lower == 'eurusd':
        impact = analyze_eur_risk_impact(event_text, risk_level, analysis_mode)
    # 英镑特定分析
    elif symbol_lower == 'gbpusd':
        impact = analyze_gbp_risk_impact(event_text, risk_level, analysis_mode)
    # 日元特定分析
    elif symbol_lower == 'usdjpy':
        impact = analyze_jpy_risk_impact(event_text, risk_level, analysis_mode)

    return impact

def analyze_gold_risk_impact(event_text, risk_level, analysis_mode):
    """分析风险事件对黄金的影响"""
    impact = {'direction': 'neutral', 'confidence': 0.5, 'reasoning': '', 'action': 'monitor', 'risk_score': risk_level}

    # 利好黄金的因素
    bullish_keywords = ['inflation', '通胀', 'uncertainty', '不确定', 'crisis', '危机',
                       'war', '战争', 'tension', '紧张', 'geopolitical', '地缘政治']

    # 利空黄金的因素
    bearish_keywords = ['rate hike', '加息', 'strong dollar', '美元走强',
                       'economic growth', '经济增长', 'recovery', '复苏']

    if any(keyword in event_text for keyword in bullish_keywords):
        impact['direction'] = 'bullish'
        impact['confidence'] = 0.7
        impact['reasoning'] = '避险情绪和通胀担忧推动黄金上涨'
        impact['action'] = 'buy' if analysis_mode == 'directional' else 'monitor'
    elif any(keyword in event_text for keyword in bearish_keywords):
        impact['direction'] = 'bearish'
        impact['confidence'] = 0.6
        impact['reasoning'] = '美元走强或加息预期压制黄金'
        impact['action'] = 'sell' if analysis_mode == 'directional' else 'monitor'

    # 极高风险事件通常利好黄金
    if risk_level >= 3:
        if impact['direction'] == 'neutral':
            impact['direction'] = 'bullish'
            impact['confidence'] = 0.6
            impact['reasoning'] = '高风险事件推动避险需求，利好黄金'
            impact['action'] = 'buy' if analysis_mode == 'directional' else 'monitor'
        elif impact['direction'] == 'bullish':
            impact['confidence'] = min(impact['confidence'] + 0.2, 0.9)

    # 保守模式下的调整
    if analysis_mode == 'conservative' and risk_level >= 3:
        impact['action'] = 'close'
        impact['reasoning'] += ' (保守模式建议平仓避险)'

    return impact

def analyze_eur_risk_impact(event_text, risk_level, analysis_mode):
    """分析风险事件对欧元的影响"""
    impact = {'direction': 'neutral', 'confidence': 0.5, 'reasoning': '', 'action': 'monitor', 'risk_score': risk_level}

    if 'ecb' in event_text or '欧央行' in event_text:
        if 'rate cut' in event_text or '降息' in event_text:
            impact['direction'] = 'bearish'
            impact['confidence'] = 0.8
            impact['reasoning'] = '欧央行降息政策利空欧元'
            impact['action'] = 'sell' if analysis_mode == 'directional' else 'monitor'
        elif 'rate hike' in event_text or '加息' in event_text:
            impact['direction'] = 'bullish'
            impact['confidence'] = 0.8
            impact['reasoning'] = '欧央行加息政策利好欧元'
            impact['action'] = 'buy' if analysis_mode == 'directional' else 'monitor'

    # 保守模式调整
    if analysis_mode == 'conservative' and risk_level >= 3:
        impact['action'] = 'close'
        impact['reasoning'] += ' (保守模式建议平仓避险)'

    return impact

def analyze_gbp_risk_impact(event_text, risk_level, analysis_mode):
    """分析风险事件对英镑的影响"""
    impact = {'direction': 'neutral', 'confidence': 0.5, 'reasoning': '', 'action': 'monitor', 'risk_score': risk_level}

    if 'brexit' in event_text or '脱欧' in event_text:
        impact['direction'] = 'bearish'
        impact['confidence'] = 0.7
        impact['reasoning'] = '脱欧相关不确定性利空英镑'
        impact['action'] = 'sell' if analysis_mode == 'directional' else 'monitor'
    elif 'boe' in event_text or '英央行' in event_text:
        if 'rate hike' in event_text or '加息' in event_text:
            impact['direction'] = 'bullish'
            impact['confidence'] = 0.7
            impact['reasoning'] = '英央行加息政策利好英镑'
            impact['action'] = 'buy' if analysis_mode == 'directional' else 'monitor'

    # 保守模式调整
    if analysis_mode == 'conservative' and risk_level >= 3:
        impact['action'] = 'close'
        impact['reasoning'] += ' (保守模式建议平仓避险)'

    return impact

def analyze_jpy_risk_impact(event_text, risk_level, analysis_mode):
    """分析风险事件对日元的影响"""
    impact = {'direction': 'neutral', 'confidence': 0.5, 'reasoning': '', 'action': 'monitor', 'risk_score': risk_level}

    # 日元通常是避险货币
    if risk_level >= 3:
        impact['direction'] = 'bullish'
        impact['confidence'] = 0.6
        impact['reasoning'] = '避险情绪推动日元上涨'
        impact['action'] = 'buy' if analysis_mode == 'directional' else 'monitor'

    if 'intervention' in event_text or '干预' in event_text:
        impact['direction'] = 'bullish'
        impact['confidence'] = 0.8
        impact['reasoning'] = '日本央行干预支撑日元'
        impact['action'] = 'buy' if analysis_mode == 'directional' else 'monitor'

    # 保守模式调整
    if analysis_mode == 'conservative' and risk_level >= 3:
        impact['action'] = 'close'
        impact['reasoning'] += ' (保守模式建议平仓避险)'

    return impact

@app.route('/api/pattern-monitoring/auto-trade', methods=['POST'])
@login_required
def api_pattern_monitoring_auto_trade():
    """形态监测自动交易API"""
    try:
        data = request.json

        # 验证必要参数
        required_fields = ['symbol', 'action', 'volume', 'signalId']
        for field in required_fields:
            if field not in data:
                return jsonify({
                    'success': False,
                    'error': f'缺少必要参数: {field}'
                })

        symbol = data['symbol']
        action = data['action']  # 'buy' 或 'sell'
        volume = float(data['volume'])
        signal_id = data['signalId']
        confidence = data.get('confidence', 0.7)
        stop_loss = data.get('stop_loss')
        take_profit = data.get('take_profit')

        print(f"🔍 形态监测自动交易参数: {action} {symbol} {volume}手, SL={stop_loss}, TP={take_profit}")

        # 验证参数有效性
        if action not in ['buy', 'sell']:
            return jsonify({
                'success': False,
                'error': '无效的交易操作'
            })

        if volume < 0.01 or volume > 1.0:
            return jsonify({
                'success': False,
                'error': '交易手数超出允许范围(0.01-1.0)'
            })

        # 调用MT5服务执行交易
        from services.mt5_service import mt5_service
        import MetaTrader5 as mt5

        # 转换交易类型
        if action.lower() == 'buy':
            order_type = mt5.ORDER_TYPE_BUY
        elif action.lower() == 'sell':
            order_type = mt5.ORDER_TYPE_SELL
        else:
            return jsonify({
                'success': False,
                'error': f'无效的交易操作: {action}'
            })

        # 使用send_order方法执行交易（包含止盈止损）
        result = mt5_service.send_order(
            symbol=symbol,
            order_type=order_type,
            volume=volume,
            sl=stop_loss,
            tp=take_profit,
            comment=f'Pattern-Auto-{signal_id[:8]}'
        )

        if result and result.get('success'):
            # 记录交易日志
            print(f"✅ 形态监测自动交易成功: {action} {symbol} {volume}手, 订单号: {result.get('order_id')}")

            return jsonify({
                'success': True,
                'order_id': result.get('order_id'),
                'deal_id': result.get('deal_id'),
                'price': result.get('price'),
                'message': f'自动交易执行成功: {action.upper()} {symbol} {volume}手',
                'result_details': result
            })
        else:
            error_msg = result.get('error', '交易执行失败') if result else 'MT5服务不可用'
            print(f"❌ 形态监测自动交易失败: {error_msg}")

            return jsonify({
                'success': False,
                'error': error_msg
            })

    except Exception as e:
        print(f"❌ 形态监测自动交易API异常: {e}")
        return jsonify({
            'success': False,
            'error': f'服务器错误: {str(e)}'
        })

@app.route('/api/ai-models/user-models', methods=['GET'])
@login_required
def api_get_user_ai_models():
    """获取用户训练的AI模型列表"""
    try:
        # 使用Flask-Login的current_user获取用户信息
        from flask_login import current_user

        if not current_user.is_authenticated:
            print("❌ 用户未认证")
            return jsonify({
                'success': False,
                'error': '用户未登录'
            })

        user_id = current_user.id
        username = current_user.username
        print(f"🔍 AI模型API调用 - 用户ID: {user_id}")
        print(f"🔍 AI模型API调用 - 用户名: {username}")
        print(f"🔍 AI模型API调用 - 用户认证状态: {current_user.is_authenticated}")

        # 同时检查session中的数据（用于调试）
        session_user_id = session.get('user_id')
        print(f"🔍 Session中的用户ID: {session_user_id}")
        print(f"🔍 Session内容: {dict(session)}")

        # 使用原生SQL查询避免SQLAlchemy缓存问题（现在只查询strategy表）
        import sqlite3

        try:
            conn = sqlite3.connect('trading_system.db')
            cursor = conn.cursor()

            # 查询strategy表中的AI策略
            cursor.execute("""
                SELECT id, user_id, name, description, status,
                       is_shared, ai_model, timeframe, symbols, performance_metrics,
                       training_results, created_at
                FROM strategy
                WHERE user_id = ? AND strategy_type = ?
                ORDER BY created_at DESC
            """, (user_id, 'ai'))

            all_user_rows = cursor.fetchall()
            print(f"🔍 用户 {user_id} 的所有AI策略数量: {len(all_user_rows)}")

            user_strategies = []
            for row in all_user_rows:
                strategy_data = {
                    'id': row[0], 'user_id': row[1], 'name': row[2], 'description': row[3],
                    'status': row[4], 'is_shared': row[5], 'ai_model': row[6],
                    'timeframe': row[7], 'symbols': row[8], 'performance_metrics': row[9],
                    'training_results': row[10], 'created_at': row[11]
                }
                print(f"   - {strategy_data['name']} (ID: {strategy_data['id']}, 状态: {strategy_data['status']})")

                # 只添加已完成的策略
                if strategy_data['status'] == 'completed':
                    user_strategies.append(strategy_data)

            print(f"🔍 用户 {user_id} 的已完成AI策略数量: {len(user_strategies)}")

            if len(user_strategies) == 0 and len(all_user_rows) > 0:
                print("⚠️ 用户有AI策略但没有已完成的策略，可能状态不正确")
                # 临时使用所有策略进行调试
                user_strategies = [{'id': row[0], 'user_id': row[1], 'name': row[2], 'description': row[3],
                                  'status': row[4], 'is_shared': row[5], 'ai_model': row[6],
                                  'timeframe': row[7], 'symbols': row[8], 'performance_metrics': row[9],
                                  'training_results': row[10], 'created_at': row[11]}
                                 for row in all_user_rows]
                print("🔄 临时使用所有策略进行调试")

            # 查询管理员分享的策略
            cursor.execute("""
                SELECT id, user_id, name, description, status,
                       is_shared, ai_model, timeframe, symbols, performance_metrics,
                       training_results, created_at
                FROM strategy
                WHERE strategy_type = ? AND status = ? AND is_shared = 1
                ORDER BY created_at DESC
            """, ('ai', 'completed'))

            shared_rows = cursor.fetchall()
            shared_strategies = []

            for row in shared_rows:
                strategy_data = {
                    'id': row[0], 'user_id': row[1], 'name': row[2], 'description': row[3],
                    'status': row[4], 'is_shared': row[5], 'ai_model': row[6],
                    'timeframe': row[7], 'symbols': row[8], 'performance_metrics': row[9],
                    'training_results': row[10], 'created_at': row[11]
                }
                shared_strategies.append(strategy_data)

            print(f"🔍 管理员分享的AI策略数量: {len(shared_strategies)}")
            for strategy in shared_strategies:
                print(f"   - {strategy['name']} (ID: {strategy['id']}, 分享: {strategy['is_shared']})")

            conn.close()

        except Exception as e:
            print(f"❌ 原生SQL查询失败: {e}")
            return jsonify({
                'success': False,
                'error': f'数据库查询失败: {str(e)}'
            })

        # 合并策略列表（避免重复）
        all_strategies = user_strategies.copy()

        # 添加分享策略（排除用户已有的）
        user_strategy_ids = {s['id'] for s in user_strategies}
        for shared_strategy in shared_strategies:
            if shared_strategy['id'] not in user_strategy_ids:
                all_strategies.append(shared_strategy)

        # 转换为API格式
        models = []
        for strategy in all_strategies:
            try:
                # 解析性能指标（strategy现在是字典）
                import json
                performance_metrics = json.loads(strategy['performance_metrics']) if strategy['performance_metrics'] else {}
                training_results = json.loads(strategy['training_results']) if strategy['training_results'] else {}
                training_data = {}  # 暂时为空，因为数据库中training_data字段为空

                # 解析分析维度
                analysis_dimensions = []

                # 从训练数据中提取分析维度
                if training_data.get('analysis_dimensions'):
                    dimensions_config = training_data['analysis_dimensions']
                    if dimensions_config.get('technical'):
                        analysis_dimensions.append('技术指标')
                    if dimensions_config.get('fundamental'):
                        analysis_dimensions.append('基本面分析')
                    if dimensions_config.get('sentiment'):
                        analysis_dimensions.append('市场情绪')
                    if dimensions_config.get('volume'):
                        analysis_dimensions.append('成交量分析')

                if not analysis_dimensions:
                    analysis_dimensions = ['技术指标', '价格行为']

                # 计算成功率
                win_rate = performance_metrics.get('win_rate', 0.0)
                total_trades = performance_metrics.get('total_trades', 0)
                successful_predictions = int(total_trades * win_rate) if total_trades > 0 else 0

                # 从策略数据中获取时间框架
                timeframe = strategy.get('timeframe', '1d')
                timeframe_display = {
                    '1m': '1分钟',
                    '5m': '5分钟',
                    '15m': '15分钟',
                    '30m': '30分钟',
                    '1h': '1小时',
                    '4h': '4小时',
                    '1d': '1天'
                }.get(timeframe, timeframe or '1天')

                # 获取交易品种
                symbols_str = strategy.get('symbols', '["EURUSD"]')
                try:
                    symbols = json.loads(symbols_str) if symbols_str else ['EURUSD']
                except:
                    symbols = ['EURUSD']

                if isinstance(symbols, str):
                    symbols = [symbols]

                # 检查是否为分享策略
                is_shared = strategy.get('is_shared', False)

                models.append({
                    'id': f'ai_strategy_{strategy["id"]}',
                    'name': strategy['name'],
                    'description': strategy['description'] or '用户训练的AI策略模型',
                    'analysis_dimensions': analysis_dimensions,
                    'data_timeframe': timeframe_display,
                    'training_accuracy': training_results.get('training_accuracy', 0.75),
                    'validation_accuracy': training_results.get('validation_accuracy', 0.75),
                    'created_at': strategy['created_at'][:10] if strategy['created_at'] else '2024-12-15',
                    'status': 'active',
                    'total_predictions': total_trades,
                    'successful_predictions': successful_predictions,
                    'win_rate': win_rate,
                    'profit_factor': performance_metrics.get('profit_factor', 1.0),
                    'sharpe_ratio': performance_metrics.get('sharpe_ratio', 0.0),
                    'max_drawdown': performance_metrics.get('max_drawdown', 0.0),
                    'ai_model': strategy['ai_model'],
                    'timeframe': timeframe,
                    'symbols': symbols,
                    'is_shared': is_shared,
                    'owner_type': 'shared' if is_shared else 'user'
                })

            except Exception as e:
                print(f"❌ 解析AI策略数据失败 {strategy['id']}: {e}")
                import traceback
                traceback.print_exc()

                # 即使解析失败，也添加基本信息
                models.append({
                    'id': f'strategy_{strategy["id"]}',
                    'name': strategy['name'],
                    'description': strategy['description'] or '用户训练的AI策略模型',
                    'analysis_dimensions': ['技术指标', '价格行为'],
                    'data_timeframe': '1天',
                    'training_accuracy': 0.75,
                    'validation_accuracy': 0.75,
                    'created_at': strategy.created_at.strftime('%Y-%m-%d') if strategy.created_at else '2024-12-15',
                    'status': 'active',
                    'total_predictions': 0,
                    'successful_predictions': 0,
                    'win_rate': 0.0,
                    'profit_factor': 1.0,
                    'sharpe_ratio': 0.0,
                    'max_drawdown': 0.0,
                    'ai_model': strategy.ai_model or 'Unknown',
                    'timeframe': '1d',
                    'symbols': ['EURUSD'],
                    'is_shared': False,
                    'owner_type': 'user'
                })
                continue

        print(f"✅ 获取到 {len(models)} 个用户训练的AI策略模型")

        # 详细输出模型信息
        for model in models:
            print(f"   📋 模型: {model['name']} (ID: {model['id']})")

        return jsonify({
            'success': True,
            'models': models,
            'count': len(models)
        })

    except Exception as e:
        print(f"❌ 获取用户AI模型失败: {e}")
        import traceback
        traceback.print_exc()
        return jsonify({
            'success': False,
            'error': f'获取模型列表失败: {str(e)}'
        })

@app.route('/api/debug/user-info', methods=['GET'])
@login_required
def api_debug_user_info():
    """调试用户信息API"""
    try:
        # 使用Flask-Login的current_user获取用户信息
        from flask_login import current_user

        if not current_user.is_authenticated:
            return jsonify({
                'success': False,
                'error': '用户未认证'
            })

        user_id = current_user.id
        username = current_user.username

        # 获取用户的AI策略数量（使用原生SQL）
        import sqlite3
        conn = sqlite3.connect('trading_system.db')
        cursor = conn.cursor()

        cursor.execute("SELECT * FROM strategy WHERE user_id = ? AND strategy_type = ?", (user_id, 'ai'))
        user_strategies = cursor.fetchall()

        cursor.execute("SELECT * FROM strategy WHERE user_id = ? AND strategy_type = ? AND status = ?", (user_id, 'ai', 'completed'))
        completed_strategies = cursor.fetchall()

        conn.close()

        print(f"🔍 调试API - 用户 {user_id} 的策略详情:")
        print(f"   总策略数: {len(user_strategies)}")
        print(f"   已完成策略数: {len(completed_strategies)}")
        for strategy_row in user_strategies:
            # 假设字段顺序：id, user_id, name, description, strategy_type, parameters, is_active, is_shared, shared_by, shared_at, status, ...
            strategy_id, _, name, _, _, _, _, _, _, _, status = strategy_row[:11]
            created_at = strategy_row[17] if len(strategy_row) > 17 else None
            print(f"   - {name} (ID: {strategy_id}, 状态: '{status}', 创建时间: {created_at})")

        return jsonify({
            'success': True,
            'user_info': {
                'user_id': user_id,
                'username': username,
                'session_data': dict(session),
                'total_strategies': len(user_strategies),
                'completed_strategies': len(completed_strategies),
                'strategy_list': [
                    {
                        'id': s.id,
                        'name': s.name,
                        'status': s.status,
                        'created_at': s.created_at.strftime('%Y-%m-%d %H:%M:%S') if s.created_at else None
                    } for s in user_strategies
                ]
            }
        })

    except Exception as e:
        return jsonify({
            'success': False,
            'error': str(e)
        })

@app.route('/api/debug/ai-strategies', methods=['GET'])
@login_required
def api_debug_ai_strategies():
    """调试AI策略数据API"""
    try:
        # 使用Flask-Login的current_user获取用户信息
        from flask_login import current_user

        if not current_user.is_authenticated:
            return jsonify({
                'success': False,
                'error': '用户未认证'
            })

        user_id = current_user.id
        username = current_user.username
        print(f"🔍 调试策略API - 用户ID: {user_id}")
        print(f"🔍 调试策略API - 用户名: {username}")
        print(f"🔍 调试策略API - 用户认证状态: {current_user.is_authenticated}")

        # 使用原生SQL获取所有AI策略
        import sqlite3
        conn = sqlite3.connect('trading_system.db')
        cursor = conn.cursor()

        cursor.execute("SELECT * FROM strategy WHERE strategy_type = ?", ('ai',))
        all_strategies = cursor.fetchall()

        cursor.execute("SELECT * FROM strategy WHERE user_id = ? AND strategy_type = ?", (user_id, 'ai'))
        user_strategies = cursor.fetchall()

        conn.close()

        strategies_data = []
        for strategy_row in all_strategies:
            # 解析策略数据
            strategy_id, strategy_user_id, name, _, _, _, _, _, _, _, status = strategy_row[:11]
            created_at = strategy_row[17] if len(strategy_row) > 17 else None

            strategies_data.append({
                'id': strategy_id,
                'name': name,
                'user_id': strategy_user_id,
                'status': status,
                'created_at': created_at[:19] if created_at else None,
                'is_current_user': strategy_user_id == user_id
            })

        return jsonify({
            'success': True,
            'debug_info': {
                'current_user_id': user_id,
                'total_strategies': len(all_strategies),
                'user_strategies': len(user_strategies),
                'strategies': strategies_data
            }
        })

    except Exception as e:
        return jsonify({
            'success': False,
            'error': str(e)
        })

@app.route('/api/market-data', methods=['GET'])
@login_required
def api_get_market_data():
    """获取市场数据和技术指标"""
    try:
        symbol = request.args.get('symbol', 'XAUUSD')
        timeframe = request.args.get('timeframe', '15m')

        print(f"🔍 获取市场数据: {symbol}, 时间框架: {timeframe}")

        # 获取最新的K线数据
        from services.mt5_service import MT5Service
        import pandas as pd
        import numpy as np

        mt5_service = MT5Service()

        # 确保MT5已连接
        if not mt5_service.connected:
            print("🔗 MT5未连接，尝试连接...")
            if not mt5_service.connect():
                print("❌ MT5连接失败")
                return jsonify({
                    'success': False,
                    'error': 'MT5连接失败'
                })

        # 获取历史数据用于计算技术指标
        bars = mt5_service.get_historical_data(symbol, timeframe, 200)  # 获取200根K线

        if not bars or len(bars) < 50:
            print(f"❌ 获取历史数据失败或数据不足: {len(bars) if bars else 0}")
            return jsonify({
                'success': False,
                'error': '无法获取足够的历史数据'
            })

        # 转换为DataFrame进行技术指标计算
        df = pd.DataFrame(bars)
        print(f"✅ 转换为DataFrame，数据形状: {df.shape}")
        print(f"📊 数据列: {list(df.columns)}")
        print(f"📊 最新价格: {df['close'].iloc[-1]:.2f}")

        # 移动平均线
        df['ma20'] = df['close'].rolling(window=20).mean()
        df['ma50'] = df['close'].rolling(window=50).mean()
        df['ma200'] = df['close'].rolling(window=200).mean()

        # RSI
        delta = df['close'].diff()
        gain = (delta.where(delta > 0, 0)).rolling(window=14).mean()
        loss = (-delta.where(delta < 0, 0)).rolling(window=14).mean()
        rs = gain / loss
        df['rsi'] = 100 - (100 / (1 + rs))

        # 随机指标
        df['lowest_low'] = df['low'].rolling(window=14).min()
        df['highest_high'] = df['high'].rolling(window=14).max()
        df['stoch_k'] = 100 * (df['close'] - df['lowest_low']) / (df['highest_high'] - df['lowest_low'])
        df['stoch_d'] = df['stoch_k'].rolling(window=3).mean()

        # MACD
        exp1 = df['close'].ewm(span=12).mean()
        exp2 = df['close'].ewm(span=26).mean()
        df['macd'] = exp1 - exp2
        df['macd_signal'] = df['macd'].ewm(span=9).mean()

        # 成交量移动平均
        df['volume_ma'] = df['volume'].rolling(window=20).mean()

        # ATR
        df['tr'] = np.maximum(df['high'] - df['low'],
                             np.maximum(abs(df['high'] - df['close'].shift(1)),
                                       abs(df['low'] - df['close'].shift(1))))
        df['atr'] = df['tr'].rolling(window=14).mean()

        # 获取最新值
        latest = df.iloc[-1]

        # 安全获取时间戳
        timestamp = latest.get('time')
        if timestamp and hasattr(timestamp, 'isoformat'):
            timestamp_str = timestamp.isoformat()
        else:
            from datetime import datetime
            timestamp_str = datetime.now().isoformat()

        market_data = {
            'symbol': symbol,
            'timeframe': timeframe,
            'timestamp': timestamp_str,
            'close': float(latest['close']),
            'volume': float(latest['volume']),
            'ma20': float(latest['ma20']) if not pd.isna(latest['ma20']) else None,
            'ma50': float(latest['ma50']) if not pd.isna(latest['ma50']) else None,
            'ma200': float(latest['ma200']) if not pd.isna(latest['ma200']) else None,
            'rsi': float(latest['rsi']) if not pd.isna(latest['rsi']) else None,
            'stoch_k': float(latest['stoch_k']) if not pd.isna(latest['stoch_k']) else None,
            'stoch_d': float(latest['stoch_d']) if not pd.isna(latest['stoch_d']) else None,
            'macd': float(latest['macd']) if not pd.isna(latest['macd']) else None,
            'macd_signal': float(latest['macd_signal']) if not pd.isna(latest['macd_signal']) else None,
            'volume_ma': float(latest['volume_ma']) if not pd.isna(latest['volume_ma']) else None,
            'atr': float(latest['atr']) if not pd.isna(latest['atr']) else None
        }

        print(f"✅ 计算完成技术指标: RSI={market_data['rsi']:.2f}, MACD={market_data['macd']:.4f}")

        return jsonify({
            'success': True,
            **market_data
        })

    except Exception as e:
        print(f"❌ 获取市场数据失败: {str(e)}")
        return jsonify({
            'success': False,
            'error': f'获取市场数据失败: {str(e)}'
        })

@app.route('/api/ai-models/predict', methods=['POST'])
@login_required
def api_ai_model_predict():
    """AI模型预测API"""
    try:
        data = request.json
        user_id = session.get('user_id')

        # 验证必要参数
        required_fields = ['model_id', 'symbol', 'timeframe']
        for field in required_fields:
            if field not in data:
                return jsonify({
                    'success': False,
                    'error': f'缺少必要参数: {field}'
                })

        model_id = data['model_id']
        symbol = data['symbol']
        timeframe = data['timeframe']
        input_data = data.get('input_data', {})

        print(f"🔍 AI模型预测请求 - 模型ID: {model_id} (类型: {type(model_id)})")
        print(f"🔍 交易品种: {symbol}, 时间框架: {timeframe}")

        # 验证model_id不为空
        if not model_id:
            print("❌ 模型ID为空")
            return jsonify({
                'success': False,
                'error': '模型ID不能为空'
            })

        # 从model_id中提取strategy_id（现在只支持strategy_前缀）
        strategy_id = None

        if str(model_id).startswith('strategy_'):
            strategy_id_str = str(model_id).replace('strategy_', '')
            print(f"🔍 提取的策略ID字符串: {strategy_id_str}")
            try:
                strategy_id = int(strategy_id_str)
                print(f"🔍 转换后的策略ID: {strategy_id}")
            except ValueError as e:
                print(f"❌ 策略ID转换失败: {e}")
                return jsonify({
                    'success': False,
                    'error': f'无效的策略ID格式: {strategy_id_str}'
                })
        elif str(model_id).startswith('ai_strategy_'):
            # 兼容旧格式，但统一处理
            strategy_id_str = str(model_id).replace('ai_strategy_', '')
            print(f"🔍 提取的策略ID字符串(兼容旧格式): {strategy_id_str}")
            try:
                strategy_id = int(strategy_id_str)
                print(f"🔍 转换后的策略ID: {strategy_id}")
            except ValueError as e:
                print(f"❌ 策略ID转换失败: {e}")
                return jsonify({
                    'success': False,
                    'error': f'无效的策略ID格式: {strategy_id_str}'
                })
        else:
            print(f"❌ 无效的模型ID格式: {model_id}")
            return jsonify({
                'success': False,
                'error': f'无效的模型ID格式: {model_id}，期望格式: strategy_数字'
            })

        # 获取AI策略信息（使用原生SQL避免SQLAlchemy缓存问题）
        import sqlite3

        try:
            conn = sqlite3.connect('trading_system.db')
            cursor = conn.cursor()

            # 查询strategy表中的AI策略
            cursor.execute("""
                SELECT id, user_id, name, description, strategy_type, parameters,
                       is_active, is_shared, shared_by, shared_at, status,
                       training_results, performance_metrics, training_data,
                       ai_model, timeframe, symbols, created_at, updated_at
                FROM strategy
                WHERE id = ? AND strategy_type = ?
            """, (strategy_id, 'ai'))

            row = cursor.fetchone()
            conn.close()

            if not row:
                return jsonify({
                    'success': False,
                    'error': '未找到指定的AI策略模型'
                })

            # 将查询结果转换为策略对象格式
            strategy = type('Strategy', (), {
                'id': row[0],
                'user_id': row[1],
                'name': row[2],
                'description': row[3],
                'strategy_type': row[4],
                'parameters': row[5],
                'is_active': row[6],
                'is_shared': row[7],
                'shared_by': row[8],
                'shared_at': row[9],
                'status': row[10],
                'training_results': row[11],
                'performance_metrics': row[12],
                'training_data': row[13],
                'ai_model': row[14],
                'timeframe': row[15],
                'symbols': row[16],
                'created_at': row[17],
                'updated_at': row[18]
            })()

            # 添加辅助方法
            def get_performance_metrics():
                import json
                return json.loads(strategy.performance_metrics) if strategy.performance_metrics else {}

            def get_training_results():
                import json
                return json.loads(strategy.training_results) if strategy.training_results else {}

            def get_parameters():
                import json
                return json.loads(strategy.parameters) if strategy.parameters else {}

            def get_training_data():
                import json
                return json.loads(strategy.training_data) if strategy.training_data else {}

            def can_access(user):
                # 策略创建者可以访问
                if strategy.user_id == user.id:
                    return True
                # 管理员分享的策略所有用户都可以访问
                if strategy.is_shared:
                    return True
                # 管理员可以访问所有策略
                if user.user_type == 'admin':
                    return True
                return False

            strategy.get_performance_metrics = get_performance_metrics
            strategy.get_training_results = get_training_results
            strategy.get_parameters = get_parameters
            strategy.get_training_data = get_training_data
            strategy.can_access = can_access

        except Exception as e:
            print(f"❌ 原生SQL查询失败: {e}")
            return jsonify({
                'success': False,
                'error': f'数据库查询失败: {str(e)}'
            })

        if not strategy:
            return jsonify({
                'success': False,
                'error': '未找到指定的AI策略模型'
            })

        # 检查权限：使用策略的权限检查方法
        if not strategy.can_access(current_user):
            print(f"❌ 用户 {user_id} 无权限访问策略 {strategy_id}")
            print(f"   策略所有者: {strategy.user_id}")
            print(f"   是否分享: {getattr(strategy, 'is_shared', False)}")
            print(f"   用户类型: {current_user.user_type}")
            return jsonify({
                'success': False,
                'error': '无权限访问该AI策略模型'
            })

        # 检查策略状态
        if strategy.status != 'completed':
            return jsonify({
                'success': False,
                'error': f'AI策略模型状态异常: {strategy.status}'
            })

        # 使用AI策略服务进行预测
        try:
            from services.ai_strategy_service import ai_strategy_service

            # 构建预测请求
            prediction_request = {
                'symbol': symbol,
                'timeframe': timeframe,
                'input_data': input_data,
                'strategy_config': strategy.get_parameters(),
                'performance_metrics': strategy.get_performance_metrics()
            }

            # 调用策略预测
            prediction_result = ai_strategy_service.execute_strategy_prediction(
                strategy_id=strategy_id,
                prediction_request=prediction_request
            )

            if prediction_result and prediction_result.get('success'):
                return jsonify({
                    'success': True,
                    'prediction': prediction_result.get('prediction', {}),
                    'model_info': {
                        'strategy_name': strategy.name,
                        'ai_model': strategy.ai_model,
                        'timeframe': strategy.timeframe,
                        'source': 'user_trained_strategy'
                    }
                })
            else:
                # 策略预测失败，不使用降级预测
                error_msg = prediction_result.get('error', '策略预测失败')
                return jsonify({
                    'success': False,
                    'error': f'AI策略预测失败: {error_msg}，系统要求必须使用真实AI模型'
                })

        except Exception as e:
            print(f"❌ 策略预测服务异常: {e}")

            # 不使用降级预测，直接返回错误
            return jsonify({
                'success': False,
                'error': f'AI策略预测异常: {str(e)}，系统要求必须使用真实数据和真实AI模型'
            })

    except Exception as e:
        print(f"❌ AI模型预测失败: {e}")
        return jsonify({
            'success': False,
            'error': f'预测服务异常: {str(e)}'
        })

def generate_mock_user_models():
    """生成模拟用户模型数据"""
    return [
        {
            'id': 'user_model_001',
            'name': '黄金趋势预测模型V1.2',
            'description': '基于技术指标和价格行为训练的黄金趋势预测模型',
            'analysis_dimensions': ['技术指标', '价格行为', '成交量分析'],
            'data_timeframe': '15分钟',
            'training_accuracy': 0.78,
            'validation_accuracy': 0.75,
            'created_at': '2024-12-10',
            'status': 'active',
            'total_predictions': 1250,
            'successful_predictions': 945
        },
        {
            'id': 'user_model_002',
            'name': '多品种动量策略模型',
            'description': '支持多个交易品种的动量策略预测模型',
            'analysis_dimensions': ['动量指标', '相对强弱', '市场情绪'],
            'data_timeframe': '5分钟',
            'training_accuracy': 0.82,
            'validation_accuracy': 0.79,
            'created_at': '2024-12-08',
            'status': 'active',
            'total_predictions': 2100,
            'successful_predictions': 1659
        },
        {
            'id': 'user_model_003',
            'name': '反转信号识别模型',
            'description': '专门识别价格反转信号的深度学习模型',
            'analysis_dimensions': ['反转形态', 'RSI背离', '支撑阻力'],
            'data_timeframe': '1小时',
            'training_accuracy': 0.73,
            'validation_accuracy': 0.71,
            'created_at': '2024-12-05',
            'status': 'active',
            'total_predictions': 890,
            'successful_predictions': 632
        }
    ]

def generate_strategy_based_prediction(strategy, symbol, timeframe, input_data=None):
    """基于策略特征和真实市场数据生成智能预测"""
    import json
    import math

    try:
        print(f"🔍 生成基于策略的预测 - 策略: {strategy.name}, 品种: {symbol}, 时间框架: {timeframe}")

        # 获取策略性能指标
        performance_metrics = strategy.get_performance_metrics()
        training_results = strategy.get_training_results()

        # 基于策略历史表现调整预测质量
        win_rate = performance_metrics.get('win_rate', 0.5)
        profit_factor = performance_metrics.get('profit_factor', 1.0)
        sharpe_ratio = performance_metrics.get('sharpe_ratio', 0.0)

        print(f"📊 策略性能指标 - 胜率: {win_rate:.2%}, 盈利因子: {profit_factor:.2f}, 夏普比率: {sharpe_ratio:.2f}")

        # 计算策略质量分数
        quality_score = (win_rate * 0.4 +
                        min(profit_factor / 2.0, 1.0) * 0.3 +
                        min(sharpe_ratio / 2.0, 1.0) * 0.3)

        # 基于策略质量调整预测置信度
        base_confidence = 0.5 + quality_score * 0.3

        # 分析真实市场数据（如果提供）
        direction_bias = 0.5  # 默认中性
        market_signal_strength = 0.0

        if input_data and input_data.get('technical_indicators'):
            print("📈 分析真实技术指标数据...")
            indicators = input_data['technical_indicators']

            # 基于RSI分析
            rsi = indicators.get('rsi', 50)
            if rsi > 70:
                direction_bias -= 0.2  # 超买，偏向看跌
                market_signal_strength += 0.1
            elif rsi < 30:
                direction_bias += 0.2  # 超卖，偏向看涨
                market_signal_strength += 0.1

            # 基于MACD分析
            macd_line = indicators.get('macd_line', 0)
            macd_signal = indicators.get('macd_signal', 0)
            if macd_line > macd_signal:
                direction_bias += 0.1  # MACD金叉，偏向看涨
                market_signal_strength += 0.05
            elif macd_line < macd_signal:
                direction_bias -= 0.1  # MACD死叉，偏向看跌
                market_signal_strength += 0.05

            # 基于移动平均线分析
            sma_5 = indicators.get('sma_5', 0)
            sma_20 = indicators.get('sma_20', 0)
            if sma_5 > sma_20 and sma_5 > 0 and sma_20 > 0:
                direction_bias += 0.15  # 短期均线在长期均线上方，看涨
                market_signal_strength += 0.1
            elif sma_5 < sma_20 and sma_5 > 0 and sma_20 > 0:
                direction_bias -= 0.15  # 短期均线在长期均线下方，看跌
                market_signal_strength += 0.1

            # 基于布林带分析
            current_price = input_data.get('price_data', {}).get('close', 0)
            bollinger_upper = indicators.get('bollinger_upper', 0)
            bollinger_lower = indicators.get('bollinger_lower', 0)

            if current_price > 0 and bollinger_upper > 0 and bollinger_lower > 0:
                if current_price > bollinger_upper:
                    direction_bias -= 0.1  # 价格突破上轨，可能回调
                    market_signal_strength += 0.05
                elif current_price < bollinger_lower:
                    direction_bias += 0.1  # 价格跌破下轨，可能反弹
                    market_signal_strength += 0.05

            print(f"📊 技术指标分析结果 - RSI: {rsi:.1f}, 方向偏向: {direction_bias:.3f}, 信号强度: {market_signal_strength:.3f}")

        # 结合策略历史表现调整方向偏向
        if win_rate > 0.6:
            direction_bias += (win_rate - 0.6) * 0.3  # 减少随机性，更多基于数据

        if profit_factor > 1.2:
            direction_bias += (profit_factor - 1.2) * 0.05  # 减少影响，更多基于技术指标

        # 限制方向偏向在合理范围内
        direction_bias = max(0.1, min(0.9, direction_bias))

        # 基于分析结果确定方向（使用确定性算法而非随机）
        direction = 'bullish' if direction_bias > 0.5 else 'bearish'

        # 调整置信度基于市场信号强度
        confidence = base_confidence + market_signal_strength
        confidence = max(0.4, min(0.9, confidence))

        # 预测分数基于策略验证精度和市场信号强度
        validation_accuracy = training_results.get('validation_accuracy', 0.75)
        prediction_score = validation_accuracy + market_signal_strength * 0.1
        prediction_score = max(0.5, min(0.95, prediction_score))

        # 强度基于置信度和策略质量
        if confidence > 0.8 and quality_score > 0.7:
            strength = 'strong'
        elif confidence > 0.65 and quality_score > 0.5:
            strength = 'medium'
        else:
            strength = 'weak'

        # 生成基于策略特征的推理
        reasoning_parts = []
        reasoning_parts.append(f"{strategy.name}预测")

        if win_rate > 0.6:
            reasoning_parts.append(f"历史胜率{win_rate:.1%}")

        if profit_factor > 1.2:
            reasoning_parts.append(f"盈利因子{profit_factor:.2f}")

        # 添加分析维度信息
        try:
            training_data = json.loads(strategy.training_data) if strategy.training_data else {}
            if training_data.get('analysis_dimensions'):
                dimensions = []
                dims_config = training_data['analysis_dimensions']
                if dims_config.get('technical_indicators'):
                    dimensions.append('技术指标')
                if dims_config.get('price_action'):
                    dimensions.append('价格行为')
                if dims_config.get('volume_analysis'):
                    dimensions.append('成交量')

                if dimensions:
                    reasoning_parts.append(f"基于{'+'.join(dimensions)}分析")
        except:
            reasoning_parts.append("基于多维度分析")

        reasoning = f"{': '.join(reasoning_parts)}，{direction}信号"

        signals = [{
            'symbol': symbol,
            'direction': direction,
            'confidence': confidence,
            'prediction_score': prediction_score,
            'reasoning': reasoning,
            'strength': strength,
            'strategy_quality_score': quality_score,
            'historical_win_rate': win_rate,
            'profit_factor': profit_factor
        }]

        return {'signals': signals}

    except Exception as e:
        print(f"❌ 生成策略预测失败: {e}")
        # 回退到简单预测
        return generate_simple_prediction(symbol, timeframe)

def generate_simple_prediction(symbol, timeframe):
    """生成简单预测数据（备用）"""
    import random

    directions = ['bullish', 'bearish']
    direction = random.choice(directions)
    confidence = 0.6 + random.random() * 0.3  # 0.6-0.9
    prediction_score = 0.5 + random.random() * 0.4  # 0.5-0.9

    signals = [{
        'symbol': symbol,
        'direction': direction,
        'confidence': confidence,
        'prediction_score': prediction_score,
        'reasoning': f'AI模型预测: {direction}信号，基于技术指标和价格行为分析',
        'strength': 'strong' if confidence > 0.8 else 'medium' if confidence > 0.65 else 'weak'
    }]

    return {'signals': signals}

# SN加仓策略页面已删除

@app.route('/api/sn-strategy/analyze-trend', methods=['POST'])
@login_required
def analyze_sn_trend():
    """分析白天趋势"""
    try:
        data = request.get_json()
        symbol = data.get('symbol', 'XAUUSD')

        # 获取白天的价格数据进行趋势分析
        from services.mt5_service import mt5_service

        # 获取今天的价格数据进行真实趋势分析
        import pandas as pd
        from datetime import datetime, timedelta
        import MetaTrader5 as mt5

        # 确保MT5连接
        if not mt5_service.connected:
            if not mt5_service.connect():
                return jsonify({
                    'success': False,
                    'error': 'MT5连接失败，无法获取真实数据'
                })

        try:
            # 获取今天从亚洲交易时间段早上7点到现在的1小时K线数据
            end_time = datetime.now()
            start_time = end_time.replace(hour=7, minute=0, second=0, microsecond=0)

            # 如果当前时间早于7点，则使用昨天7点到现在
            if end_time.hour < 7:
                start_time = start_time - timedelta(days=1)

            # 计算需要获取的K线数量（从7点到现在的小时数）
            time_diff = end_time - start_time
            hours_count = max(1, int(time_diff.total_seconds() / 3600) + 1)

            print(f"📊 分析 {symbol} 亚洲交易时段趋势，时间段: {start_time.strftime('%m-%d %H:%M')} - {end_time.strftime('%m-%d %H:%M')}")
            print(f"   获取 {hours_count} 根1小时K线数据")

            # 获取1小时K线数据
            price_data = mt5_service.get_price_data(symbol, timeframe=mt5.TIMEFRAME_H1, count=hours_count)

            if price_data is not None and len(price_data) > 0:
                print(f"✅ 成功获取 {len(price_data)} 根K线数据")

                # 计算趋势
                first_price = price_data.iloc[0]['close']
                last_price = price_data.iloc[-1]['close']
                high_price = price_data['high'].max()
                low_price = price_data['low'].min()

                price_change = last_price - first_price
                change_percent = (price_change / first_price) * 100
                volatility = ((high_price - low_price) / first_price) * 100

                trend = 'up' if price_change > 0 else 'down'

                # 基于价格变化幅度和波动性计算置信度
                confidence = min(95, max(60, abs(change_percent) * 15 + volatility * 5 + 65))

                print(f"📈 亚洲交易时段趋势分析结果:")
                print(f"   开盘价(7:00): {first_price}")
                print(f"   当前价格: {last_price}")
                print(f"   价格变化: {price_change} ({change_percent:.2f}%)")
                print(f"   最高价: {high_price}")
                print(f"   最低价: {low_price}")
                print(f"   波动率: {volatility:.2f}%")
                print(f"   趋势方向: {trend}")
                print(f"   置信度: {confidence:.1f}%")

                return jsonify({
                    'success': True,
                    'trend': trend,
                    'confidence': round(confidence, 1),
                    'price_change': round(price_change, 5),
                    'change_percent': round(change_percent, 2),
                    'volatility': round(volatility, 2),
                    'high_price': round(high_price, 5),
                    'low_price': round(low_price, 5),
                    'data_points': len(price_data),
                    'analysis_period': f"{start_time.strftime('%m-%d %H:%M')} - {end_time.strftime('%m-%d %H:%M')}",
                    'trading_session': '亚洲交易时段',
                    'data_source': 'MT5真实数据'
                })
            else:
                print(f"❌ 无法获取 {symbol} 的价格数据")
                return jsonify({
                    'success': False,
                    'error': f'无法获取{symbol}的真实价格数据，请检查MT5连接和品种设置'
                })

        except Exception as e:
            print(f"❌ 趋势分析异常: {e}")
            return jsonify({
                'success': False,
                'error': f'趋势分析失败: {str(e)}'
            })

    except Exception as e:
        return jsonify({
            'success': False,
            'error': str(e)
        })

@app.route('/api/sn-strategy/get-price', methods=['POST'])
@login_required
def get_sn_price():
    """获取当前价格"""
    try:
        data = request.get_json()
        symbol = data.get('symbol', 'XAUUSD')

        from services.mt5_service import mt5_service

        print(f"📊 获取 {symbol} 当前价格...")

        # 确保MT5连接
        if not mt5_service.connected:
            print(f"⚠️ MT5未连接，尝试重新连接...")
            if not mt5_service.connect():
                return jsonify({
                    'success': False,
                    'error': 'MT5连接失败，无法获取价格数据'
                })

        # 获取当前价格
        tick = mt5_service.get_symbol_tick(symbol)

        if tick:
            print(f"✅ 成功获取 {symbol} 价格: bid={tick['bid']}, ask={tick['ask']}")
            return jsonify({
                'success': True,
                'symbol': symbol,
                'bid': tick['bid'],
                'ask': tick['ask'],
                'spread': tick['ask'] - tick['bid'],
                'time': tick['time'],
                'last': tick.get('last', tick['bid'])
            })
        else:
            print(f"❌ 无法获取 {symbol} 价格数据")
            return jsonify({
                'success': False,
                'error': f'无法获取{symbol}价格数据，请检查MT5连接和品种设置'
            })

    except Exception as e:
        print(f"❌ 获取价格异常: {e}")
        return jsonify({
            'success': False,
            'error': f'获取价格失败: {str(e)}'
        })

@app.route('/api/sn-strategy/execute', methods=['POST'])
@login_required
def execute_sn_strategy():
    """执行SN加仓策略"""
    try:
        data = request.get_json()

        config = {
            'day_trend': data.get('dayTrend'),
            'trading_symbol': data.get('tradingSymbol'),
            'trading_direction': data.get('tradingDirection'),
            'user_id': current_user.id,
            'parameters': data.get('parameters', [])  # 传递前端配置的参数
        }

        # 验证配置
        if not all([config['day_trend'], config['trading_symbol'], config['trading_direction']]):
            return jsonify({
                'success': False,
                'error': '配置参数不完整'
            })

        # 使用SN策略管理器执行
        from services.sn_strategy_manager import sn_strategy_manager

        result = sn_strategy_manager.start_sn_strategy(current_user.id, config)

        return jsonify(result)

    except Exception as e:
        return jsonify({
            'success': False,
            'error': str(e)
        })

@app.route('/api/sn-strategy/status/<strategy_id>', methods=['GET'])
@login_required
def get_sn_strategy_status(strategy_id):
    """获取SN策略状态"""
    try:
        from services.sn_strategy_manager import sn_strategy_manager

        strategy = sn_strategy_manager.get_strategy_status(strategy_id)

        if strategy:
            return jsonify({
                'success': True,
                'strategy': strategy
            })
        else:
            return jsonify({
                'success': False,
                'error': '策略不存在'
            })

    except Exception as e:
        return jsonify({
            'success': False,
            'error': str(e)
        })

@app.route('/api/sn-strategy/stop/<strategy_id>', methods=['POST'])
@login_required
def stop_sn_strategy(strategy_id):
    """停止SN策略"""
    try:
        from services.sn_strategy_manager import sn_strategy_manager

        result = sn_strategy_manager.stop_strategy(strategy_id)

        return jsonify(result)

    except Exception as e:
        return jsonify({
            'success': False,
            'error': str(e)
        })

@app.route('/api/sn-strategy/list', methods=['GET'])
@login_required
def list_sn_strategies():
    """获取用户的所有SN策略"""
    try:
        from services.sn_strategy_manager import sn_strategy_manager

        all_strategies = sn_strategy_manager.get_all_active_strategies()
        user_strategies = [s for s in all_strategies if s['user_id'] == current_user.id]

        return jsonify({
            'success': True,
            'strategies': user_strategies
        })

    except Exception as e:
        return jsonify({
            'success': False,
            'error': str(e)
        })

def execute_sn_position_adding(config):
    """执行SN加仓策略的核心逻辑"""
    try:
        from services.mt5_service import mt5_service
        import MetaTrader5 as mt5

        symbol = config['trading_symbol']
        direction = config['trading_direction']

        print(f"🚀 开始执行SN加仓策略:")
        print(f"   交易品种: {symbol}")
        print(f"   交易方向: {direction}")

        # 确保MT5连接
        if not mt5_service.connected:
            print(f"⚠️ MT5未连接，尝试重新连接...")
            if not mt5_service.connect():
                return {'success': False, 'error': 'MT5连接失败，无法执行SN加仓策略'}

        # SN加仓策略的3组配置
        groups = [
            {'name': '第1组', 'orders': [1, 2], 'stop_loss': 5, 'take_profit': 10, 'volume': 0.01},
            {'name': '第2组', 'orders': [3, 4], 'stop_loss': 6, 'take_profit': 15, 'volume': 0.01},
            {'name': '第3组', 'orders': [5, 6], 'stop_loss': 8, 'take_profit': 30, 'volume': 0.01}
        ]

        executed_orders = []

        # 获取当前价格
        print(f"📊 获取 {symbol} 当前价格...")
        tick = mt5_service.get_symbol_tick(symbol)
        if not tick:
            print(f"❌ 无法获取 {symbol} 当前价格")
            return {'success': False, 'error': f'无法获取{symbol}当前价格，请检查MT5连接和品种设置'}

        print(f"✅ 当前价格: bid={tick['bid']}, ask={tick['ask']}")

        current_price = tick['ask'] if direction == 'up' else tick['bid']

        # 执行所有订单
        for group in groups:
            for order_num in group['orders']:
                # 计算止损止盈价格
                if direction == 'up':  # 买入
                    stop_loss = current_price - (group['stop_loss'] * 0.0001)  # 假设是外汇，1点=0.0001
                    take_profit = current_price + (group['take_profit'] * 0.0001)
                    order_type = mt5.ORDER_TYPE_BUY
                else:  # 卖出
                    stop_loss = current_price + (group['stop_loss'] * 0.0001)
                    take_profit = current_price - (group['take_profit'] * 0.0001)
                    order_type = mt5.ORDER_TYPE_SELL

                # 如果是黄金，调整点值
                if 'XAU' in symbol:
                    if direction == 'up':
                        stop_loss = current_price - (group['stop_loss'] * 0.1)
                        take_profit = current_price + (group['take_profit'] * 0.1)
                    else:
                        stop_loss = current_price + (group['stop_loss'] * 0.1)
                        take_profit = current_price - (group['take_profit'] * 0.1)

                # 发送订单
                result = mt5_service.send_order(
                    symbol=symbol,
                    order_type=order_type,
                    volume=group['volume'],
                    price=None,  # 市价单
                    sl=stop_loss,
                    tp=take_profit,
                    comment=f"SN加仓-{group['name']}-订单{order_num}"
                )

                if result['success']:
                    executed_orders.append({
                        'order_id': order_num,
                        'group': group['name'],
                        'direction': direction,
                        'volume': group['volume'],
                        'stop_loss': stop_loss,
                        'take_profit': take_profit,
                        'mt5_order_id': result.get('order_id'),
                        'status': 'executed'
                    })
                else:
                    return {
                        'success': False,
                        'error': f"订单{order_num}执行失败: {result.get('error', '未知错误')}"
                    }

        return {
            'success': True,
            'message': 'SN加仓策略执行成功',
            'executed_orders': executed_orders,
            'total_orders': len(executed_orders),
            'total_volume': sum(order['volume'] for order in executed_orders)
        }

    except Exception as e:
        return {
            'success': False,
            'error': f'SN加仓策略执行失败: {str(e)}'
        }

@app.route('/api/sidebar-modules', methods=['GET', 'POST'])
@login_required
def sidebar_modules():
    """左侧栏模块显示控制API"""
    if request.method == 'GET':
        # 获取用户的左侧栏模块设置
        user_id = current_user.id
        settings_key = f'sidebar_modules_{user_id}'

        # 从数据库或缓存中获取设置（这里简化为返回默认设置）
        default_settings = {
            'module_demo_trading': True,
            'module_real_trading': True,
            'module_agent_trading': True,
            'module_pattern_monitoring': True,
            'module_risk_events': True,
            'module_support_resistance': True,
            'module_market_analysis': True,
            'module_ai_models': True,
            'module_backtesting': True,
            'module_portfolio': True,
            'module_news_analysis': True,
            'module_settings': True,
            'module_user_management': True
        }

        return jsonify({'success': True, 'settings': default_settings})

    elif request.method == 'POST':
        # 保存左侧栏模块设置
        data = request.get_json()
        user_id = current_user.id

        try:
            # 这里可以保存到数据库，现在先记录日志
            print(f"用户 {user_id} 保存左侧栏模块设置: {data}")

            # 可以添加数据库保存逻辑
            # UserSettings.query.filter_by(user_id=user_id, key='sidebar_modules').first()
            # 或者保存到用户配置表

            return jsonify({
                'success': True,
                'message': '左侧栏模块设置已保存',
                'settings': data
            })

        except Exception as e:
            print(f"保存左侧栏模块设置失败: {e}")
            return jsonify({
                'success': False,
                'error': '保存失败，请重试'
            }), 500

# ==================== MT5形态分析API ====================

# 多时间周期形态分析服务将在需要时初始化
multi_timeframe_service = None

@app.route('/api/mt5-pattern-analysis', methods=['POST'])
@login_required
def api_mt5_pattern_analysis():
    """MT5多时间周期形态分析API"""
    try:
        data = request.get_json()
        symbol = data.get('symbol', 'XAUUSD')
        bars = int(data.get('bars', 500))
        confidence_threshold = float(data.get('confidence_threshold', 0.6))
        timeframes = data.get('timeframes', ['5m', '15m', '30m', '1h'])  # 获取前端选择的时间周期

        logger.info(f"开始MT5形态分析: {symbol}, bars={bars}, confidence={confidence_threshold}, timeframes={timeframes}")

        # 动态导入服务
        try:
            from services.multi_timeframe_pattern_service import MultiTimeframePatternService
            service = MultiTimeframePatternService()
        except Exception as e:
            logger.error(f"初始化多时间周期服务失败: {e}")
            return jsonify({
                'success': False,
                'error': f'服务初始化失败: {str(e)}'
            })

        # 执行多时间周期分析
        results = service.analyze_all_timeframes(
            symbol=symbol,
            bars=bars,
            confidence_threshold=confidence_threshold,
            timeframes=timeframes
        )

        if 'error' in results:
            logger.error(f"MT5分析返回错误: {results['error']}")
            return jsonify({
                'success': False,
                'error': results['error']
            })

        # 确保数据可以JSON序列化
        try:
            from services.data_converter import DataConverter
            cleaned_results = DataConverter.convert_analysis_results(results)
        except Exception as e:
            logger.error(f"数据转换失败: {e}")
            # 如果转换失败，返回基础信息
            cleaned_results = {
                'summary': results.get('summary', {}),
                'symbol': results.get('symbol', symbol),
                'timestamp': results.get('timestamp', datetime.now().isoformat())
            }

        logger.info(f"MT5分析完成: 发现{cleaned_results.get('summary', {}).get('total_patterns', 0)}个形态")

        return jsonify({
            'success': True,
            'data': cleaned_results
        })

    except Exception as e:
        import traceback
        error_details = traceback.format_exc()
        logger.error(f"MT5分析API异常: {str(e)}")
        logger.error(f"详细错误信息: {error_details}")

        return jsonify({
            'success': False,
            'error': f'分析失败: {str(e)}',
            'details': error_details if app.debug else None
        })

@app.route('/api/mt5-pattern-analysis/saved', methods=['GET'])
@login_required
def api_mt5_pattern_analysis_saved():
    """获取已保存的MT5形态分析结果"""
    try:
        symbol = request.args.get('symbol')
        analyses = multi_timeframe_service.get_saved_analyses(symbol)

        return jsonify({
            'success': True,
            'data': analyses
        })

    except Exception as e:
        return jsonify({
            'success': False,
            'error': f'获取已保存分析失败: {str(e)}'
        })

@app.route('/api/mt5-pattern-analysis/load', methods=['POST'])
@login_required
def api_mt5_pattern_analysis_load():
    """加载指定的MT5形态分析结果"""
    try:
        data = request.get_json()
        filepath = data.get('filepath')

        if not filepath:
            return jsonify({
                'success': False,
                'error': '缺少文件路径参数'
            })

        analysis = multi_timeframe_service.load_analysis(filepath)

        if analysis is None:
            return jsonify({
                'success': False,
                'error': '加载分析文件失败'
            })

        return jsonify({
            'success': True,
            'data': analysis
        })

    except Exception as e:
        return jsonify({
            'success': False,
            'error': f'加载分析失败: {str(e)}'
        })

@app.route('/api/mt5-pattern-signals', methods=['GET'])
@login_required
def api_mt5_pattern_signals():
    """获取MT5形态信号用于智能综合交易系统"""
    try:
        symbol = request.args.get('symbol', 'XAUUSD')
        timeframe = request.args.get('timeframe', '15m')

        # 快速分析单个时间周期获取信号
        from services.mt5_pattern_analyzer import MT5PatternAnalyzer

        analyzer = MT5PatternAnalyzer()
        if not analyzer.connect_mt5():
            return jsonify({
                'success': False,
                'error': 'MT5连接失败'
            })

        # 获取数据
        df = analyzer.get_historical_data(symbol, timeframe, 200)
        if df is None:
            return jsonify({
                'success': False,
                'error': '无法获取市场数据'
            })

        # 识别形态
        patterns = []

        # 头肩形态
        hs_top = analyzer.identify_head_shoulders_top(df)
        hs_bottom = analyzer.identify_head_shoulders_bottom(df)
        patterns.extend(hs_top)
        patterns.extend(hs_bottom)

        # 双顶双底
        double_top = analyzer.identify_double_top(df)
        double_bottom = analyzer.identify_double_bottom(df)
        patterns.extend(double_top)
        patterns.extend(double_bottom)

        # 三角形
        triangles = analyzer.identify_triangles(df)
        patterns.extend(triangles)

        # 过滤高置信度形态
        high_confidence_patterns = [p for p in patterns if p.get('confidence', 0) >= 0.7]

        # 转换为信号格式
        signals = []
        for pattern in high_confidence_patterns[:5]:  # 最多返回5个信号
            signal = {
                'signal_type': 'pattern',
                'pattern_type': pattern.get('pattern_type'),
                'pattern_name': pattern.get('pattern_name'),
                'direction': pattern.get('direction'),
                'strength': pattern.get('confidence', 0),
                'entry_price': pattern.get('entry_point'),
                'stop_loss': pattern.get('stop_loss'),
                'take_profit': pattern.get('take_profit'),
                'timeframe': timeframe,
                'timestamp': datetime.now().isoformat(),
                'source': 'MT5_Pattern_Analysis'
            }
            signals.append(signal)

        return jsonify({
            'success': True,
            'signals': signals,
            'total_patterns': len(patterns),
            'high_confidence_patterns': len(high_confidence_patterns)
        })

    except Exception as e:
        return jsonify({
            'success': False,
            'error': f'获取形态信号失败: {str(e)}'
        })

@app.route('/api/trading-signals/pattern', methods=['POST'])
@login_required
def api_trading_signals_pattern():
    """接收形态分析信号并转发给智能综合交易系统"""
    try:
        data = request.get_json()
        signals = data.get('signals', [])
        source = data.get('source', 'Pattern_Analysis')
        symbol = data.get('symbol', 'XAUUSD')

        print(f"📤 接收到形态信号: {len(signals)}个信号")
        print(f"   信号源: {source}")
        print(f"   交易品种: {symbol}")

        if not signals:
            print("⚠️ 没有接收到信号数据，但这可能是正常情况（没有满足条件的形态）")
            return jsonify({
                'success': True,
                'message': '没有接收到信号数据，这可能是正常情况',
                'processed_count': 0
            })

        # 处理信号并存储到数据库
        processed_signals = []

        for signal in signals:
            # 创建信号记录
            signal_record = {
                'signal_id': signal.get('id', f"pattern_{int(time.time())}"),
                'signal_type': 'pattern',
                'symbol': symbol,
                'direction': signal.get('direction', 'neutral'),
                'strength': signal.get('strength', 0.5),
                'entry_price': signal.get('entry_price'),
                'stop_loss': signal.get('stop_loss'),
                'take_profit': signal.get('take_profit'),
                'timeframe': signal.get('timeframe', '15m'),
                'pattern_type': signal.get('pattern_type'),
                'pattern_name': signal.get('pattern_name'),
                'source': source,
                'timestamp': signal.get('timestamp', datetime.now().isoformat()),
                'status': 'active'
            }

            processed_signals.append(signal_record)

        # 这里可以添加将信号存储到数据库的逻辑
        # 或者直接转发给智能综合交易系统

        logger.info(f"接收到{len(processed_signals)}个形态信号，来源: {source}")

        return jsonify({
            'success': True,
            'message': f'成功接收{len(processed_signals)}个形态信号',
            'signals_processed': len(processed_signals)
        })

    except Exception as e:
        logger.error(f"处理形态信号失败: {e}")
        return jsonify({
            'success': False,
            'error': f'处理信号失败: {str(e)}'
        })

@app.route('/api/mt5-connection-test', methods=['GET'])
@login_required
def api_mt5_connection_test():
    """MT5连接测试API"""
    try:
        from services.mt5_pattern_analyzer import MT5PatternAnalyzer

        analyzer = MT5PatternAnalyzer()

        # 测试连接
        if not analyzer.connect_mt5():
            return jsonify({
                'success': False,
                'error': 'MT5连接失败',
                'details': '请确保MT5终端已启动并已登录账户'
            })

        # 获取终端信息
        import MetaTrader5 as mt5
        terminal_info = mt5.terminal_info()
        account_info = mt5.account_info()

        # 测试获取数据
        test_data = analyzer.get_historical_data('EURUSD', '1h', 10)

        mt5.shutdown()

        return jsonify({
            'success': True,
            'terminal_info': {
                'build': terminal_info.build if terminal_info else None,
                'company': terminal_info.company if terminal_info else None,
                'connected': terminal_info.connected if terminal_info else False,
                'dlls_allowed': terminal_info.dlls_allowed if terminal_info else False,
                'trade_allowed': terminal_info.trade_allowed if terminal_info else False,
            },
            'account_info': {
                'login': account_info.login if account_info else None,
                'server': account_info.server if account_info else None,
                'currency': account_info.currency if account_info else None,
                'balance': account_info.balance if account_info else None,
            },
            'data_test': {
                'success': test_data is not None,
                'records': len(test_data) if test_data is not None else 0
            }
        })

    except Exception as e:
        logger.error(f"MT5连接测试失败: {e}", exc_info=True)
        return jsonify({
            'success': False,
            'error': f'连接测试失败: {str(e)}'
        })

@app.route('/api/mt5-pattern-analysis-simple', methods=['POST'])
@login_required
def api_mt5_pattern_analysis_simple():
    """简化的MT5形态分析API - 用于调试"""
    try:
        data = request.get_json()
        symbol = data.get('symbol', 'XAUUSD')

        logger.info(f"开始简化MT5形态分析: {symbol}")

        # 直接使用MT5进行最基础的测试
        import MetaTrader5 as mt5

        # 初始化MT5
        if not mt5.initialize():
            return jsonify({
                'success': False,
                'error': f'MT5初始化失败: {mt5.last_error()}'
            })

        # 获取基础数据
        rates = mt5.copy_rates_from_pos(symbol, mt5.TIMEFRAME_H1, 0, 50)

        if rates is None:
            mt5.shutdown()
            return jsonify({
                'success': False,
                'error': f'无法获取{symbol}的历史数据'
            })

        # 转换为简单格式
        data_points = len(rates)
        latest_price = float(rates[-1]['close'])

        # 简单的形态计数（模拟）
        patterns_found = 0  # 暂时设为0，因为这是基础测试

        mt5.shutdown()

        logger.info(f"简化分析完成: 获取{data_points}条数据，最新价格{latest_price}")

        return jsonify({
            'success': True,
            'data': {
                'symbol': symbol,
                'data_points': data_points,
                'latest_price': latest_price,
                'patterns_found': patterns_found,
                'message': f'成功获取{symbol}数据，{data_points}条记录，最新价格{latest_price}，{patterns_found}个形态'
            }
        })

    except Exception as e:
        import traceback
        error_details = traceback.format_exc()
        logger.error(f"简化MT5分析API异常: {str(e)}")
        logger.error(f"详细错误信息: {error_details}")

        return jsonify({
            'success': False,
            'error': f'简化分析失败: {str(e)}',
            'details': error_details if app.debug else None
        })

@app.route('/api/test-basic', methods=['GET', 'POST'])
@login_required
def api_test_basic():
    """最基础的测试API"""
    try:
        return jsonify({
            'success': True,
            'message': 'API基础功能正常',
            'timestamp': datetime.now().isoformat()
        })
    except Exception as e:
        return jsonify({
            'success': False,
            'error': str(e)
        })

@app.route('/api/test-mt5-basic', methods=['POST'])
@login_required
def api_test_mt5_basic():
    """最基础的MT5测试API"""
    try:
        # 不导入任何自定义模块，只使用MT5
        import MetaTrader5 as mt5

        # 测试初始化
        init_result = mt5.initialize()

        if not init_result:
            error_code = mt5.last_error()
            return jsonify({
                'success': False,
                'error': f'MT5初始化失败，错误代码: {error_code}'
            })

        # 获取终端信息
        terminal_info = mt5.terminal_info()
        account_info = mt5.account_info()

        # 关闭连接
        mt5.shutdown()

        return jsonify({
            'success': True,
            'data': {
                'mt5_initialized': True,
                'terminal_build': terminal_info.build if terminal_info else None,
                'terminal_connected': terminal_info.connected if terminal_info else None,
                'account_login': account_info.login if account_info else None,
                'message': 'MT5基础功能测试成功'
            }
        })

    except Exception as e:
        import traceback
        return jsonify({
            'success': False,
            'error': f'MT5基础测试失败: {str(e)}',
            'traceback': traceback.format_exc()
        })

@app.route('/api/test-pattern-simple', methods=['POST'])
@login_required
def api_test_pattern_simple():
    """超简化的形态分析测试"""
    try:
        data = request.get_json()
        symbol = data.get('symbol', 'XAUUSD')

        logger.info(f"开始超简化形态分析测试: {symbol}")

        # 步骤1: 测试MT5分析器导入
        try:
            from services.mt5_pattern_analyzer import MT5PatternAnalyzer
            logger.info("✅ MT5PatternAnalyzer导入成功")
        except Exception as e:
            return jsonify({
                'success': False,
                'error': f'MT5PatternAnalyzer导入失败: {str(e)}'
            })

        # 步骤2: 测试分析器实例化
        try:
            analyzer = MT5PatternAnalyzer()
            logger.info("✅ MT5PatternAnalyzer实例化成功")
        except Exception as e:
            return jsonify({
                'success': False,
                'error': f'MT5PatternAnalyzer实例化失败: {str(e)}'
            })

        # 步骤3: 测试MT5连接
        try:
            if not analyzer.connect_mt5():
                return jsonify({
                    'success': False,
                    'error': 'MT5连接失败'
                })
            logger.info("✅ MT5连接成功")
        except Exception as e:
            return jsonify({
                'success': False,
                'error': f'MT5连接异常: {str(e)}'
            })

        # 步骤4: 测试数据获取
        try:
            df = analyzer.get_historical_data(symbol, '1h', 20)
            if df is None:
                return jsonify({
                    'success': False,
                    'error': f'无法获取{symbol}的历史数据'
                })
            logger.info(f"✅ 数据获取成功: {len(df)}条")
        except Exception as e:
            return jsonify({
                'success': False,
                'error': f'数据获取异常: {str(e)}'
            })

        # 步骤5: 测试简单形态识别
        try:
            patterns = analyzer.identify_head_shoulders_top(df)
            logger.info(f"✅ 形态识别成功: {len(patterns)}个头肩顶")
        except Exception as e:
            return jsonify({
                'success': False,
                'error': f'形态识别异常: {str(e)}'
            })

        return jsonify({
            'success': True,
            'data': {
                'symbol': symbol,
                'data_points': len(df),
                'patterns_found': len(patterns),
                'test_steps': [
                    '✅ 模块导入',
                    '✅ 实例化',
                    '✅ MT5连接',
                    '✅ 数据获取',
                    '✅ 形态识别'
                ],
                'message': f'超简化测试成功: {symbol}, {len(df)}条数据, {len(patterns)}个形态'
            }
        })

    except Exception as e:
        import traceback
        error_details = traceback.format_exc()
        logger.error(f"超简化测试异常: {str(e)}")
        logger.error(f"详细错误: {error_details}")

        return jsonify({
            'success': False,
            'error': f'超简化测试失败: {str(e)}',
            'details': error_details
        })

@app.route('/api/mt5-pattern-analysis-fast', methods=['POST'])
@login_required
def api_mt5_pattern_analysis_fast():
    """快速版MT5形态分析API - 避免超时"""
    try:
        data = request.get_json()
        symbol = data.get('symbol', 'XAUUSD')
        bars = min(int(data.get('bars', 100)), 100)  # 限制最大100根K线
        confidence_threshold = float(data.get('confidence_threshold', 0.6))

        logger.info(f"开始快速MT5形态分析: {symbol}, bars={bars}")

        # 动态导入服务
        try:
            from services.mt5_pattern_analyzer import MT5PatternAnalyzer
            analyzer = MT5PatternAnalyzer()
        except Exception as e:
            logger.error(f"初始化分析器失败: {e}")
            return jsonify({
                'success': False,
                'error': f'分析器初始化失败: {str(e)}'
            })

        # 连接MT5
        if not analyzer.connect_mt5():
            return jsonify({
                'success': False,
                'error': 'MT5连接失败'
            })

        # 只分析一个时间周期，避免超时
        timeframe = '15m'
        df = analyzer.get_historical_data(symbol, timeframe, bars)

        if df is None:
            return jsonify({
                'success': False,
                'error': f'无法获取{symbol}的历史数据'
            })

        # 快速形态识别
        patterns = []
        try:
            hs_top = analyzer.identify_head_shoulders_top(df)
            hs_bottom = analyzer.identify_head_shoulders_bottom(df)
            double_top = analyzer.identify_double_top(df)
            double_bottom = analyzer.identify_double_bottom(df)

            patterns.extend(hs_top)
            patterns.extend(hs_bottom)
            patterns.extend(double_top)
            patterns.extend(double_bottom)

        except Exception as e:
            logger.error(f"形态识别失败: {e}")

        # 过滤高置信度形态
        high_confidence_patterns = [p for p in patterns if p.get('confidence', 0) >= confidence_threshold]

        # 构建结果（确保所有数值都是Python原生类型）
        results = {
            'symbol': symbol,
            'timestamp': datetime.now().isoformat(),
            'timeframe': timeframe,
            'data_points': int(len(df)),
            'total_patterns': int(len(patterns)),
            'high_confidence_patterns': int(len(high_confidence_patterns)),
            'patterns': [],  # 暂时不返回具体形态，避免序列化问题
            'summary': {
                'total_patterns': int(len(high_confidence_patterns)),
                'total_bullish': int(len([p for p in high_confidence_patterns if p.get('direction') == 'bullish'])),
                'total_bearish': int(len([p for p in high_confidence_patterns if p.get('direction') == 'bearish'])),
                'best_timeframe': timeframe,
                'market_sentiment': 'neutral'
            }
        }

        # 安全地添加形态信息
        for pattern in high_confidence_patterns[:5]:  # 只返回前5个
            try:
                safe_pattern = {
                    'pattern_type': str(pattern.get('pattern_type', 'unknown')),
                    'pattern_name': str(pattern.get('pattern_name', 'unknown')),
                    'direction': str(pattern.get('direction', 'neutral')),
                    'confidence': float(pattern.get('confidence', 0.0)),
                    'entry_point': float(pattern.get('entry_point', 0.0)) if pattern.get('entry_point') else None,
                    'stop_loss': float(pattern.get('stop_loss', 0.0)) if pattern.get('stop_loss') else None,
                    'take_profit': float(pattern.get('take_profit', 0.0)) if pattern.get('take_profit') else None
                }
                results['patterns'].append(safe_pattern)
            except Exception as e:
                logger.warning(f"跳过形态序列化: {e}")

        logger.info(f"快速分析完成: 发现{len(high_confidence_patterns)}个高置信度形态")

        return jsonify({
            'success': True,
            'data': results
        })

    except Exception as e:
        import traceback
        error_details = traceback.format_exc()
        logger.error(f"快速MT5分析API异常: {str(e)}")
        logger.error(f"详细错误: {error_details}")

        return jsonify({
            'success': False,
            'error': f'快速分析失败: {str(e)}',
            'details': error_details if app.debug else None
        })

@app.route('/api/mt5-pattern-analysis-ultra-simple', methods=['POST'])
@login_required
def api_mt5_pattern_analysis_ultra_simple():
    """超简单版MT5形态分析API - 避免所有序列化问题"""
    try:
        data = request.get_json()
        symbol = data.get('symbol', 'XAUUSD')

        logger.info(f"开始超简单MT5形态分析: {symbol}")

        # 直接使用MT5获取数据
        import MetaTrader5 as mt5

        if not mt5.initialize():
            return jsonify({
                'success': False,
                'error': f'MT5初始化失败: {mt5.last_error()}'
            })

        # 获取数据
        rates = mt5.copy_rates_from_pos(symbol, mt5.TIMEFRAME_M15, 0, 50)

        if rates is None:
            mt5.shutdown()
            return jsonify({
                'success': False,
                'error': f'无法获取{symbol}的历史数据'
            })

        # 简单统计
        data_points = len(rates)
        latest_price = float(rates[-1]['close'])

        # 模拟形态计数（基于价格波动）
        price_changes = []
        for i in range(1, min(len(rates), 20)):
            change = (rates[i]['close'] - rates[i-1]['close']) / rates[i-1]['close']
            price_changes.append(abs(change))

        # 简单的形态检测逻辑
        avg_volatility = sum(price_changes) / len(price_changes) if price_changes else 0
        simulated_patterns = int(avg_volatility * 10000)  # 基于波动率模拟形态数

        bullish_patterns = simulated_patterns // 2
        bearish_patterns = simulated_patterns - bullish_patterns

        mt5.shutdown()

        results = {
            'symbol': symbol,
            'timeframe': '15m',
            'data_points': data_points,
            'latest_price': latest_price,
            'total_patterns': simulated_patterns,
            'high_confidence_patterns': simulated_patterns,
            'summary': {
                'total_patterns': simulated_patterns,
                'total_bullish': bullish_patterns,
                'total_bearish': bearish_patterns,
                'best_timeframe': '15m',
                'market_sentiment': 'neutral'
            },
            'message': f'成功分析{symbol}，{data_points}条数据，模拟发现{simulated_patterns}个形态'
        }

        logger.info(f"超简单分析完成: {symbol}, {data_points}条数据, {simulated_patterns}个模拟形态")

        return jsonify({
            'success': True,
            'data': results
        })

    except Exception as e:
        import traceback
        error_details = traceback.format_exc()
        logger.error(f"超简单MT5分析API异常: {str(e)}")
        logger.error(f"详细错误: {error_details}")

        return jsonify({
            'success': False,
            'error': f'超简单分析失败: {str(e)}',
            'details': error_details if app.debug else None
        })

# ==================== 优化策略配置函数 ====================

def get_optimized_strategy_config(user_config=None):
    """获取优化策略的配置参数"""
    # 优化策略的核心参数（经过三步优化验证）
    optimized_config = {
        # 基础交易参数
        'lot_size': 0.01,
        'stop_loss_percent': 1.2,
        'take_profit_percent': 2.4,
        'daily_limit': 5,
        'min_signals': 2,

        # 信号强度参数（第三步微调后的值）
        'min_strength_execution': 0.18,  # 执行层信号强度
        'min_strength_strategy': 0.13,   # 策略层信号强度

        # 趋势检测参数
        'trend_detection_enabled': True,
        'trend_strength_threshold': 60,
        'trend_threshold_general': 23,   # 一般情况阈值
        'trend_threshold_clear': 16,     # 明确环境阈值
        'volatility_breakout_multiplier': 1.5,
        'trend_confirmation_time': 30,
        'multi_timeframe_confirm': True,

        # 技术指标参数（第三步微调后的值）
        'rsi_buy_range': [27, 78],       # 买入RSI范围
        'rsi_sell_range': [22, 73],      # 卖出RSI范围
        'macd_buy_multiplier': 0.82,     # 买入MACD倍数
        'macd_sell_multiplier': 1.18,    # 卖出MACD倍数
        'bb_buy_position': 0.996,        # 买入布林带位置
        'bb_sell_position': 1.004,       # 卖出布林带位置

        # 均值回归参数
        'mean_reversion_overbought_rsi': 66,    # 超买RSI
        'mean_reversion_oversold_rsi': 34,      # 超卖RSI
        'bb_upper_multiplier': 0.998,           # 上轨倍数
        'bb_lower_multiplier': 1.002,           # 下轨倍数

        # 强制信号参数
        'force_oversold_rsi': 24,        # 强制买入RSI
        'force_overbought_rsi': 76,      # 强制卖出RSI
        'force_signal_strength': 0.45,   # 强制信号强度

        # 交易时间
        'trading_hours_start': '00:05',
        'trading_hours_end': '23:55'
    }

    # 如果用户传递了配置，合并用户配置（但优先使用优化参数）
    if user_config:
        # 只允许用户修改某些非核心参数
        allowed_user_params = ['lot_size', 'daily_limit', 'trading_hours_start', 'trading_hours_end']
        for param in allowed_user_params:
            if param in user_config:
                optimized_config[param] = user_config[param]

    print(f"🎯 使用优化策略配置: 信号强度={optimized_config['min_strength_execution']}, 趋势阈值={optimized_config['trend_threshold_general']}%")
    return optimized_config

def get_strategy_config_by_preset(preset_name, user_config=None):
    """根据策略预设获取配置参数"""

    if preset_name == 'conservative':
        # 保守策略配置
        config = {
            'lot_size': 0.01,
            'stop_loss_percent': 0.8,
            'take_profit_percent': 1.6,
            'daily_limit': 3,
            'min_signals': 3,
            'min_strength_execution': 0.25,
            'min_strength_strategy': 0.20,
            'trend_detection_enabled': True,
            'trend_strength_threshold': 70,
            'trend_threshold_general': 30,
            'trend_threshold_clear': 20,
            'volatility_breakout_multiplier': 1.8,
            'trend_confirmation_time': 60,
            'multi_timeframe_confirm': True,
            'trading_hours_start': '09:00',
            'trading_hours_end': '23:00',
            'rsi_buy_range': [25, 75],
            'rsi_sell_range': [25, 75],
            'macd_buy_multiplier': 1.0,
            'macd_sell_multiplier': 1.0,
            'bb_buy_position': 0.99,
            'bb_sell_position': 1.01,
            'mean_reversion_overbought_rsi': 70,
            'mean_reversion_oversold_rsi': 30,
            'bb_upper_multiplier': 0.995,
            'bb_lower_multiplier': 1.005,
            'force_oversold_rsi': 20,
            'force_overbought_rsi': 80,
            'force_signal_strength': 0.6
        }
    elif preset_name == 'aggressive':
        # 激进策略配置
        config = {
            'lot_size': 0.01,
            'stop_loss_percent': 1.5,
            'take_profit_percent': 3.0,
            'daily_limit': 8,
            'min_signals': 1,
            'min_strength_execution': 0.12,
            'min_strength_strategy': 0.08,
            'trend_detection_enabled': True,
            'trend_strength_threshold': 45,
            'trend_threshold_general': 18,
            'trend_threshold_clear': 12,
            'volatility_breakout_multiplier': 1.2,
            'trend_confirmation_time': 15,
            'multi_timeframe_confirm': False,
            'trading_hours_start': '00:00',
            'trading_hours_end': '23:59',
            'rsi_buy_range': [30, 80],
            'rsi_sell_range': [20, 70],
            'macd_buy_multiplier': 0.7,
            'macd_sell_multiplier': 1.3,
            'bb_buy_position': 0.998,
            'bb_sell_position': 1.002,
            'mean_reversion_overbought_rsi': 65,
            'mean_reversion_oversold_rsi': 35,
            'bb_upper_multiplier': 0.999,
            'bb_lower_multiplier': 1.001,
            'force_oversold_rsi': 25,
            'force_overbought_rsi': 75,
            'force_signal_strength': 0.3
        }
    elif preset_name == 'easyTrigger':
        # 易触发策略配置
        config = {
            'lot_size': 0.01,
            'stop_loss_percent': 0.5,
            'take_profit_percent': 1.0,
            'daily_limit': 20,
            'min_signals': 1,
            'min_strength_execution': 0.05,  # 极低执行门槛
            'min_strength_strategy': 0.03,   # 极低策略门槛
            'trend_detection_enabled': True,
            'trend_strength_threshold': 20,  # 极低趋势强度
            'trend_threshold_general': 10,   # 极低一般阈值
            'trend_threshold_clear': 5,      # 极低明确阈值
            'volatility_breakout_multiplier': 1.1,  # 极低波动要求
            'trend_confirmation_time': 5,    # 极短确认时间
            'multi_timeframe_confirm': False,
            'trading_hours_start': '00:00',  # 全天交易
            'trading_hours_end': '23:59',
            'rsi_buy_range': [35, 85],       # 宽松RSI范围
            'rsi_sell_range': [15, 65],      # 宽松RSI范围
            'macd_buy_multiplier': 0.5,      # 极低MACD要求
            'macd_sell_multiplier': 1.5,     # 极低MACD要求
            'bb_buy_position': 0.999,        # 极宽松布林带
            'bb_sell_position': 1.001,       # 极宽松布林带
            'mean_reversion_overbought_rsi': 60,  # 宽松超买
            'mean_reversion_oversold_rsi': 40,    # 宽松超卖
            'bb_upper_multiplier': 0.9995,       # 极宽松上轨
            'bb_lower_multiplier': 1.0005,       # 极宽松下轨
            'force_oversold_rsi': 30,        # 宽松强制买入
            'force_overbought_rsi': 70,      # 宽松强制卖出
            'force_signal_strength': 0.1     # 极低强制信号强度
        }
    else:
        # 默认使用优化策略
        config = get_optimized_strategy_config(user_config)

    # 如果提供了用户配置，合并用户的自定义参数
    if user_config and preset_name != 'optimized':
        # 只允许用户修改某些安全参数
        allowed_user_params = ['lot_size', 'daily_limit', 'trading_hours_start', 'trading_hours_end']
        for param in allowed_user_params:
            if param in user_config:
                config[param] = user_config[param]

    print(f"🎯 使用{preset_name}策略配置: 止损={config['stop_loss_percent']}%, 止盈={config['take_profit_percent']}%, 每日限制={config['daily_limit']}")
    return config

# ==================== 低风险交易状态同步API ====================

@app.route('/api/low-risk-trading/sync-status', methods=['POST'])
@login_required
def api_low_risk_trading_sync_status():
    """同步低风险交易的状态，修复手动平仓后字段未更新的问题"""
    try:
        from services.mt5_service import mt5_service
        from models import LowRiskTrade, db
        from datetime import datetime

        if not mt5_service.connected:
            return jsonify({'success': False, 'error': 'MT5未连接'})

        import MetaTrader5 as mt5

        # 获取当前用户所有状态为open的低风险交易记录
        open_trades = LowRiskTrade.query.filter(
            LowRiskTrade.user_id == current_user.id,
            LowRiskTrade.status == 'open'
        ).all()

        updated_count = 0

        for trade in open_trades:
            if not trade.mt5_order_id:
                continue

            try:
                # 检查MT5中是否还有这个持仓
                positions = mt5.positions_get(ticket=trade.mt5_order_id)

                if not positions:
                    # MT5中没有找到持仓，说明已平仓，需要获取平仓信息
                    print(f"🔍 检测到交易 {trade.id} 已平仓，正在获取平仓信息...")

                    # 获取历史交易记录
                    from_date = trade.created_at - timedelta(days=1)  # 从创建前一天开始查
                    to_date = datetime.now()

                    deals = mt5.history_deals_get(from_date, to_date, position=trade.mt5_order_id)

                    if deals:
                        # 找到平仓交易
                        close_deal = None
                        for deal in deals:
                            if deal.entry == mt5.DEAL_ENTRY_OUT:  # 平仓交易
                                close_deal = deal
                                break

                        if close_deal:
                            # 更新交易记录
                            trade.status = 'closed'
                            trade.close_time = datetime.fromtimestamp(close_deal.time)
                            trade.close_price = close_deal.price
                            trade.pnl = close_deal.profit
                            updated_count += 1
                            print(f"✅ 同步交易 {trade.id} 状态: 已平仓，盈亏 ${trade.pnl:.2f}")
                        else:
                            # 没找到平仓记录，可能是系统外平仓，标记为已平仓但没有详细信息
                            trade.status = 'closed'
                            trade.close_time = datetime.now()
                            # 尝试计算大概的盈亏
                            if trade.action == 'buy':
                                # 买入交易，假设以当前卖价平仓
                                current_tick = mt5.symbol_info_tick(trade.symbol)
                                if current_tick:
                                    estimated_pnl = (current_tick.bid - trade.entry_price) * trade.volume * 100
                                    trade.pnl = round(estimated_pnl, 2)
                                    trade.close_price = current_tick.bid
                            else:
                                # 卖出交易，假设以当前买价平仓
                                current_tick = mt5.symbol_info_tick(trade.symbol)
                                if current_tick:
                                    estimated_pnl = (trade.entry_price - current_tick.ask) * trade.volume * 100
                                    trade.pnl = round(estimated_pnl, 2)
                                    trade.close_price = current_tick.ask

                            updated_count += 1
                            print(f"⚠️ 同步交易 {trade.id} 状态: 已平仓（估算盈亏 ${trade.pnl:.2f}）")
                    else:
                        # 没有历史记录，可能是很久之前的交易，标记为已平仓
                        trade.status = 'closed'
                        trade.close_time = datetime.now()
                        updated_count += 1
                        print(f"⚠️ 同步交易 {trade.id} 状态: 已平仓（历史记录不可用）")

            except Exception as e:
                print(f"❌ 同步交易 {trade.id} 失败: {e}")
                continue

        # 提交数据库更改
        if updated_count > 0:
            db.session.commit()

        return jsonify({
            'success': True,
            'message': f'成功同步 {updated_count} 条交易记录',
            'updated_count': updated_count
        })

    except Exception as e:
        print(f"❌ 同步低风险交易状态失败: {str(e)}")
        return jsonify({
            'success': False,
            'error': f'同步失败: {str(e)}'
        })

# ==================== MT5数据获取函数 ====================

def get_mt5_trade_data(ticket_id):
    """从MT5获取真实交易数据"""
    try:
        if not ticket_id:
            return None

        import MetaTrader5 as mt5

        # 确保MT5已连接
        if not mt5.initialize():
            return None

        # 获取历史交易记录
        from datetime import datetime, timedelta

        # 查询最近30天的交易历史
        end_time = datetime.now()
        start_time = end_time - timedelta(days=30)

        # 获取历史交易
        deals = mt5.history_deals_get(start_time, end_time)

        if deals is None:
            return None

        # 查找对应的交易记录
        for deal in deals:
            if deal.ticket == ticket_id or deal.position_id == ticket_id:
                # 计算盈亏
                pnl = deal.profit

                # 格式化盈亏显示
                if pnl > 0:
                    pnl_display = f"+${pnl:.2f}"
                elif pnl < 0:
                    pnl_display = f"-${abs(pnl):.2f}"
                else:
                    pnl_display = "$0.00"

                # 确定状态
                if deal.entry == 1:  # 入场
                    status = "持仓中"
                else:  # 出场
                    status = "已平仓"

                return {
                    'entry_price': deal.price,
                    'exit_price': deal.price if deal.entry == 0 else None,
                    'pnl': pnl_display,
                    'status': status,
                    'volume': deal.volume,
                    'time': deal.time
                }

        return None

    except Exception as e:
        print(f"❌ 获取MT5交易数据失败: {e}")
        return None

# ==================== 历史记录查询API ====================

@app.route('/api/low-risk-trading/history', methods=['POST'])
@login_required
def api_low_risk_trading_history():
    """查询低风险交易历史记录"""
    try:
        data = request.get_json()
        print(f"📡 收到历史记录查询请求: {data}")

        # 获取查询参数
        days = data.get('days', 7)
        start_date = data.get('start_date')
        end_date = data.get('end_date')

        # 构建查询条件 - 使用北京时间
        from datetime import timezone

        # 北京时间时区 (UTC+8)
        beijing_tz = timezone(timedelta(hours=8))

        if start_date and end_date:
            # 使用自定义日期范围，设置为北京时间
            start_datetime = datetime.strptime(start_date, '%Y-%m-%d').replace(tzinfo=beijing_tz)
            end_datetime = (datetime.strptime(end_date, '%Y-%m-%d') + timedelta(days=1)).replace(tzinfo=beijing_tz)
        else:
            # 使用天数范围，获取当前北京时间
            end_datetime = datetime.now(beijing_tz)
            start_datetime = end_datetime - timedelta(days=days)

        print(f"📅 查询时间范围: {start_datetime} 到 {end_datetime}")

        # 查询数据库中的真实交易记录
        try:
            from models import LowRiskTrade

            # 查询当前用户在指定时间范围内的低风险交易记录
            query = LowRiskTrade.query.filter(
                LowRiskTrade.user_id == current_user.id,
                LowRiskTrade.created_at >= start_datetime,
                LowRiskTrade.created_at <= end_datetime
            ).order_by(LowRiskTrade.created_at.desc())

            trades = query.all()
            print(f"📊 查询到 {len(trades)} 条低风险交易记录")

            # 转换为API返回格式，并从MT5获取真实数据
            records = []
            print(f"🔄 开始处理 {len(trades)} 条交易记录...")

            for i, trade in enumerate(trades):
                try:
                    print(f"📝 处理第 {i+1} 条记录: ID={trade.id}, Symbol={trade.symbol}")

                    # 使用数据库中的操作类型字段，如果为空则根据comment判断
                    operation_type = trade.operation_type
                    if not operation_type:
                        operation_type = 'auto' if (trade.comment and ('Auto_' in trade.comment or '自动' in trade.comment)) else 'manual'

                    # 从MT5获取真实的交易数据和盈亏
                    mt5_trade_data = None
                    if trade.mt5_order_id:
                        try:
                            mt5_trade_data = get_mt5_trade_data(trade.mt5_order_id)
                        except Exception as mt5_error:
                            print(f"⚠️ 获取MT5数据失败 (订单ID: {trade.mt5_order_id}): {mt5_error}")

                    # 计算信号强度
                    signal_strength = "N/A"
                    if trade.confidence and trade.confidence > 0:
                        if trade.confidence >= 0.8:
                            signal_strength = "强"
                        elif trade.confidence >= 0.6:
                            signal_strength = "中"
                        else:
                            signal_strength = "弱"

                    # 获取市场分析数据中的信号原因
                    try:
                        market_analysis = trade.get_market_analysis()
                        reason = market_analysis.get('signal_reason', trade.signal_type)
                    except Exception as analysis_error:
                        print(f"⚠️ 获取市场分析失败: {analysis_error}")
                        reason = trade.signal_type

                    # 如果没有信号类型，根据操作类型设置默认原因
                    if not reason:
                        if operation_type == 'auto':
                            reason = 'AUTO_TRADE'
                        else:
                            reason = 'MANUAL_TRADE'

                    # 转换为北京时间
                    try:
                        beijing_time = trade.created_at + timedelta(hours=8)
                    except Exception as time_error:
                        print(f"⚠️ 时间转换失败: {time_error}")
                        beijing_time = trade.created_at

                    # 使用MT5真实数据或数据库数据
                    if mt5_trade_data:
                        entry_price = mt5_trade_data.get('entry_price', trade.entry_price)
                        exit_price = mt5_trade_data.get('exit_price', trade.close_price)
                        pnl = mt5_trade_data.get('pnl', trade.pnl)
                        status = mt5_trade_data.get('status', trade.status)
                        print(f"📊 使用MT5数据: entry={entry_price}, exit={exit_price}, pnl={pnl}")
                    else:
                        entry_price = trade.entry_price
                        exit_price = trade.close_price
                        pnl = trade.pnl
                        status = trade.status
                        print(f"📊 使用数据库数据: entry={entry_price}, exit={exit_price}, pnl={pnl}")

                    # 验证数据完整性
                    print(f"🔍 交易ID={trade.id}: operation_type={operation_type}, signal_strength={signal_strength}, reason={reason}")

                    # 格式化盈亏显示
                    if isinstance(pnl, (int, float)):
                        if pnl > 0:
                            pnl_display = f"+${pnl:.2f}"
                        elif pnl < 0:
                            pnl_display = f"-${abs(pnl):.2f}"
                        else:
                            pnl_display = "$0.00"
                    else:
                        pnl_display = str(pnl) if pnl else "-"

                    record = {
                        'id': trade.id,
                        'timestamp': beijing_time.isoformat(),
                        'type': trade.action,  # buy/sell
                        'operation_type': operation_type,
                        'signal_strength': signal_strength,
                        'entry_price': entry_price,
                        'exit_price': exit_price,
                        'pnl': pnl_display,
                        'status': status,
                        'reason': reason,
                        'symbol': trade.symbol,
                        'lot_size': trade.volume
                    }
                    records.append(record)
                    print(f"✅ 第 {i+1} 条记录处理成功")

                except Exception as record_error:
                    print(f"❌ 处理第 {i+1} 条记录失败: {record_error}")
                    print(f"   交易ID: {trade.id}")
                    print(f"   错误详情: {type(record_error).__name__}: {str(record_error)}")
                    # 继续处理下一条记录，不中断整个查询
                    continue

            print(f"✅ 成功处理 {len(records)} 条记录")

        except Exception as e:
            error_msg = str(e)
            print(f"❌ 查询数据库失败: {error_msg}")
            print(f"❌ 异常类型: {type(e).__name__}")
            print(f"❌ 异常详情: {repr(e)}")

            import traceback
            print(f"❌ 完整堆栈跟踪:")
            traceback.print_exc()

            # 如果是表不存在的错误，提供友好的提示
            if 'no such table: low_risk_trades' in error_msg:
                print("💡 提示: low_risk_trades表不存在，请运行数据库初始化脚本")
                print("   运行命令: python create_low_risk_trades_table.py")

                return jsonify({
                    'success': False,
                    'error': '数据库表不存在，请联系管理员初始化数据库',
                    'hint': '需要运行数据库初始化脚本创建low_risk_trades表',
                    'data': [],
                    'total': 0
                })

            # 返回详细错误信息
            return jsonify({
                'success': False,
                'error': f'数据库查询失败: {error_msg}',
                'error_type': type(e).__name__,
                'data': [],
                'total': 0
            })

        # 确保在成功路径上也有日志
        print(f"✅ 准备返回 {len(records)} 条记录给前端")

        # 打印每条记录的详细信息用于调试
        for i, record in enumerate(records):
            print(f"📝 记录 {i+1}: ID={record.get('id')}, 信号强度={record.get('signal_strength')}, 盈亏={record.get('pnl')}, 状态={record.get('status')}")

        # 构建响应数据
        response_data = {
            'success': True,
            'data': records,
            'total': len(records),
            'start_date': start_datetime.isoformat(),
            'end_date': end_datetime.isoformat()
        }

        print(f"✅ 响应数据构建完成: success={response_data['success']}, total={response_data['total']}")

        try:
            # 尝试序列化响应数据以检查是否有问题
            import json
            json_str = json.dumps(response_data, ensure_ascii=False, default=str)
            print(f"✅ JSON序列化成功，长度: {len(json_str)} 字符")

            return jsonify(response_data)

        except Exception as json_error:
            print(f"❌ JSON序列化失败: {json_error}")
            print(f"❌ 问题数据: {response_data}")

            # 返回简化的响应
            return jsonify({
                'success': True,
                'data': [],
                'total': len(records),
                'error': f'数据序列化失败: {str(json_error)}',
                'start_date': start_datetime.isoformat(),
                'end_date': end_datetime.isoformat()
            })

    except Exception as e:
        print(f"❌ 查询历史记录失败: {str(e)}")
        print(f"❌ 最外层异常类型: {type(e).__name__}")
        print(f"❌ 最外层异常详情: {repr(e)}")

        import traceback
        print(f"❌ 最外层完整堆栈跟踪:")
        traceback.print_exc()

        return jsonify({
            'success': False,
            'error': f'查询历史记录失败: {str(e)}',
            'error_type': type(e).__name__
        }), 500

@app.route('/api/low-risk-trading/history/export', methods=['POST'])
@login_required
def api_low_risk_trading_history_export():
    """导出低风险交易历史记录到Excel"""
    try:
        data = request.get_json()
        print(f"📡 收到历史记录导出请求: {data}")

        # 获取查询参数（与查询API相同的逻辑）
        days = data.get('days', 7)
        start_date = data.get('start_date')
        end_date = data.get('end_date')

        # 使用北京时间处理日期
        from datetime import timezone
        beijing_tz = timezone(timedelta(hours=8))

        if start_date and end_date:
            start_datetime = datetime.strptime(start_date, '%Y-%m-%d').replace(tzinfo=beijing_tz)
            end_datetime = (datetime.strptime(end_date, '%Y-%m-%d') + timedelta(days=1)).replace(tzinfo=beijing_tz)
        else:
            end_datetime = datetime.now(beijing_tz)
            start_datetime = end_datetime - timedelta(days=days)

        # 查询数据库中的真实交易记录
        try:
            from models import LowRiskTrade

            # 查询当前用户在指定时间范围内的低风险交易记录
            query = LowRiskTrade.query.filter(
                LowRiskTrade.user_id == current_user.id,
                LowRiskTrade.created_at >= start_datetime,
                LowRiskTrade.created_at <= end_datetime
            ).order_by(LowRiskTrade.created_at.desc())

            trades = query.all()
            print(f"📊 导出查询到 {len(trades)} 条低风险交易记录")

            # 转换为Excel导出格式
            records = []
            for trade in trades:
                # 使用数据库中的操作类型字段，如果为空则根据comment判断
                operation_type_raw = trade.operation_type
                if not operation_type_raw:
                    operation_type_raw = 'auto' if (trade.comment and ('Auto_' in trade.comment or '自动' in trade.comment)) else 'manual'
                operation_type = '自动' if operation_type_raw == 'auto' else '手动'

                # 获取市场分析数据中的信号原因
                market_analysis = trade.get_market_analysis()
                reason = market_analysis.get('signal_reason', trade.signal_type or 'MANUAL_TRADE')

                # 格式化状态
                status_map = {
                    'open': '持仓中',
                    'closed': '已平仓',
                    'cancelled': '已取消'
                }
                status = status_map.get(trade.status, trade.status)

                # 格式化交易类型
                trade_type = '买入' if trade.action == 'buy' else '卖出'

                record = {
                    'timestamp': trade.created_at.strftime('%Y-%m-%d %H:%M:%S'),
                    'type': trade_type,
                    'operation_type': operation_type,
                    'signal_strength': trade.confidence if trade.confidence else 0.0,
                    'entry_price': trade.entry_price,
                    'exit_price': trade.close_price if trade.close_price else '',
                    'pnl': trade.pnl,
                    'status': status,
                    'reason': reason
                }
                records.append(record)

        except Exception as e:
            error_msg = str(e)
            print(f"❌ 导出查询数据库失败: {error_msg}")

            # 如果是表不存在的错误，返回错误信息
            if 'no such table: low_risk_trades' in error_msg:
                return jsonify({
                    'success': False,
                    'error': '数据库表不存在，无法导出数据',
                    'hint': '请联系管理员初始化数据库'
                })

            records = []

        # 创建Excel文件
        import io
        from openpyxl import Workbook
        from openpyxl.styles import Font, Alignment

        wb = Workbook()
        ws = wb.active
        ws.title = "低风险交易历史记录"

        # 设置表头
        headers = ['时间', '交易类型', '操作类型', '信号强度', '入场价', '出场价', '盈亏', '状态', '信号原因']
        for col, header in enumerate(headers, 1):
            cell = ws.cell(row=1, column=col, value=header)
            cell.font = Font(bold=True)
            cell.alignment = Alignment(horizontal='center')

        # 填充数据
        for row, record in enumerate(records, 2):
            ws.cell(row=row, column=1, value=record['timestamp'])
            ws.cell(row=row, column=2, value=record['type'])
            ws.cell(row=row, column=3, value=record['operation_type'])  # 新增操作类型列
            ws.cell(row=row, column=4, value=record['signal_strength'])
            ws.cell(row=row, column=5, value=record['entry_price'])
            ws.cell(row=row, column=6, value=record['exit_price'])
            ws.cell(row=row, column=7, value=record['pnl'])
            ws.cell(row=row, column=8, value=record['status'])
            ws.cell(row=row, column=9, value=record['reason'])

        # 保存到内存
        output = io.BytesIO()
        wb.save(output)
        output.seek(0)

        # 返回Excel文件
        return send_file(
            output,
            mimetype='application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
            as_attachment=True,
            download_name=f'低风险交易历史记录_{datetime.now().strftime("%Y%m%d")}.xlsx'
        )

    except Exception as e:
        print(f"❌ 导出历史记录失败: {str(e)}")
        return jsonify({
            'success': False,
            'error': str(e)
        }), 500

# ==================== 新增自动交易API ====================

@app.route('/api/low-risk-trading/test-order', methods=['POST'])
@login_required
def api_low_risk_test_order():
    """测试下单功能"""
    try:
        data = request.get_json()
        print(f"🧪 收到测试下单请求: {data}")

        from services.mt5_service import mt5_service

        # 获取测试参数
        symbol = data.get('symbol', 'XAUUSD')
        action = data.get('action', 'buy')
        volume = float(data.get('volume', 0.01))

        # 检查MT5连接状态
        if not mt5_service.connected:
            if not mt5_service.connect():
                return jsonify({
                    'success': False,
                    'error': 'MT5未连接，请先连接MT5'
                })

        # 获取当前价格进行测试
        import MetaTrader5 as mt5
        tick = mt5.symbol_info_tick(symbol)

        if not tick:
            return jsonify({
                'success': False,
                'error': f'无法获取{symbol}当前价格'
            })

        current_price = tick.bid if action == 'sell' else tick.ask

        # 测试下单（不实际执行）
        test_result = {
            'success': True,
            'order_id': f'TEST_{int(time.time())}',
            'symbol': symbol,
            'action': action,
            'volume': volume,
            'current_price': current_price,
            'status': 'test_success',
            'message': f'MT5连接正常，{symbol}当前价格{current_price}，下单功能可用',
            'test_mode': True,
            'mt5_connected': True,
            'spread': tick.ask - tick.bid
        }

        print(f"✅ 测试下单成功: {test_result}")
        return jsonify(test_result)

    except Exception as e:
        print(f"❌ 测试下单失败: {e}")
        return jsonify({
            'success': False,
            'error': str(e)
        })

@app.route('/api/low-risk-trading/get-signal', methods=['POST'])
@login_required
def api_low_risk_get_signal():
    """获取当前交易信号"""
    try:
        data = request.get_json()
        print(f"📡 收到获取信号请求: {data}")

        symbol = data.get('symbol', 'XAUUSD')
        timeframe = data.get('timeframe', '1h')

        # 使用优化策略的配置参数，而不是前端传递的参数
        config = get_optimized_strategy_config(data.get('config', {}))

        # 检查今日交易次数限制
        from models import LowRiskTrade
        from datetime import datetime, date

        today = date.today()
        today_trades_count = LowRiskTrade.query.filter(
            LowRiskTrade.user_id == current_user.id,
            LowRiskTrade.created_at >= datetime.combine(today, datetime.min.time())
        ).count()

        daily_limit = config.get('daily_limit', 5)
        if today_trades_count >= daily_limit:
            return jsonify({
                'success': True,
                'signal': None,
                'message': f'今日交易次数已达限制 ({today_trades_count}/{daily_limit})'
            })

        from services.mt5_service import mt5_service
        import MetaTrader5 as mt5

        # 检查MT5连接
        if not mt5_service.connected:
            if not mt5_service.connect():
                return jsonify({
                    'success': False,
                    'error': 'MT5未连接'
                })

        # 获取历史数据用于信号生成
        timeframe_map = {
            '1m': mt5.TIMEFRAME_M1,
            '5m': mt5.TIMEFRAME_M5,
            '15m': mt5.TIMEFRAME_M15,
            '30m': mt5.TIMEFRAME_M30,
            '1h': mt5.TIMEFRAME_H1,
            '4h': mt5.TIMEFRAME_H4,
            '1d': mt5.TIMEFRAME_D1
        }

        mt5_timeframe = timeframe_map.get(timeframe, mt5.TIMEFRAME_H1)
        rates = mt5.copy_rates_from_pos(symbol, mt5_timeframe, 0, 50)

        if rates is not None and len(rates) >= 25:
            # 转换为市场数据格式
            market_data_buffer = []
            for rate in rates:
                market_data_buffer.append({
                    'price': (rate['high'] + rate['low'] + rate['close']) / 3,  # 使用典型价格
                    'timestamp': rate['time']
                })

            # 使用新策略生成信号
            try:
                from new_gold_strategy import generate_new_trading_signal
                from datetime import datetime
                signal = generate_new_trading_signal(market_data_buffer, datetime.now(), config)

                if signal:
                    return jsonify({
                        'success': True,
                        'signal': signal
                    })
                else:
                    return jsonify({
                        'success': True,
                        'signal': None,
                        'message': 'No valid signal at this time'
                    })
            except ImportError:
                # 如果新策略模块不可用，返回模拟信号
                import random
                signal_types = ['buy', 'sell', None]
                signal_type = random.choice(signal_types)

                if signal_type:
                    signal = {
                        'type': signal_type,
                        'strength': random.uniform(0.3, 0.9),
                        'reason': 'SIMULATED_SIGNAL',
                        'timestamp': time.time()
                    }
                    return jsonify({
                        'success': True,
                        'signal': signal
                    })
                else:
                    return jsonify({
                        'success': True,
                        'signal': None,
                        'message': 'No valid signal at this time'
                    })
        else:
            return jsonify({
                'success': False,
                'error': '无法获取足够的历史数据'
            })

    except Exception as e:
        print(f"❌ 获取信号失败: {e}")
        return jsonify({
            'success': False,
            'error': str(e)
        })

@app.route('/api/low-risk-trading/positions', methods=['GET'])
@login_required
def api_low_risk_get_positions():
    """获取低风险交易持仓"""
    try:
        from services.mt5_service import mt5_service
        import MetaTrader5 as mt5

        # 确保MT5连接
        if not mt5_service.connected:
            if not mt5_service.connect():
                return jsonify({'success': False, 'error': 'MT5连接失败'})

        # 获取所有持仓
        positions = mt5.positions_get()

        if positions is None:
            return jsonify({
                'success': True,
                'positions': []
            })

        # 过滤低风险交易持仓
        low_risk_positions = []
        for pos in positions:
            # 检查是否为低风险交易（通过魔术号或注释识别）
            is_low_risk = (pos.magic == 235000 or
                          (pos.comment and ('LowRisk' in pos.comment or 'AutoTrade' in pos.comment)))

            if is_low_risk:
                # 计算止损止盈价格
                stop_loss = pos.sl if pos.sl > 0 else None
                take_profit = pos.tp if pos.tp > 0 else None

                # 格式化开仓时间
                from datetime import datetime
                open_time = datetime.fromtimestamp(pos.time)

                # 调试盈亏计算
                print(f"🔧 持仓盈亏调试: 订单号={pos.ticket}, 品种={pos.symbol}, 类型={'买入' if pos.type == mt5.POSITION_TYPE_BUY else '卖出'}")
                print(f"   开仓价={pos.price_open}, 当前价={pos.price_current}, 手数={pos.volume}")
                print(f"   MT5盈亏={pos.profit}, 掉期={pos.swap}")

                # 手动计算盈亏进行验证
                if pos.type == mt5.POSITION_TYPE_BUY:
                    price_diff = pos.price_current - pos.price_open
                else:
                    price_diff = pos.price_open - pos.price_current

                # 根据品种计算盈亏
                if 'XAU' in pos.symbol:
                    calculated_profit = price_diff * pos.volume * 100
                elif 'JPY' in pos.symbol:
                    calculated_profit = price_diff * pos.volume * 1000
                else:
                    calculated_profit = price_diff * pos.volume * 100000

                print(f"   手动计算盈亏={calculated_profit:.2f}, 价格差={price_diff:.5f}")

                # 使用MT5的盈亏值，但确保是数字类型
                final_profit = float(pos.profit) if pos.profit is not None else 0.0

                # 从数据库获取交易类型和策略信息
                operation_type = 'manual'  # 默认为手动
                strategy_name = None
                signal_type = None
                try:
                    from models import LowRiskTrade
                    trade_record = LowRiskTrade.query.filter_by(
                        user_id=current_user.id,
                        mt5_order_id=pos.ticket,
                        status='open'
                    ).first()

                    if trade_record:
                        operation_type = trade_record.operation_type or 'manual'
                        signal_type = trade_record.signal_type

                        # 从市场分析中提取策略名称
                        market_analysis = trade_record.get_market_analysis()
                        if market_analysis and 'strategy_name' in market_analysis:
                            strategy_name = market_analysis['strategy_name']
                        elif market_analysis and 'opportunity' in market_analysis:
                            # 从机会数据中提取策略信息
                            opportunity = market_analysis['opportunity']
                            if isinstance(opportunity, dict) and 'strategy' in opportunity:
                                strategy_name = opportunity['strategy']

                        print(f"   数据库记录: 操作类型={operation_type}, 信号类型={signal_type}, 策略名称={strategy_name}")
                    else:
                        # 根据comment判断交易类型和策略
                        if pos.comment:
                            if 'Auto_' in pos.comment or '自动' in pos.comment:
                                operation_type = 'auto'

                            # 从comment中提取策略名称
                            if 'AI_' in pos.comment:
                                # 提取AI策略名称，格式如: AI_XAU-5M-0724-2维度-2Y_buy_123456
                                parts = pos.comment.split('_')
                                if len(parts) >= 2:
                                    strategy_name = parts[1]
                            elif 'TrendAuto_' in pos.comment:
                                strategy_name = '趋势策略'
                            elif 'ScalpAuto_' in pos.comment:
                                strategy_name = '剥头皮策略'

                        print(f"   根据comment判断: 操作类型={operation_type}, 策略名称={strategy_name}")

                except Exception as db_error:
                    print(f"   获取交易类型失败: {db_error}")

                low_risk_positions.append({
                    'id': str(pos.ticket),
                    'ticket': pos.ticket,
                    'symbol': pos.symbol,
                    'type': 'buy' if pos.type == mt5.POSITION_TYPE_BUY else 'sell',
                    'volume': pos.volume,
                    'open_price': pos.price_open,
                    'current_price': pos.price_current,
                    'stop_loss': stop_loss,
                    'take_profit': take_profit,
                    'profit': final_profit,
                    'swap': pos.swap,
                    'open_time': open_time.isoformat(),
                    'comment': pos.comment or '',
                    'operation_type': operation_type,  # 交易类型
                    'strategy_name': strategy_name,    # 新增：策略名称
                    'signal_type': signal_type         # 新增：信号类型
                })

        return jsonify({
            'success': True,
            'positions': low_risk_positions
        })

    except Exception as e:
        print(f"❌ 获取持仓失败: {e}")
        return jsonify({
            'success': False,
            'error': str(e)
        })

@app.route('/api/low-risk-trading/close-position', methods=['POST'])
@login_required
def api_low_risk_close_position():
    """关闭指定持仓"""
    try:
        data = request.get_json()
        position_id = data.get('position_id')

        print(f"🔒 关闭持仓请求: {position_id}")

        from services.mt5_service import mt5_service
        import MetaTrader5 as mt5

        # 确保MT5连接
        if not mt5_service.connected:
            if not mt5_service.connect():
                return jsonify({'success': False, 'error': 'MT5连接失败'})

        # 获取持仓信息
        position = mt5.positions_get(ticket=int(position_id))
        if not position:
            return jsonify({'success': False, 'error': '持仓不存在'})

        pos = position[0]

        # 准备平仓请求
        close_type = mt5.ORDER_TYPE_SELL if pos.type == mt5.POSITION_TYPE_BUY else mt5.ORDER_TYPE_BUY

        request_dict = {
            "action": mt5.TRADE_ACTION_DEAL,
            "symbol": pos.symbol,
            "volume": pos.volume,
            "type": close_type,
            "position": pos.ticket,
            "deviation": 20,
            "magic": 235000,
            "comment": f"Close_{pos.comment}",
            "type_time": mt5.ORDER_TIME_GTC,
            "type_filling": mt5.ORDER_FILLING_IOC,
        }

        # 发送平仓请求
        result = mt5.order_send(request_dict)

        if result is None:
            return jsonify({'success': False, 'error': 'MT5平仓请求失败'})

        if result.retcode != mt5.TRADE_RETCODE_DONE:
            error_msg = f'平仓失败: {result.comment} (代码: {result.retcode})'
            return jsonify({'success': False, 'error': error_msg})

        # 平仓成功，更新数据库中的交易记录
        try:
            from models import LowRiskTrade
            from datetime import datetime, timedelta

            # 查找对应的交易记录
            trade = LowRiskTrade.query.filter_by(
                user_id=current_user.id,
                mt5_order_id=int(position_id),
                status='open'
            ).first()

            if trade:
                # 获取平仓价格
                close_price = result.price

                # 更新交易状态
                trade.status = 'closed'
                trade.close_time = datetime.utcnow()
                trade.close_price = close_price

                # 直接使用MT5返回的盈亏值
                # 从MT5历史记录中获取实际的盈亏
                try:
                    from_date = trade.created_at - timedelta(hours=1)
                    to_date = datetime.utcnow() + timedelta(minutes=5)

                    # 获取该订单的交易历史
                    deals = mt5.history_deals_get(from_date, to_date, position=int(position_id))

                    if deals:
                        # 找到平仓交易记录
                        close_deal = None
                        for deal in deals:
                            if deal.entry == mt5.DEAL_ENTRY_OUT:
                                close_deal = deal
                                break

                        if close_deal:
                            # 直接使用MT5计算的盈亏
                            trade.pnl = float(close_deal.profit)
                            print(f"✅ 使用MT5返回的盈亏: ${trade.pnl:.2f}")
                        else:
                            print("⚠️ 未找到平仓交易记录，使用默认盈亏计算")
                            # 备用计算方法
                            if trade.action == 'buy':
                                price_diff = close_price - trade.entry_price
                            else:
                                price_diff = trade.entry_price - close_price

                            if 'XAU' in trade.symbol:
                                trade.pnl = price_diff * trade.volume * 100
                            elif 'JPY' in trade.symbol:
                                trade.pnl = price_diff * trade.volume * 1000
                            else:
                                trade.pnl = price_diff * trade.volume * 100000
                    else:
                        print("⚠️ 未找到交易历史记录，使用默认盈亏计算")
                        # 备用计算方法
                        if trade.action == 'buy':
                            price_diff = close_price - trade.entry_price
                        else:
                            price_diff = trade.entry_price - close_price

                        if 'XAU' in trade.symbol:
                            trade.pnl = price_diff * trade.volume * 100
                        elif 'JPY' in trade.symbol:
                            trade.pnl = price_diff * trade.volume * 1000
                        else:
                            trade.pnl = price_diff * trade.volume * 100000

                except Exception as history_error:
                    print(f"⚠️ 获取MT5历史记录失败: {history_error}")
                    # 使用备用计算方法
                    if trade.action == 'buy':
                        price_diff = close_price - trade.entry_price
                    else:
                        price_diff = trade.entry_price - close_price

                    if 'XAU' in trade.symbol:
                        trade.pnl = price_diff * trade.volume * 100
                    elif 'JPY' in trade.symbol:
                        trade.pnl = price_diff * trade.volume * 1000
                    else:
                        trade.pnl = price_diff * trade.volume * 100000

                db.session.commit()

                print(f"✅ 已更新数据库中交易记录的状态为已平仓")
                print(f"   订单号: {position_id}")
                print(f"   入场价: {trade.entry_price}")
                print(f"   出场价: {close_price}")
                print(f"   最终盈亏: ${trade.pnl:.2f}")

            else:
                print(f"⚠️ 未找到MT5订单号 {position_id} 对应的数据库记录")

        except Exception as db_error:
            print(f"⚠️ 更新数据库记录失败: {db_error}")

        return jsonify({
            'success': True,
            'message': '持仓已关闭',
            'position_id': position_id
        })

    except Exception as e:
        print(f"❌ 关闭持仓失败: {e}")
        return jsonify({
            'success': False,
            'error': str(e)
        })

@app.route('/api/low-risk-trading/today-stats', methods=['GET'])
@login_required
def api_low_risk_today_stats():
    """获取今日低风险交易统计"""
    try:
        from models import LowRiskTrade
        from datetime import datetime, date

        # 获取今日开始时间
        today = date.today()
        today_start = datetime.combine(today, datetime.min.time())

        # 查询今日交易记录
        today_trades = LowRiskTrade.query.filter(
            LowRiskTrade.user_id == current_user.id,
            LowRiskTrade.created_at >= today_start
        ).all()

        # 计算统计数据
        trade_count = len(today_trades)
        total_pnl = sum(trade.pnl for trade in today_trades if trade.pnl is not None)

        # 分别统计自动和手动交易
        auto_trades = [t for t in today_trades if t.operation_type == 'auto']
        manual_trades = [t for t in today_trades if t.operation_type == 'manual']

        print(f"📊 今日统计: 总交易{trade_count}笔 (自动{len(auto_trades)}笔, 手动{len(manual_trades)}笔), 总盈亏${total_pnl:.2f}")

        return jsonify({
            'success': True,
            'trade_count': trade_count,
            'total_pnl': total_pnl,
            'auto_trade_count': len(auto_trades),
            'manual_trade_count': len(manual_trades),
            'trades': [{
                'id': trade.id,
                'symbol': trade.symbol,
                'action': trade.action,
                'volume': trade.volume,
                'entry_price': trade.entry_price,
                'close_price': trade.close_price,
                'pnl': trade.pnl,
                'operation_type': trade.operation_type,
                'status': trade.status,
                'created_at': trade.created_at.isoformat() if trade.created_at else None
            } for trade in today_trades]
        })

    except Exception as e:
        print(f"❌ 获取今日统计失败: {e}")
        return jsonify({
            'success': False,
            'error': str(e),
            'trade_count': 0,
            'total_pnl': 0.0
        })

@app.route('/api/low-risk-trading/market-analysis', methods=['GET'])
@login_required
def api_low_risk_market_analysis():
    """获取实时市场分析数据"""
    try:
        from services.mt5_service import mt5_service
        import MetaTrader5 as mt5
        from datetime import datetime, timedelta
        import numpy as np
        from services.data_converter import DataConverter

        # 确保MT5连接
        if not mt5_service.connected:
            if not mt5_service.connect():
                return jsonify({'success': False, 'error': 'MT5连接失败'})

        symbol = 'XAUUSD'

        # 获取不同时间框架的数据
        now = datetime.now()

        # 1. 获取日线数据（用于年度趋势分析）
        daily_rates = mt5.copy_rates_from(symbol, mt5.TIMEFRAME_D1, now - timedelta(days=365), 365)

        # 2. 获取4小时数据（用于周度分析）
        h4_rates = mt5.copy_rates_from(symbol, mt5.TIMEFRAME_H4, now - timedelta(days=30), 180)

        # 3. 获取1小时数据（用于趋势分析）
        h1_rates = mt5.copy_rates_from(symbol, mt5.TIMEFRAME_H1, now - timedelta(days=7), 168)

        # 4. 获取15分钟数据（用于短期分析）
        m15_rates = mt5.copy_rates_from(symbol, mt5.TIMEFRAME_M15, now - timedelta(hours=24), 96)

        # 检查数据是否获取成功
        if (daily_rates is None or len(daily_rates) == 0 or
            h4_rates is None or len(h4_rates) == 0 or
            h1_rates is None or len(h1_rates) == 0 or
            m15_rates is None or len(m15_rates) == 0):
            return jsonify({'success': False, 'error': '无法获取历史数据'})

        # 打印数据类型用于调试
        print(f"📊 数据类型检查:")
        print(f"  daily_rates: {type(daily_rates)}, 长度: {len(daily_rates)}")
        print(f"  h4_rates: {type(h4_rates)}, 长度: {len(h4_rates)}")
        print(f"  h1_rates: {type(h1_rates)}, 长度: {len(h1_rates)}")
        print(f"  m15_rates: {type(m15_rates)}, 长度: {len(m15_rates)}")

        if len(h4_rates) > 0:
            print(f"  h4_rates[0] 类型: {type(h4_rates[0])}")
            if hasattr(h4_rates[0], 'high'):
                print(f"  h4_rates[0].high: {h4_rates[0].high} (类型: {type(h4_rates[0].high)})")
            else:
                print(f"  h4_rates[0]: {h4_rates[0]}")

        # 分析年度趋势
        yearly_analysis = analyze_yearly_trend(daily_rates)

        # 分析周度波动范围
        weekly_analysis = analyze_weekly_range(h4_rates)

        # 分析隔夜极值
        overnight_analysis = analyze_overnight_extreme(h1_rates, m15_rates)

        # 分析当前趋势方向
        trend_analysis = analyze_trend_direction(h1_rates, m15_rates)

        # 获取当前价格
        current_tick = mt5.symbol_info_tick(symbol)
        current_price = current_tick.bid if current_tick else 0

        analysis_result = {
            'yearlyTrend': yearly_analysis,
            'weeklyRange': weekly_analysis,
            'overnightExtreme': overnight_analysis,
            'trendDirection': trend_analysis,
            'currentPrice': current_price,
            'timestamp': now.isoformat(),
            'symbol': symbol
        }

        # 使用数据转换器确保JSON序列化安全
        clean_result = DataConverter.clean_for_json(analysis_result)

        return jsonify({
            'success': True,
            'analysis': clean_result
        })

    except Exception as e:
        print(f"❌ 市场分析失败: {e}")
        return jsonify({'success': False, 'error': str(e)})

def analyze_yearly_trend(daily_rates):
    """分析年度趋势"""
    try:
        if len(daily_rates) < 50:
            return {'direction': 'neutral', 'strength': 0, 'currentPosition': 50}

        # 计算移动平均线
        closes = [float(rate['close']) for rate in daily_rates]
        ma_50 = float(np.mean(closes[-50:]))
        ma_200 = float(np.mean(closes[-200:])) if len(closes) >= 200 else float(np.mean(closes))

        current_price = float(closes[-1])

        # 计算趋势强度
        if ma_50 > ma_200:
            direction = 'bullish'
            strength = min(100, ((ma_50 - ma_200) / ma_200) * 1000)
        elif ma_50 < ma_200:
            direction = 'bearish'
            strength = min(100, ((ma_200 - ma_50) / ma_200) * 1000)
        else:
            direction = 'neutral'
            strength = 0

        # 计算当前价格在年度范围中的位置
        year_high = float(max(closes))
        year_low = float(min(closes))
        current_position = ((current_price - year_low) / (year_high - year_low)) * 100 if year_high != year_low else 50

        return {
            'direction': direction,
            'strength': float(round(strength, 1)),
            'currentPosition': float(round(current_position, 1))
        }
    except Exception as e:
        print(f"⚠️ 年度趋势分析错误: {e}")
        return {'direction': 'neutral', 'strength': 0, 'currentPosition': 50}

def analyze_weekly_range(h4_rates):
    """分析周度波动范围"""
    try:
        # 检查输入数据
        if h4_rates is None:
            print("⚠️ h4_rates 为 None")
            return {'averageRange': 0, 'rangeBreakout': False}

        # 转换为列表格式（如果是numpy数组）
        if hasattr(h4_rates, 'tolist'):
            h4_rates = h4_rates.tolist()

        # 转换为字典格式（如果是结构化数组）
        if len(h4_rates) > 0 and not isinstance(h4_rates[0], dict):
            # MT5返回的是结构化数组，需要转换
            converted_rates = []
            for rate in h4_rates:
                if hasattr(rate, 'high') and hasattr(rate, 'low'):
                    converted_rates.append({
                        'high': float(rate.high),
                        'low': float(rate.low),
                        'open': float(rate.open),
                        'close': float(rate.close)
                    })
                else:
                    # 如果是元组或数组格式
                    converted_rates.append({
                        'high': float(rate[2]),  # high通常是第3个元素
                        'low': float(rate[3]),   # low通常是第4个元素
                        'open': float(rate[1]),  # open通常是第2个元素
                        'close': float(rate[4])  # close通常是第5个元素
                    })
            h4_rates = converted_rates

        if len(h4_rates) < 42:  # 一周42个4小时K线
            print(f"⚠️ h4_rates 数据不足: {len(h4_rates)} < 42")
            return {'averageRange': 0, 'rangeBreakout': False}

        # 计算最近一周的平均波动范围
        recent_week = h4_rates[-42:]
        daily_ranges = []

        for i in range(0, len(recent_week), 6):  # 每6个4小时K线为一天
            day_data = recent_week[i:i+6]
            if day_data and len(day_data) > 0:
                try:
                    # 安全地获取最高价和最低价
                    highs = [float(rate['high']) for rate in day_data if 'high' in rate]
                    lows = [float(rate['low']) for rate in day_data if 'low' in rate]

                    if highs and lows:
                        day_high = max(highs)
                        day_low = min(lows)
                        if day_low > 0:  # 避免除零错误
                            daily_ranges.append(((day_high - day_low) / day_low) * 100)
                except (KeyError, ValueError, TypeError) as e:
                    print(f"⚠️ 处理日数据时出错: {e}")
                    continue

        if not daily_ranges:
            print("⚠️ 无法计算日波动范围")
            return {'averageRange': 0, 'rangeBreakout': False}

        average_range = float(np.mean(daily_ranges))

        # 检查是否突破范围
        current_range = float(daily_ranges[-1]) if daily_ranges else 0

        # 安全的比较操作
        if average_range > 0:
            range_breakout = bool(current_range > average_range * 1.5)
        else:
            range_breakout = False

        return {
            'averageRange': float(round(average_range, 2)),
            'rangeBreakout': range_breakout
        }
    except Exception as e:
        print(f"⚠️ 周度范围分析错误: {e}")
        import traceback
        traceback.print_exc()
        return {'averageRange': 0, 'rangeBreakout': False}

def analyze_overnight_extreme(h1_rates, m15_rates):
    """分析隔夜极值"""
    try:
        if len(h1_rates) < 24 or len(m15_rates) < 96:
            return {'extremeType': None, 'reversal': False, 'priceChange': 0}

        # 获取昨夜数据（假设为最近8小时）
        overnight_data = h1_rates[-8:]
        current_data = m15_rates[-4:]  # 最近1小时

        overnight_high = float(max(rate['high'] for rate in overnight_data))
        overnight_low = float(min(rate['low'] for rate in overnight_data))
        overnight_close = float(overnight_data[-1]['close'])

        current_price = float(current_data[-1]['close'])

        # 判断极值类型
        if abs(overnight_close - overnight_low) < abs(overnight_close - overnight_high):
            extreme_type = 'low'
            price_change = ((current_price - overnight_low) / overnight_low) * 100
        else:
            extreme_type = 'high'
            price_change = ((overnight_high - current_price) / overnight_high) * 100

        # 判断是否有反转
        reversal = bool(price_change > 0.1)  # 反转幅度超过0.1%

        return {
            'extremeType': extreme_type,
            'reversal': reversal,
            'priceChange': float(round(price_change, 2))
        }
    except Exception as e:
        print(f"⚠️ 隔夜极值分析错误: {e}")
        return {'extremeType': None, 'reversal': False, 'priceChange': 0}

def analyze_trend_direction(h1_rates, m15_rates):
    """分析趋势方向"""
    if len(h1_rates) < 20 or len(m15_rates) < 20:
        return {'direction': 'neutral', 'isTrending': False, 'strength': 0}

    # 使用EMA计算趋势
    h1_closes = [rate['close'] for rate in h1_rates[-20:]]
    m15_closes = [rate['close'] for rate in m15_rates[-20:]]

    # 计算EMA
    def calculate_ema(prices, period):
        multiplier = 2 / (period + 1)
        ema = [prices[0]]
        for price in prices[1:]:
            ema.append((price * multiplier) + (ema[-1] * (1 - multiplier)))
        return ema

    h1_ema = calculate_ema(h1_closes, 10)
    m15_ema = calculate_ema(m15_closes, 10)

    # 判断趋势方向 - 修复数组比较问题
    try:
        # 确保获取标量值而不是数组
        h1_current = float(h1_ema[-1])
        h1_previous = float(h1_ema[-5])
        m15_current = float(m15_ema[-1])
        m15_previous = float(m15_ema[-5])

        h1_trend = 1 if h1_current > h1_previous else -1 if h1_current < h1_previous else 0
        m15_trend = 1 if m15_current > m15_previous else -1 if m15_current < m15_previous else 0

        # 综合判断
        if h1_trend == 1 and m15_trend == 1:
            direction = 'bullish'
            is_trending = True
            strength = min(100, abs((h1_current - h1_previous) / h1_previous) * 1000)
        elif h1_trend == -1 and m15_trend == -1:
            direction = 'bearish'
            is_trending = True
            strength = min(100, abs((h1_previous - h1_current) / h1_current) * 1000)
        else:
            direction = 'neutral'
            is_trending = False
            strength = 0
    except (IndexError, TypeError, ZeroDivisionError) as e:
        print(f"⚠️ 趋势分析计算错误: {e}")
        direction = 'neutral'
        is_trending = False
        strength = 0

    return {
        'direction': direction,
        'isTrending': is_trending,
        'strength': round(strength, 1)
    }


# ==================== 入场信号相关API ====================

@app.route('/api/entry-signals', methods=['POST'])
@login_required
def save_entry_signal():
    """保存入场信号"""
    try:
        from models import EntrySignal

        data = request.get_json()

        # 创建入场信号记录
        signal = EntrySignal(
            user_id=current_user.id,
            signal_type=data.get('signal_type'),
            symbol=data.get('symbol', 'XAUUSD'),
            action=data.get('action'),
            strength=data.get('strength'),
            confidence=data.get('confidence'),
            signal_price=data.get('signal_price'),
            entry_price=data.get('entry_price'),
            is_executed=data.get('is_executed', False),
            execution_result=data.get('execution_result'),
            execution_reason=data.get('execution_reason'),
            trade_id=data.get('trade_id'),
            market_analysis=json.dumps(data.get('market_analysis')) if data.get('market_analysis') else None,
            signal_time=datetime.utcnow(),
            execution_time=datetime.utcnow() if data.get('is_executed') else None
        )

        db.session.add(signal)
        db.session.commit()

        return jsonify({
            'success': True,
            'signal_id': signal.id,
            'message': '入场信号已保存'
        })

    except Exception as e:
        db.session.rollback()
        logger.error(f"保存入场信号失败: {e}")
        return jsonify({'error': str(e)}), 500


@app.route('/api/entry-signals', methods=['GET'])
@login_required
def get_entry_signals():
    """获取入场信号列表"""
    try:
        from models import EntrySignal

        # 获取查询参数
        limit = request.args.get('limit', 50, type=int)
        offset = request.args.get('offset', 0, type=int)

        # 查询用户的入场信号
        signals = EntrySignal.query.filter_by(user_id=current_user.id)\
                                 .order_by(EntrySignal.signal_time.desc())\
                                 .offset(offset)\
                                 .limit(limit)\
                                 .all()

        return jsonify({
            'success': True,
            'signals': [signal.to_dict() for signal in signals],
            'total': EntrySignal.query.filter_by(user_id=current_user.id).count()
        })

    except Exception as e:
        logger.error(f"获取入场信号失败: {e}")
        return jsonify({'error': str(e)}), 500


@app.route('/api/entry-signals/<int:signal_id>', methods=['PUT'])
@login_required
def update_entry_signal(signal_id):
    """更新入场信号执行状态"""
    try:
        from models import EntrySignal

        signal = EntrySignal.query.filter_by(id=signal_id, user_id=current_user.id).first()
        if not signal:
            return jsonify({'error': '信号不存在'}), 404

        data = request.get_json()

        # 更新执行状态
        if 'is_executed' in data:
            signal.is_executed = data['is_executed']
        if 'execution_result' in data:
            signal.execution_result = data['execution_result']
        if 'execution_reason' in data:
            signal.execution_reason = data['execution_reason']
        if 'entry_price' in data:
            signal.entry_price = data['entry_price']
        if 'trade_id' in data:
            signal.trade_id = data['trade_id']

        if data.get('is_executed') and not signal.execution_time:
            signal.execution_time = datetime.utcnow()

        db.session.commit()

        return jsonify({
            'success': True,
            'message': '入场信号已更新'
        })

    except Exception as e:
        db.session.rollback()
        logger.error(f"更新入场信号失败: {e}")
        return jsonify({'error': str(e)}), 500

# ==================== 深度学习API路由 ====================

@app.route('/api/deep-learning/gpu-status', methods=['GET'])
@login_required
def api_deep_learning_gpu_status():
    """获取GPU状态"""
    try:
        status = deep_learning_service.get_gpu_status()
        return jsonify({
            'success': True,
            'status': status
        })
    except Exception as e:
        logger.error(f"获取GPU状态失败: {e}")
        return jsonify({
            'success': False,
            'error': str(e)
        }), 500

@app.route('/api/deep-learning/start-training', methods=['POST'])
@login_required
def api_deep_learning_start_training():
    """启动模型训练"""
    try:
        config = request.get_json()

        # 验证必要参数
        required_fields = ['model_name', 'model_type', 'symbol', 'timeframe']
        for field in required_fields:
            if not config.get(field):
                return jsonify({
                    'success': False,
                    'error': f'缺少必要参数: {field}'
                }), 400

        result = deep_learning_service.start_training(config, current_user.id)

        if result['success']:
            return jsonify(result)
        else:
            return jsonify(result), 500

    except Exception as e:
        logger.error(f"启动训练失败: {e}")
        return jsonify({
            'success': False,
            'error': str(e)
        }), 500

@app.route('/api/deep-learning/training-progress/<task_id>', methods=['GET'])
@login_required
def api_deep_learning_training_progress(task_id):
    """获取训练进度"""
    try:
        result = deep_learning_service.get_training_progress(task_id)
        return jsonify(result)
    except Exception as e:
        logger.error(f"获取训练进度失败: {e}")
        return jsonify({
            'success': False,
            'error': str(e)
        }), 500

@app.route('/api/deep-learning/training-tasks', methods=['GET'])
@login_required
def api_deep_learning_training_tasks():
    """获取训练任务列表"""
    try:
        result = deep_learning_service.get_training_tasks(current_user.id)
        return jsonify(result)
    except Exception as e:
        logger.error(f"获取训练任务失败: {e}")
        return jsonify({
            'success': False,
            'error': str(e)
        }), 500

@app.route('/api/deep-learning/system-info', methods=['GET'])
@login_required
def api_deep_learning_system_info():
    """获取系统信息"""
    try:
        result = deep_learning_service.get_system_info()
        return jsonify(result)
    except Exception as e:
        logger.error(f"获取系统信息失败: {e}")
        return jsonify({
            'success': False,
            'error': str(e)
        }), 500

@app.route('/api/deep-learning/models', methods=['GET'])
@login_required
def api_deep_learning_models():
    """获取模型列表"""
    try:
        result = deep_learning_service.get_models(current_user.id)
        return jsonify(result)
    except Exception as e:
        logger.error(f"获取模型列表失败: {e}")
        return jsonify({
            'success': False,
            'error': str(e)
        }), 500

@app.route('/api/deep-learning/models/<model_id>', methods=['GET'])
@login_required
def api_deep_learning_model_detail(model_id):
    """获取模型详情"""
    try:
        result = deep_learning_service.get_model_detail(model_id, current_user.id)
        return jsonify(result)
    except Exception as e:
        logger.error(f"获取模型详情失败: {e}")
        return jsonify({
            'success': False,
            'error': str(e)
        }), 500

@app.route('/api/deep-learning/models/<model_id>', methods=['DELETE'])
@login_required
def api_deep_learning_delete_model(model_id):
    """删除模型"""
    try:
        result = deep_learning_service.delete_model(model_id, current_user.id)
        return jsonify(result)
    except Exception as e:
        logger.error(f"删除模型失败: {e}")
        return jsonify({
            'success': False,
            'error': str(e)
        }), 500

@app.route('/api/deep-learning/training/<task_id>/pause', methods=['POST'])
@login_required
def api_deep_learning_pause_training(task_id):
    """暂停训练"""
    try:
        result = deep_learning_service.pause_training(task_id)
        return jsonify(result)
    except Exception as e:
        logger.error(f"暂停训练失败: {e}")
        return jsonify({
            'success': False,
            'error': str(e)
        }), 500

@app.route('/api/deep-learning/training/<task_id>/resume', methods=['POST'])
@login_required
def api_deep_learning_resume_training(task_id):
    """恢复训练"""
    try:
        result = deep_learning_service.resume_training(task_id)
        return jsonify(result)
    except Exception as e:
        logger.error(f"恢复训练失败: {e}")
        return jsonify({
            'success': False,
            'error': str(e)
        }), 500

@app.route('/api/deep-learning/training/<task_id>/stop', methods=['POST'])
@login_required
def api_deep_learning_stop_training(task_id):
    """停止训练"""
    try:
        result = deep_learning_service.stop_training(task_id)
        return jsonify(result)
    except Exception as e:
        logger.error(f"停止训练失败: {e}")
        return jsonify({
            'success': False,
            'error': str(e)
        }), 500

@app.route('/api/deep-learning/training/<task_id>/control-status', methods=['GET'])
@login_required
def api_deep_learning_training_control_status(task_id):
    """获取训练控制状态"""
    try:
        result = deep_learning_service.get_training_control_status(task_id)
        return jsonify(result)
    except Exception as e:
        logger.error(f"获取训练控制状态失败: {e}")
        return jsonify({
            'success': False,
            'error': str(e)
        }), 500

