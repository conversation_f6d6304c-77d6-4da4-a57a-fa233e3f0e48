
import MetaTrader5 as mt5
import pandas as pd
import time
from datetime import datetime

class MT5Service:
    """MT5交易服务"""

    def __init__(self):
        self.connected = False
        self.reconnect_service = None

    def connect(self, login=None, password=None, server=None):
        """连接MT5

        Args:
            login: 账户号码 (MT5会自动使用已登录的账户)
            password: 密码 (MT5会自动使用已登录的账户)
            server: 服务器 (MT5会自动使用已登录的账户)

        Returns:
            bool: 连接是否成功
        """
        try:
            print(f"🔗 尝试连接MT5...")
            print(f"   参数: login={login}, server={server}")

            # 首先尝试初始化MT5
            if not mt5.initialize():
                error = mt5.last_error()
                print(f"❌ MT5初始化失败: {error}")

                # 如果有登录参数，尝试手动登录
                if login and password and server:
                    print(f"🔄 尝试手动登录到MT5...")
                    if mt5.initialize(login=int(login), password=password, server=server):
                        print(f"✅ MT5手动登录成功")
                        self.connected = True
                    else:
                        error = mt5.last_error()
                        print(f"❌ MT5手动登录失败: {error}")
                        return False
                else:
                    print("❌ MT5初始化失败且无登录参数")
                    return False
            else:
                print(f"✅ MT5初始化成功")
                self.connected = True

            # 获取账户信息验证连接
            account_info = mt5.account_info()
            if account_info:
                print(f"✅ MT5连接成功 - 账户: {account_info.login}")
                print(f"   服务器: {account_info.server}")
                print(f"   余额: ${account_info.balance:.2f}")
                print(f"   权益: ${account_info.equity:.2f}")
                print(f"   公司: {account_info.company}")

                # 启动自动重连监控
                self._start_reconnect_monitoring()

                return True
            else:
                print("⚠️  MT5连接成功，但无账户信息")
                # 即使没有账户信息，也可能是演示模式
                if server and 'demo' in server.lower():
                    print("🎯 检测到演示模式，启用模拟功能")
                    return True
                else:
                    print("❌ 无法获取账户信息，请在MT5客户端中登录账户")
                    return False

        except ImportError as e:
            print(f"❌ MT5库导入失败: {e}")
            print("请安装MetaTrader5库: pip install MetaTrader5")
            return False
        except Exception as e:
            print(f"❌ MT5连接异常: {e}")
            return False

    def disconnect(self):
        """断开连接"""
        if self.connected:
            # 停止重连监控
            self._stop_reconnect_monitoring()

            mt5.shutdown()
            self.connected = False
            print("🔌 MT5连接已关闭")

    def _start_reconnect_monitoring(self):
        """启动重连监控"""
        try:
            from .mt5_reconnect_service import get_reconnect_service
            self.reconnect_service = get_reconnect_service(self)
            self.reconnect_service.start_monitoring()
            print("🔄 MT5自动重连监控已启动")
        except Exception as e:
            print(f"⚠️ 启动重连监控失败: {e}")

    def _stop_reconnect_monitoring(self):
        """停止重连监控"""
        if self.reconnect_service:
            try:
                self.reconnect_service.stop_monitoring()
                print("⏹️ MT5自动重连监控已停止")
            except Exception as e:
                print(f"⚠️ 停止重连监控失败: {e}")

    def force_reconnect(self):
        """强制重连"""
        if self.reconnect_service:
            self.reconnect_service.force_reconnect()
        else:
            print("⚠️ 重连服务未启动")

    def get_connection_status(self):
        """获取连接状态"""
        base_status = {
            'connected': self.connected,
            'reconnect_service_active': self.reconnect_service is not None
        }

        if self.reconnect_service:
            reconnect_status = self.reconnect_service.get_connection_status()
            base_status.update(reconnect_status)

        return base_status

    def set_auto_reconnect(self, enabled):
        """设置自动重连"""
        if self.reconnect_service:
            self.reconnect_service.set_auto_reconnect(enabled)
        else:
            print("⚠️ 重连服务未启动")

    def get_account_info(self):
        """获取账户信息"""
        try:
            print("🔍 获取MT5账户信息...")

            # 尝试初始化MT5连接
            if not self.connected:
                print("🔄 MT5未连接，尝试初始化...")
                if not mt5.initialize():
                    print("❌ MT5初始化失败")
                    return self._get_demo_account_info()
                self.connected = True

            account_info = mt5.account_info()
            if account_info:
                print(f"✅ 成功获取MT5账户信息: {account_info.login}")

                # 判断账户类型 - 使用更准确的方法
                if hasattr(account_info, 'trade_mode'):
                    # trade_mode: 0=demo, 1=contest, 2=real
                    account_type = 'demo' if account_info.trade_mode == 0 else 'real'
                else:
                    # 回退到服务器名称判断，但排除MetaQuotes-Demo等真实服务器
                    server_name = account_info.server.lower()
                    if 'demo' in server_name and 'metaquotes' not in server_name:
                        account_type = 'demo'
                    else:
                        account_type = 'real'

                # 转换为字典格式，方便JSON序列化
                account_data = {
                    'login': account_info.login,
                    'name': account_info.name,
                    'server': account_info.server,
                    'currency': account_info.currency,
                    'balance': account_info.balance,
                    'equity': account_info.equity,
                    'margin': account_info.margin,
                    'margin_free': account_info.margin_free,
                    'margin_level': account_info.margin_level,
                    'leverage': account_info.leverage,
                    'trade_allowed': account_info.trade_allowed,
                    'trade_expert': account_info.trade_expert,
                    'company': account_info.company,
                    'account_type': account_type
                }

                return {
                    'success': True,
                    'account_info': account_data
                }
            else:
                print("⚠️  MT5已连接但无法获取账户信息，启用演示模式")
                return self._get_demo_account_info()

        except ImportError as e:
            print(f"❌ MT5库导入失败: {e}")
            return self._get_demo_account_info()
        except Exception as e:
            print(f"❌ 获取MT5账户信息异常: {e}")
            return self._get_demo_account_info()

    def _get_demo_account_info(self):
        """获取演示账户信息"""
        print("🎯 返回演示账户信息")
        demo_data = {
            'login': *********,
            'name': 'Demo Account',
            'server': 'Demo-Server',
            'currency': 'USD',
            'balance': 10000.0,
            'equity': 10000.0,
            'margin': 0.0,
            'margin_free': 10000.0,
            'margin_level': 0.0,
            'leverage': 100,
            'trade_allowed': True,
            'trade_expert': True,
            'company': 'MateTrade4 Demo',
            'account_type': 'demo',
            'demo_mode': True
        }

        return {
            'success': False,  # 表示这是演示数据，不是真实MT5数据
            'account_info': demo_data,
            'demo_mode': True
        }

    def get_symbols(self):
        """获取所有交易品种"""
        if not self.connected:
            return []

        symbols = mt5.symbols_get()
        if symbols:
            symbol_list = []
            for symbol in symbols:
                symbol_list.append({
                    'name': symbol.name,
                    'description': symbol.description,
                    'currency_base': symbol.currency_base,
                    'currency_profit': symbol.currency_profit,
                    'currency_margin': symbol.currency_margin,
                    'digits': symbol.digits,
                    'point': symbol.point,
                    'spread': symbol.spread,
                    'volume_min': symbol.volume_min,
                    'volume_max': symbol.volume_max,
                    'volume_step': symbol.volume_step,
                    'trade_mode': symbol.trade_mode,
                    'visible': symbol.visible,
                    'select': symbol.select
                })
            return symbol_list
        return []

    def get_symbol_info(self, symbol):
        """获取交易品种信息"""
        if not self.connected:
            return None

        symbol_info = mt5.symbol_info(symbol)
        if symbol_info:
            return {
                'name': symbol_info.name,
                'description': symbol_info.description,
                'bid': symbol_info.bid,
                'ask': symbol_info.ask,
                'last': symbol_info.last,
                'volume': symbol_info.volume,
                'time': symbol_info.time,
                'digits': symbol_info.digits,
                'point': symbol_info.point,
                'spread': symbol_info.spread,
                'volume_min': symbol_info.volume_min,
                'volume_max': symbol_info.volume_max,
                'volume_step': symbol_info.volume_step,
                'currency_base': symbol_info.currency_base,
                'currency_profit': symbol_info.currency_profit,
                'currency_margin': symbol_info.currency_margin,
                'stops_level': getattr(symbol_info, 'stops_level', 10),
                'filling_mode': getattr(symbol_info, 'filling_mode', 1)
            }
        return None

    def get_tick(self, symbol):
        """获取最新tick数据"""
        try:
            # 确保连接
            if not self.connected:
                print(f"⚠️ MT5未连接，尝试重新连接...")
                if not self.connect():
                    print(f"❌ MT5连接失败")
                    return None

            # 确保品种可用
            symbol_info = mt5.symbol_info(symbol)
            if not symbol_info:
                print(f"❌ 品种 {symbol} 不存在或不可用")
                return None

            # 如果品种未选中，尝试选中
            if not symbol_info.select:
                if not mt5.symbol_select(symbol, True):
                    print(f"❌ 无法选中品种 {symbol}")
                    return None
                print(f"✅ 品种 {symbol} 已选中")

            # 获取tick数据
            tick = mt5.symbol_info_tick(symbol)
            if tick:
                print(f"✅ 获取 {symbol} tick数据成功: bid={tick.bid}, ask={tick.ask}")
                return {
                    'time': tick.time,
                    'bid': tick.bid,
                    'ask': tick.ask,
                    'last': tick.last,
                    'volume': tick.volume,
                    'flags': tick.flags
                }
            else:
                error = mt5.last_error()
                print(f"❌ 获取 {symbol} tick数据失败: {error}")
                return None

        except Exception as e:
            print(f"❌ get_tick异常: {e}")
            return None

    def get_symbol_tick(self, symbol):
        """获取交易品种的tick数据（别名方法）"""
        return self.get_tick(symbol)

    def get_price_data(self, symbol, timeframe=mt5.TIMEFRAME_M1, count=100):
        """获取价格数据"""
        if not self.connected:
            return None

        rates = mt5.copy_rates_from_pos(symbol, timeframe, 0, count)
        if rates is not None:
            return pd.DataFrame(rates)
        return None

    def get_historical_data(self, symbol, timeframe='15m', count=200, start_date=None, end_date=None):
        """获取历史数据用于技术分析

        Args:
            symbol: 交易品种
            timeframe: 时间框架 ('1m', '5m', '15m', '30m', '1h', '4h', '1d')
            count: 数据条数 (当start_date和end_date为None时使用)
            start_date: 开始日期 (datetime对象)
            end_date: 结束日期 (datetime对象)

        Returns:
            list: 历史数据列表，包含time, open, high, low, close, volume
        """
        if not self.connected:
            print("❌ MT5未连接")
            return None

        # 转换时间框架
        timeframe_map = {
            '1m': mt5.TIMEFRAME_M1,
            '5m': mt5.TIMEFRAME_M5,
            '15m': mt5.TIMEFRAME_M15,
            '30m': mt5.TIMEFRAME_M30,
            '1h': mt5.TIMEFRAME_H1,
            '4h': mt5.TIMEFRAME_H4,
            '1d': mt5.TIMEFRAME_D1
        }

        mt5_timeframe = timeframe_map.get(timeframe, mt5.TIMEFRAME_M15)

        try:
            # 根据参数选择获取方式
            if start_date is not None and end_date is not None:
                # 使用时间范围获取数据
                rates = mt5.copy_rates_range(symbol, mt5_timeframe, start_date, end_date)
                print(f"🔍 使用时间范围获取: {start_date.strftime('%Y-%m-%d')} 至 {end_date.strftime('%Y-%m-%d')}")
            else:
                # 使用数量获取数据
                rates = mt5.copy_rates_from_pos(symbol, mt5_timeframe, 0, count)
                print(f"🔍 使用数量获取: 最近{count}条")

            if rates is not None and len(rates) > 0:
                # 转换为字典列表格式
                data = []
                for rate in rates:
                    data.append({
                        'time': pd.to_datetime(rate['time'], unit='s'),
                        'open': float(rate['open']),
                        'high': float(rate['high']),
                        'low': float(rate['low']),
                        'close': float(rate['close']),
                        'volume': int(rate['tick_volume'])
                    })

                print(f"✅ 获取到 {len(data)} 条历史数据")
                return data
            else:
                print(f"❌ 未获取到历史数据: {symbol}, {timeframe}")
                return None

        except Exception as e:
            print(f"❌ 获取历史数据异常: {str(e)}")
            return None

    def get_rates(self, symbol, timeframe=mt5.TIMEFRAME_H1, count=500):
        """获取历史价格数据（兼容旧接口）"""
        if not self.connected:
            return pd.DataFrame()

        rates = mt5.copy_rates_from_pos(symbol, timeframe, 0, count)
        if rates is not None:
            df = pd.DataFrame(rates)
            # 重命名列以匹配预期格式
            df.rename(columns={
                'open': 'Open',
                'high': 'High',
                'low': 'Low',
                'close': 'Close',
                'tick_volume': 'Volume'
            }, inplace=True)

            # 设置时间索引
            df['time'] = pd.to_datetime(df['time'], unit='s')
            df.set_index('time', inplace=True)

            return df
        return pd.DataFrame()

    def place_order(self, symbol, order_type, volume, price=None, sl=None, tp=None):
        """下单"""
        if not self.connected:
            print(f"❌ MT5未连接，无法下单")
            return None

        # 清理货币对名称
        original_symbol = symbol
        symbol = self._clean_symbol_name(symbol)
        if original_symbol != symbol:
            print(f"🔧 MT5货币对名称清理: {original_symbol} -> {symbol}")

        # 转换订单类型
        if order_type == 'buy':
            mt5_order_type = mt5.ORDER_TYPE_BUY
        elif order_type == 'sell':
            mt5_order_type = mt5.ORDER_TYPE_SELL
        else:
            print(f"❌ 不支持的订单类型: {order_type}")
            return None

        # 获取当前价格
        tick = mt5.symbol_info_tick(symbol)
        if not tick:
            print(f"❌ 无法获取{symbol}价格")
            return None

        # 设置价格
        if order_type == 'buy':
            current_price = tick.ask
        else:
            current_price = tick.bid

        print(f"📊 下单信息: {symbol} {order_type} {volume}手 @ {current_price}")

        # 获取支持的填充模式
        symbol_info = mt5.symbol_info(symbol)
        filling_mode = mt5.ORDER_FILLING_FOK  # 默认使用FOK
        if symbol_info:
            # 检查支持的填充模式
            if symbol_info.filling_mode & 2:  # 支持IOC
                filling_mode = mt5.ORDER_FILLING_IOC
            elif symbol_info.filling_mode & 1:  # 支持FOK
                filling_mode = mt5.ORDER_FILLING_FOK
            else:  # 使用返回模式
                filling_mode = mt5.ORDER_FILLING_RETURN

        request = {
            "action": mt5.TRADE_ACTION_DEAL,
            "symbol": symbol,
            "volume": volume,
            "type": mt5_order_type,
            "price": current_price,
            "deviation": 20,
            "magic": 234000,
            "comment": "智能体交易",
            "type_time": mt5.ORDER_TIME_GTC,
            "type_filling": filling_mode,
        }

        # 验证和修正止损止盈价格
        if sl or tp:
            sl, tp = self._validate_and_fix_stops(symbol, order_type, current_price, sl, tp)

        if sl:
            request["sl"] = sl
            print(f"📊 设置止损: {sl}")
        if tp:
            request["tp"] = tp
            print(f"📊 设置止盈: {tp}")

        print(f"📋 发送交易请求: {request}")
        result = mt5.order_send(request)
        print(f"📋 MT5返回结果: {result}")

        return result

    def _clean_symbol_name(self, symbol):
        """清理货币对名称，移除可能的后缀"""
        try:
            if not symbol:
                return symbol

            # 移除常见的后缀
            suffixes_to_remove = ['=X', '.', '_', '-']

            cleaned_symbol = symbol.strip().upper()

            for suffix in suffixes_to_remove:
                if suffix in cleaned_symbol:
                    cleaned_symbol = cleaned_symbol.split(suffix)[0]

            # 确保是有效的货币对格式（6个字符）
            if len(cleaned_symbol) == 6 and cleaned_symbol.isalpha():
                return cleaned_symbol

            # 如果不是标准格式，返回原始符号（可能是其他类型的交易品种）
            return symbol.strip()

        except Exception as e:
            print(f"❌ 清理货币对名称失败: {e}")
            return symbol

    def _validate_and_fix_stops(self, symbol, order_type, current_price, sl, tp):
        """验证和修正止损止盈价格"""
        try:
            # 获取交易品种信息
            symbol_info = mt5.symbol_info(symbol)
            if not symbol_info:
                print(f"❌ 无法获取{symbol}交易品种信息")
                return sl, tp

            # 获取最小距离要求（点数）
            stops_level = getattr(symbol_info, 'stops_level', 10)  # 如果没有stops_level属性，默认10点
            point = symbol_info.point
            min_distance = stops_level * point

            print(f"📊 {symbol} 止损止盈验证:")
            print(f"   - 当前价格: {current_price}")
            print(f"   - 最小距离: {stops_level} 点 ({min_distance})")
            print(f"   - 原始止损: {sl}")
            print(f"   - 原始止盈: {tp}")

            # 修正止损价格
            if sl:
                if order_type == 'buy':
                    # 买单：止损应该低于当前价格
                    if sl >= current_price:
                        sl = current_price - max(min_distance, 0.01)  # 至少1个点的距离
                        print(f"🔧 修正买单止损: {sl} (低于当前价格)")
                    elif (current_price - sl) < min_distance:
                        sl = current_price - min_distance
                        print(f"🔧 修正买单止损距离: {sl}")
                else:  # sell
                    # 卖单：止损应该高于当前价格
                    if sl <= current_price:
                        sl = current_price + max(min_distance, 0.01)
                        print(f"🔧 修正卖单止损: {sl} (高于当前价格)")
                    elif (sl - current_price) < min_distance:
                        sl = current_price + min_distance
                        print(f"🔧 修正卖单止损距离: {sl}")

            # 修正止盈价格
            if tp:
                if order_type == 'buy':
                    # 买单：止盈应该高于当前价格
                    if tp <= current_price:
                        tp = current_price + max(min_distance, 0.01)
                        print(f"🔧 修正买单止盈: {tp} (高于当前价格)")
                    elif (tp - current_price) < min_distance:
                        tp = current_price + min_distance
                        print(f"🔧 修正买单止盈距离: {tp}")
                else:  # sell
                    # 卖单：止盈应该低于当前价格
                    if tp >= current_price:
                        tp = current_price - max(min_distance, 0.01)
                        print(f"🔧 修正卖单止盈: {tp} (低于当前价格)")
                    elif (current_price - tp) < min_distance:
                        tp = current_price - min_distance
                        print(f"🔧 修正卖单止盈距离: {tp}")

            # 确保止损和止盈不相同
            if sl and tp and abs(sl - tp) < point:
                if order_type == 'buy':
                    tp = tp + point * 10  # 增加10个点的差距
                else:
                    sl = sl + point * 10
                print(f"🔧 修正止损止盈相同问题: sl={sl}, tp={tp}")

            print(f"✅ 最终止损止盈: sl={sl}, tp={tp}")
            return sl, tp

        except Exception as e:
            print(f"❌ 验证止损止盈失败: {e}")
            return sl, tp

    def get_positions(self):
        """获取持仓"""
        if not self.connected:
            return []

        positions = mt5.positions_get()
        if positions:
            position_list = []
            for pos in positions:
                position_list.append({
                    'ticket': pos.ticket,
                    'symbol': pos.symbol,
                    'type': pos.type,
                    'volume': pos.volume,
                    'price_open': pos.price_open,
                    'price_current': pos.price_current,
                    'profit': pos.profit,
                    'swap': pos.swap,
                    'comment': pos.comment,
                    'time': pos.time,
                    'magic': pos.magic
                })
            return position_list
        return []

    def close_position(self, position_id):
        """平仓"""
        if not self.connected:
            return None

        positions = mt5.positions_get(ticket=position_id)
        if positions:
            position = positions[0]

            # 构建平仓请求
            close_request = {
                "action": mt5.TRADE_ACTION_DEAL,
                "symbol": position.symbol,
                "volume": position.volume,
                "type": mt5.ORDER_TYPE_BUY if position.type == mt5.ORDER_TYPE_SELL else mt5.ORDER_TYPE_SELL,
                "position": position_id,
                "deviation": 20,
                "magic": 234000,
                "comment": "智能体平仓",
                "type_time": mt5.ORDER_TIME_GTC,
                "type_filling": mt5.ORDER_FILLING_IOC,
            }

            result = mt5.order_send(close_request)
            return result

        return None

    def place_demo_order(self, symbol, order_type, volume, price=None, sl=None, tp=None, comment=""):
        """模拟交易下单 - 会在MT5中显示"""
        if not self.connected:
            return None

        # 获取当前价格
        tick = mt5.symbol_info_tick(symbol)
        if not tick:
            return None

        if price is None:
            price = tick.ask if order_type == mt5.ORDER_TYPE_BUY else tick.bid

        # 获取支持的填充模式
        symbol_info = mt5.symbol_info(symbol)
        filling_mode = mt5.ORDER_FILLING_FOK  # 默认使用FOK
        if symbol_info:
            # 检查支持的填充模式
            if symbol_info.filling_mode & 2:  # 支持IOC
                filling_mode = mt5.ORDER_FILLING_IOC
            elif symbol_info.filling_mode & 1:  # 支持FOK
                filling_mode = mt5.ORDER_FILLING_FOK
            else:  # 使用返回模式
                filling_mode = mt5.ORDER_FILLING_RETURN

        # 模拟交易请求，使用特殊的magic number
        request = {
            "action": mt5.TRADE_ACTION_DEAL,
            "symbol": symbol,
            "volume": volume,
            "type": order_type,
            "price": price,
            "sl": sl,
            "tp": tp,
            "deviation": 20,
            "magic": 234001,  # 模拟交易专用magic number
            "comment": f"[DEMO] {comment}",
            "type_time": mt5.ORDER_TIME_GTC,
            "type_filling": filling_mode,
        }

        result = mt5.order_send(request)
        return result

    def get_demo_positions(self):
        """获取模拟交易持仓"""
        if not self.connected:
            return []

        positions = mt5.positions_get()
        if positions is None:
            return []

        demo_positions = []
        for position in positions:
            # 只返回模拟交易的持仓（magic number = 234001）
            if position.magic == 234001:
                demo_positions.append({
                    'ticket': position.ticket,
                    'symbol': position.symbol,
                    'type': position.type,
                    'volume': position.volume,
                    'open_price': position.price_open,
                    'current_price': position.price_current,
                    'profit': position.profit,
                    'swap': position.swap,
                    'comment': position.comment,
                    'time': position.time,
                    'magic': position.magic
                })

        return demo_positions

    def get_demo_history(self, days=30):
        """获取模拟交易历史"""
        if not self.connected:
            return []

        from datetime import datetime, timedelta

        # 获取最近30天的历史
        end_date = datetime.now()
        start_date = end_date - timedelta(days=days)

        deals = mt5.history_deals_get(start_date, end_date)
        if deals is None:
            return []

        demo_deals = []
        for deal in deals:
            # 只返回模拟交易的历史（magic number = 234001）
            if deal.magic == 234001:
                demo_deals.append({
                    'ticket': deal.ticket,
                    'order': deal.order,
                    'time': deal.time,
                    'type': deal.type,
                    'entry': deal.entry,
                    'symbol': deal.symbol,
                    'volume': deal.volume,
                    'price': deal.price,
                    'profit': deal.profit,
                    'swap': deal.swap,
                    'commission': deal.commission,
                    'comment': deal.comment,
                    'magic': deal.magic
                })

        return demo_deals

    def close_demo_position(self, ticket):
        """平仓模拟交易"""
        if not self.connected:
            return None

        position = mt5.positions_get(ticket=ticket)
        if not position:
            return None

        position = position[0]

        # 确保是模拟交易
        if position.magic != 234001:
            return None

        # 构建平仓请求
        if position.type == mt5.POSITION_TYPE_BUY:
            order_type = mt5.ORDER_TYPE_SELL
            price = mt5.symbol_info_tick(position.symbol).bid
        else:
            order_type = mt5.ORDER_TYPE_BUY
            price = mt5.symbol_info_tick(position.symbol).ask

        # 获取支持的填充模式
        symbol_info = mt5.symbol_info(position.symbol)
        filling_mode = mt5.ORDER_FILLING_FOK  # 默认使用FOK
        if symbol_info:
            # 检查支持的填充模式
            if symbol_info.filling_mode & 2:  # 支持IOC
                filling_mode = mt5.ORDER_FILLING_IOC
            elif symbol_info.filling_mode & 1:  # 支持FOK
                filling_mode = mt5.ORDER_FILLING_FOK
            else:  # 使用返回模式
                filling_mode = mt5.ORDER_FILLING_RETURN

        request = {
            "action": mt5.TRADE_ACTION_DEAL,
            "symbol": position.symbol,
            "volume": position.volume,
            "type": order_type,
            "position": ticket,
            "price": price,
            "deviation": 20,
            "magic": 234001,
            "comment": f"[DEMO] Close {position.comment}",
            "type_time": mt5.ORDER_TIME_GTC,
            "type_filling": filling_mode,
        }

        result = mt5.order_send(request)
        return result

    def get_trade_history(self, start_date=None, end_date=None, days=30):
        """获取交易历史

        Args:
            start_date: 开始日期 (datetime对象)
            end_date: 结束日期 (datetime对象)
            days: 如果没有指定日期，获取最近多少天的历史 (默认30天)

        Returns:
            list: 交易历史列表
        """
        if not self.connected:
            print("❌ MT5未连接，无法获取交易历史")
            return []

        try:
            from datetime import datetime, timedelta

            # 如果没有指定日期，使用默认的天数
            if start_date is None or end_date is None:
                end_date = datetime.now()
                start_date = end_date - timedelta(days=days)

            print(f"🔍 获取MT5交易历史: {start_date.strftime('%Y-%m-%d')} 到 {end_date.strftime('%Y-%m-%d')}")

            # 获取历史交易记录
            deals = mt5.history_deals_get(start_date, end_date)
            if deals is None:
                print("⚠️ 没有找到交易历史记录")
                return []

            print(f"📊 找到 {len(deals)} 条交易记录")

            trade_history = []
            for deal in deals:
                # 过滤掉余额调整等非交易记录
                if deal.type in [mt5.DEAL_TYPE_BUY, mt5.DEAL_TYPE_SELL]:
                    trade_history.append({
                        'ticket': deal.ticket,
                        'order': deal.order,
                        'time': deal.time,
                        'type': deal.type,
                        'entry': deal.entry,  # 0=入场, 1=出场
                        'symbol': deal.symbol,
                        'volume': deal.volume,
                        'price': deal.price,
                        'profit': deal.profit,
                        'swap': deal.swap,
                        'commission': deal.commission,
                        'comment': deal.comment,
                        'magic': deal.magic,
                        'reason': deal.reason,
                        'position_id': deal.position_id
                    })

            print(f"✅ 成功获取 {len(trade_history)} 条有效交易记录")
            return trade_history

        except Exception as e:
            print(f"❌ 获取MT5交易历史失败: {str(e)}")
            return []

    def close_position(self, ticket):
        """平仓真实交易持仓"""
        if not self.connected:
            return {'success': False, 'error': 'MT5未连接'}

        try:
            # 获取持仓信息
            position = None
            positions = mt5.positions_get(ticket=ticket)
            if positions:
                position = positions[0]
            else:
                return {'success': False, 'error': '持仓不存在'}

            print(f"🔄 准备平仓: Ticket {ticket}, Symbol {position.symbol}, Volume {position.volume}")

            # 构建平仓请求
            if position.type == mt5.POSITION_TYPE_BUY:
                order_type = mt5.ORDER_TYPE_SELL
            else:
                order_type = mt5.ORDER_TYPE_BUY

            # 获取当前价格
            tick = mt5.symbol_info_tick(position.symbol)
            if not tick:
                return {'success': False, 'error': '无法获取价格'}

            price = tick.bid if position.type == mt5.POSITION_TYPE_BUY else tick.ask

            request = {
                "action": mt5.TRADE_ACTION_DEAL,
                "symbol": position.symbol,
                "volume": position.volume,
                "type": order_type,
                "position": ticket,
                "price": price,
                "deviation": 20,
                "magic": position.magic,
                "comment": "Web平仓",
                "type_time": mt5.ORDER_TIME_GTC,
                "type_filling": mt5.ORDER_FILLING_IOC,
            }

            result = mt5.order_send(request)
            if result and result.retcode == mt5.TRADE_RETCODE_DONE:
                print(f"✅ 平仓成功: Order {result.order}")
                return {'success': True, 'order_id': result.order}
            else:
                error_msg = f"平仓失败: {result.comment if result else '未知错误'}"
                print(f"❌ {error_msg}")
                return {'success': False, 'error': error_msg}

        except Exception as e:
            error_msg = f"平仓异常: {str(e)}"
            print(f"❌ {error_msg}")
            return {'success': False, 'error': error_msg}

    def send_order(self, symbol, order_type, volume, price=None, sl=None, tp=None, comment="Web交易", skip_margin_check=False):
        """发送交易订单

        Args:
            symbol: 交易品种
            order_type: 订单类型 (mt5.ORDER_TYPE_BUY 或 mt5.ORDER_TYPE_SELL)
            volume: 交易量
            price: 价格 (市价单可以为None)
            sl: 止损价格
            tp: 止盈价格
            comment: 订单备注

        Returns:
            dict: 订单结果
        """
        if not self.connected:
            return {'success': False, 'error': 'MT5未连接'}

        try:
            # 1. 强制重新连接并获取账户资金状况
            if not self.connected:
                print("🔄 MT5未连接，尝试重新连接...")
                if not self.connect():
                    return {'success': False, 'error': 'MT5连接失败，请检查MT5软件状态'}

            # 强制刷新账户信息（多次尝试）
            account_info = None
            for attempt in range(3):
                account_info = mt5.account_info()
                if account_info and account_info.margin_free > 0:
                    break
                print(f"⚠️ 账户信息获取异常，尝试第{attempt + 1}次刷新...")
                time.sleep(0.5)  # 等待0.5秒后重试

            if not account_info:
                return {'success': False, 'error': '无法获取账户信息，请检查MT5连接和账户状态'}

            print(f"💰 账户资金检查:")
            print(f"   账户: {account_info.login}")
            print(f"   服务器: {account_info.server}")
            print(f"   余额: ${account_info.balance:.2f}")
            print(f"   净值: ${account_info.equity:.2f}")
            print(f"   可用保证金: ${account_info.margin_free:.2f}")
            print(f"   已用保证金: ${account_info.margin:.2f}")
            print(f"   保证金比例: {account_info.margin_level:.2f}%")
            print(f"   杠杆: 1:{account_info.leverage}")
            print(f"   交易允许: {'✅' if account_info.trade_allowed else '❌'}")

            # 计算保证金使用率
            margin_usage = (account_info.margin / account_info.equity * 100) if account_info.equity > 0 else 0
            print(f"   保证金使用率: {margin_usage:.1f}%")

            # 特别检查可用保证金异常情况
            if account_info.margin_free <= 0 and account_info.balance > 0:
                print(f"⚠️ 检测到可用保证金异常！")
                print(f"   余额正常但可用保证金为0，可能原因:")
                print(f"   1. 持仓占用了全部保证金")
                print(f"   2. 账户状态异常")
                print(f"   3. MT5数据同步问题")

                # 检查持仓情况
                positions = mt5.positions_get()
                if positions:
                    print(f"   当前持仓数量: {len(positions)}")
                    total_margin_used = sum(pos.profit for pos in positions)
                    print(f"   持仓总盈亏: ${total_margin_used:.2f}")
                else:
                    print(f"   当前无持仓，可能是MT5数据同步问题")

            # 检查基本资金条件 - 特殊处理可用保证金为0的情况
            if account_info.balance < 0:  # 只检查负余额
                return {
                    'success': False,
                    'error': f'账户余额为负：当前余额 ${account_info.balance:.2f}，请先向账户充值'
                }

            # 特殊处理：如果余额充足但可用保证金为0，可能是MT5数据同步问题
            if account_info.margin_free <= 0 and account_info.balance > 100:
                print(f"⚠️ 检测到可用保证金异常，但余额充足，尝试继续执行...")
                print(f"   将跳过保证金预检查，让MT5服务器做最终判断")
                # 不直接返回错误，继续执行让MT5服务器判断
            elif account_info.margin_free < 0:  # 只检查负保证金
                return {
                    'success': False,
                    'error': f'可用保证金为负：可用 ${account_info.margin_free:.2f}，请平掉部分持仓'
                }

            if not account_info.trade_allowed:
                return {
                    'success': False,
                    'error': '账户不允许交易，请联系经纪商开启交易权限'
                }

            # 清理货币对名称
            original_symbol = symbol
            symbol = self._clean_symbol_name(symbol)
            if original_symbol != symbol:
                print(f"🔧 MT5发送订单货币对清理: {original_symbol} -> {symbol}")

            # 2. 获取品种信息和当前价格
            symbol_info = mt5.symbol_info(symbol)
            if not symbol_info:
                return {'success': False, 'error': f'无法获取{symbol}品种信息'}

            tick = mt5.symbol_info_tick(symbol)
            if not tick:
                return {'success': False, 'error': f'无法获取{symbol}价格'}

            # 如果没有指定价格，使用市价
            if price is None:
                price = tick.ask if order_type == mt5.ORDER_TYPE_BUY else tick.bid

            # 3. 计算所需保证金（可选跳过）
            if skip_margin_check:
                print(f"⚠️ 跳过保证金检查，直接发送订单")
            else:
                try:
                    # 使用MT5内置函数计算保证金需求
                    margin_required = mt5.order_calc_margin(
                        mt5.ORDER_TYPE_BUY if order_type == mt5.ORDER_TYPE_BUY else mt5.ORDER_TYPE_SELL,
                        symbol,
                        volume,
                        price
                    )

                    if margin_required is None:
                        # 如果内置函数失败，使用估算方法
                        if symbol.startswith('XAU'):  # 黄金
                            contract_size = symbol_info.trade_contract_size or 100
                            margin_required = (volume * contract_size * price) / account_info.leverage
                        else:  # 外汇对
                            contract_size = symbol_info.trade_contract_size or 100000
                            margin_required = (volume * contract_size) / account_info.leverage

                    print(f"📊 保证金计算:")
                    print(f"   所需保证金: ${margin_required:.2f}")
                    print(f"   可用保证金: ${account_info.margin_free:.2f}")

                    # 特殊处理：如果可用保证金为0但余额充足，跳过保证金检查
                    if account_info.margin_free <= 0 and account_info.balance > 100:
                        print(f"⚠️ 可用保证金为0但余额充足，可能是MT5数据同步问题")
                        print(f"   跳过保证金预检查，让MT5服务器做最终判断")
                        print(f"   需要保证金: ${margin_required:.2f}, 账户余额: ${account_info.balance:.2f}")
                    # 更宽松的保证金检查 - 只在极度不足时才拒绝
                    elif margin_required > account_info.margin_free * 5.0 and account_info.margin_free > 0:  # 允许500%的缓冲
                        shortage = margin_required - account_info.margin_free
                        max_volume = (account_info.margin_free / margin_required) * volume

                        print(f"⚠️ 保证金极度不足，拒绝交易")
                        # 只在极度不足时才返回错误，其他情况让MT5服务器判断
                        if account_info.margin_free < 10:  # 可用保证金少于$10才拒绝
                            return {
                                'success': False,
                                'error': f'账户资金极度不足：可用保证金仅${account_info.margin_free:.2f}，建议充值后再交易'
                            }
                        else:
                            print(f"   但可用保证金${account_info.margin_free:.2f}充足，让MT5服务器最终判断")
                            # 不阻止交易，让MT5服务器做最终判断
                    elif margin_required > account_info.margin_free and account_info.margin_free > 0:
                        shortage = margin_required - account_info.margin_free
                        print(f"⚠️ 保证金可能不足，但允许MT5服务器最终判断")
                        print(f"   需要: ${margin_required:.2f}, 可用: ${account_info.margin_free:.2f}, 缺少: ${shortage:.2f}")
                        # 不阻止交易，让MT5服务器做最终判断
                    else:
                        print(f"✅ 保证金检查通过")

                except Exception as margin_error:
                    print(f"⚠️ 保证金计算失败: {margin_error}")
                    print(f"   继续执行，让MT5服务器进行最终检查")

            print(f"🔄 发送MT5订单: {symbol} {'BUY' if order_type == mt5.ORDER_TYPE_BUY else 'SELL'} {volume} @ {price}")

            # 获取支持的填充模式
            symbol_info = mt5.symbol_info(symbol)
            filling_mode = mt5.ORDER_FILLING_FOK  # 默认使用FOK
            if symbol_info:
                # 检查支持的填充模式
                if symbol_info.filling_mode & 2:  # 支持IOC
                    filling_mode = mt5.ORDER_FILLING_IOC
                elif symbol_info.filling_mode & 1:  # 支持FOK
                    filling_mode = mt5.ORDER_FILLING_FOK
                else:  # 使用返回模式
                    filling_mode = mt5.ORDER_FILLING_RETURN

            # 构建订单请求，只在止损止盈有效时才设置
            request = {
                "action": mt5.TRADE_ACTION_DEAL,
                "symbol": symbol,
                "volume": volume,
                "type": order_type,
                "price": price,
                "deviation": 20,
                "magic": 234000,  # 真实交易magic number
                "comment": comment,
                "type_time": mt5.ORDER_TIME_GTC,
                "type_filling": filling_mode,
            }

            # 验证并设置止损止盈
            current_price = price
            sl_valid = False
            tp_valid = False

            if sl and sl > 0:
                # 验证止损价格合理性
                if order_type == mt5.ORDER_TYPE_BUY and sl < current_price:
                    request["sl"] = sl
                    sl_valid = True
                    print(f"📊 设置买单止损: {sl} (当前价格: {current_price})")
                elif order_type == mt5.ORDER_TYPE_SELL and sl > current_price:
                    request["sl"] = sl
                    sl_valid = True
                    print(f"📊 设置卖单止损: {sl} (当前价格: {current_price})")
                else:
                    print(f"⚠️ 止损价格不合理，跳过设置: {sl} (当前价格: {current_price})")

            if tp and tp > 0:
                # 验证止盈价格合理性
                if order_type == mt5.ORDER_TYPE_BUY and tp > current_price:
                    request["tp"] = tp
                    tp_valid = True
                    print(f"📊 设置买单止盈: {tp} (当前价格: {current_price})")
                elif order_type == mt5.ORDER_TYPE_SELL and tp < current_price:
                    request["tp"] = tp
                    tp_valid = True
                    print(f"📊 设置卖单止盈: {tp} (当前价格: {current_price})")
                else:
                    print(f"⚠️ 止盈价格不合理，跳过设置: {tp} (当前价格: {current_price})")

            if not sl_valid and not tp_valid:
                print("⚠️ 未设置止损止盈，使用市价单")
            elif sl_valid and tp_valid:
                print("✅ 已设置止损止盈")
            elif sl_valid:
                print("✅ 已设置止损，未设置止盈")
            else:
                print("✅ 已设置止盈，未设置止损")

            result = mt5.order_send(request)
            if result and result.retcode == mt5.TRADE_RETCODE_DONE:
                print(f"✅ 订单执行成功: Order {result.order}, Deal {result.deal}")
                return {
                    'success': True,
                    'order_id': result.order,
                    'deal_id': result.deal,
                    'price': result.price,
                    'volume': result.volume,
                    'comment': result.comment
                }
            else:
                error_msg = f"订单失败: {result.comment if result else '未知错误'}"
                print(f"❌ {error_msg}")
                return {'success': False, 'error': error_msg}

        except Exception as e:
            error_msg = f"发送订单异常: {str(e)}"
            print(f"❌ {error_msg}")
            return {'success': False, 'error': error_msg}

    def get_symbol_info_tick(self, symbol):
        """获取品种的tick数据"""
        if not self.connected:
            return None

        try:
            tick = mt5.symbol_info_tick(symbol)
            return tick
        except Exception as e:
            print(f"❌ 获取{symbol}的tick数据失败: {str(e)}")
            return None

    def get_point(self, symbol):
        """获取品种的点值"""
        if not self.connected:
            return 0.0001  # 默认点值

        try:
            symbol_info = mt5.symbol_info(symbol)
            if symbol_info:
                return symbol_info.point
            else:
                return 0.0001  # 默认点值
        except Exception as e:
            print(f"❌ 获取{symbol}的点值失败: {str(e)}")
            return 0.0001  # 默认点值

    def place_order(self, symbol, order_type, lot_size, price=None, sl=None, tp=None, comment=""):
        """下单交易"""
        if not self.connected:
            return {'success': False, 'error': 'MT5未连接'}

        try:
            # 获取品种信息
            symbol_info = mt5.symbol_info(symbol)
            if not symbol_info:
                return {'success': False, 'error': f'品种{symbol}不存在'}

            # 确保品种可见
            if not symbol_info.visible:
                if not mt5.symbol_select(symbol, True):
                    return {'success': False, 'error': f'无法选择品种{symbol}'}

            # 准备交易请求
            if order_type.upper() == 'BUY':
                trade_type = mt5.ORDER_TYPE_BUY
                if price is None:
                    price = mt5.symbol_info_tick(symbol).ask
            elif order_type.upper() == 'SELL':
                trade_type = mt5.ORDER_TYPE_SELL
                if price is None:
                    price = mt5.symbol_info_tick(symbol).bid
            else:
                return {'success': False, 'error': f'不支持的订单类型: {order_type}'}

            # 构建交易请求
            request = {
                "action": mt5.TRADE_ACTION_DEAL,
                "symbol": symbol,
                "volume": lot_size,
                "type": trade_type,
                "price": price,
                "deviation": 20,
                "magic": 234000,
                "comment": comment,
                "type_time": mt5.ORDER_TIME_GTC,
                "type_filling": mt5.ORDER_FILLING_IOC,
            }

            # 添加止损止盈
            if sl is not None:
                request["sl"] = sl
            if tp is not None:
                request["tp"] = tp

            # 发送交易请求
            result = mt5.order_send(request)

            if result.retcode == mt5.TRADE_RETCODE_DONE:
                return {
                    'success': True,
                    'order_id': result.order,
                    'entry_price': result.price,
                    'volume': result.volume,
                    'comment': result.comment
                }
            else:
                error_msg = f"订单失败: {result.comment if result else '未知错误'}"
                return {'success': False, 'error': error_msg}

        except Exception as e:
            return {'success': False, 'error': f'下单异常: {str(e)}'}

    def close_position(self, position_ticket):
        """平仓指定持仓"""
        if not self.connected:
            return {'success': False, 'error': 'MT5未连接'}

        try:
            # 获取持仓信息
            positions = mt5.positions_get(ticket=position_ticket)
            if not positions:
                return {'success': False, 'error': f'未找到持仓 {position_ticket}'}

            position = positions[0]

            # 准备平仓请求
            if position.type == mt5.POSITION_TYPE_BUY:
                trade_type = mt5.ORDER_TYPE_SELL
                price = mt5.symbol_info_tick(position.symbol).bid
            else:
                trade_type = mt5.ORDER_TYPE_BUY
                price = mt5.symbol_info_tick(position.symbol).ask

            request = {
                "action": mt5.TRADE_ACTION_DEAL,
                "symbol": position.symbol,
                "volume": position.volume,
                "type": trade_type,
                "position": position_ticket,
                "price": price,
                "deviation": 20,
                "magic": 234000,
                "comment": "AI_Close",
                "type_time": mt5.ORDER_TIME_GTC,
                "type_filling": mt5.ORDER_FILLING_IOC,
            }

            # 发送平仓请求
            result = mt5.order_send(request)

            if result.retcode == mt5.TRADE_RETCODE_DONE:
                return {
                    'success': True,
                    'order_id': result.order,
                    'profit': result.profit if hasattr(result, 'profit') else 0
                }
            else:
                return {'success': False, 'error': f'平仓失败: {result.comment}'}

        except Exception as e:
            return {'success': False, 'error': f'平仓异常: {str(e)}'}

# 全局MT5服务实例
mt5_service = MT5Service()
