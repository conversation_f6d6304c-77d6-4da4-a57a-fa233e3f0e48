{% extends "base.html" %}

{% block title %}回调交易 - MateTrade{% endblock %}

{% block content %}
<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center mb-4">
                <h2>
                    <i class="fas fa-undo text-warning me-2"></i>
                    回调交易
                </h2>
                <div>
                    <span class="badge bg-info">智能回调策略</span>
                    <span class="badge bg-success">支持回测</span>
                </div>
            </div>
        </div>
    </div>

    <!-- 策略说明 -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="card border-info">
                <div class="card-header bg-info text-white">
                    <h5 class="mb-0">
                        <i class="fas fa-info-circle me-2"></i>
                        回调交易策略说明
                    </h5>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-6">
                            <h6 class="text-primary">策略原理</h6>
                            <ul class="list-unstyled">
                                <li><i class="fas fa-check text-success me-2"></i>识别主要趋势方向</li>
                                <li><i class="fas fa-check text-success me-2"></i>等待价格回调到关键支撑/阻力位</li>
                                <li><i class="fas fa-check text-success me-2"></i>在回调结束时顺势入场</li>
                                <li><i class="fas fa-check text-success me-2"></i>设置合理的止损和止盈</li>
                            </ul>
                        </div>
                        <div class="col-md-6">
                            <h6 class="text-primary">适用场景</h6>
                            <ul class="list-unstyled">
                                <li><i class="fas fa-arrow-up text-success me-2"></i>明确的上升趋势中的回调</li>
                                <li><i class="fas fa-arrow-down text-danger me-2"></i>明确的下降趋势中的反弹</li>
                                <li><i class="fas fa-chart-line text-info me-2"></i>关键支撑阻力位附近</li>
                                <li><i class="fas fa-clock text-warning me-2"></i>市场波动性适中时期</li>
                            </ul>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- 主要内容区域 -->
    <div class="row">
        <!-- 左侧：回测区域 -->
        <div class="col-lg-6">
            <div class="card">
                <div class="card-header bg-primary text-white">
                    <h5 class="mb-0">
                        <i class="fas fa-chart-bar me-2"></i>
                        回调策略回测
                    </h5>
                </div>
                <div class="card-body">
                    <!-- 回测配置 -->
                    <div class="mb-4">
                        <h6 class="text-primary mb-3">回测配置</h6>
                        
                        <div class="row">
                            <div class="col-md-6 mb-3">
                                <label class="form-label">交易品种</label>
                                <select class="form-select" id="backtestSymbol">
                                    <option value="XAUUSD" selected>黄金 (XAUUSD)</option>
                                    <option value="EURUSD">欧美 (EURUSD)</option>
                                    <option value="GBPUSD">镑美 (GBPUSD)</option>
                                    <option value="USDJPY">美日 (USDJPY)</option>
                                </select>
                            </div>
                            <div class="col-md-6 mb-3">
                                <label class="form-label">时间周期</label>
                                <select class="form-select" id="backtestTimeframe">
                                    <option value="H1" selected>1小时</option>
                                    <option value="H4">4小时</option>
                                    <option value="D1">日线</option>
                                </select>
                            </div>
                        </div>

                        <div class="row">
                            <div class="col-md-6 mb-3">
                                <label class="form-label">回测开始日期</label>
                                <input type="date" class="form-control" id="backtestStartDate">
                            </div>
                            <div class="col-md-6 mb-3">
                                <label class="form-label">回测结束日期</label>
                                <input type="date" class="form-control" id="backtestEndDate">
                            </div>
                        </div>

                        <!-- 快速时间选择 -->
                        <div class="row mb-3">
                            <div class="col-12">
                                <label class="form-label">
                                    <i class="fas fa-clock me-2"></i>
                                    快速时间选择
                                </label>
                                <div class="d-grid gap-2">
                                    <div class="btn-group btn-group-sm" role="group">
                                        <button type="button" class="btn btn-outline-primary" onclick="setQuickDateRange(1)">
                                            <i class="fas fa-calendar-day me-1"></i>
                                            1天
                                        </button>
                                        <button type="button" class="btn btn-outline-primary" onclick="setQuickDateRange(3)">
                                            <i class="fas fa-calendar-alt me-1"></i>
                                            3天
                                        </button>
                                        <button type="button" class="btn btn-outline-primary" onclick="setQuickDateRange(7)">
                                            <i class="fas fa-calendar-week me-1"></i>
                                            1周
                                        </button>
                                        <button type="button" class="btn btn-outline-primary" onclick="setQuickDateRange(30)">
                                            <i class="fas fa-calendar me-1"></i>
                                            1月
                                        </button>
                                        <button type="button" class="btn btn-outline-primary" onclick="setQuickDateRange(60)">
                                            <i class="fas fa-calendar-plus me-1"></i>
                                            2月
                                        </button>
                                    </div>
                                </div>
                                <small class="text-muted">
                                    点击快速设置回测时间范围，结束日期为今天，开始日期自动计算
                                </small>
                            </div>
                        </div>

                        <!-- 回测资金配置 -->
                        <div class="card border-info mb-3">
                            <div class="card-header">
                                <h6 class="mb-0">
                                    <i class="fas fa-dollar-sign me-2"></i>
                                    资金配置
                                </h6>
                            </div>
                            <div class="card-body">
                                <div class="row">
                                    <div class="col-md-6 mb-3">
                                        <label class="form-label">初始资金 (USD)</label>
                                        <input type="number" class="form-control" id="initialCapital" value="10000" min="1000" max="1000000" step="1000">
                                        <small class="text-muted">回测使用的初始资金</small>
                                    </div>
                                    <div class="col-md-6 mb-3">
                                        <label class="form-label">每笔交易手数</label>
                                        <input type="number" class="form-control" id="backtestLotSize" value="0.01" min="0.01" max="10" step="0.01">
                                        <small class="text-muted">每次交易的固定手数</small>
                                    </div>
                                </div>
                                <div class="row">
                                    <div class="col-md-6 mb-3">
                                        <label class="form-label">风险比例 (%)</label>
                                        <input type="number" class="form-control" id="riskPercent" value="2.0" min="0.5" max="10" step="0.1">
                                        <small class="text-muted">每笔交易的最大风险比例</small>
                                    </div>
                                    <div class="col-md-6 mb-3">
                                        <label class="form-label">最大同时持仓</label>
                                        <input type="number" class="form-control" id="maxPositions" value="4" min="1" max="10" step="1">
                                        <small class="text-muted">同时持有的最大订单数</small>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- 回调策略参数 -->
                        <div class="card border-secondary mb-3">
                            <div class="card-header">
                                <h6 class="mb-0">回调策略参数</h6>
                            </div>
                            <div class="card-body">
                                <div class="row">
                                    <div class="col-md-6 mb-3">
                                        <label class="form-label">
                                            趋势判断周期
                                            <i class="fas fa-info-circle text-info ms-1" data-bs-toggle="tooltip"
                                               title="移动平均线的计算周期。例如：H1时间框架下，20周期 = 20小时；H4时间框架下，20周期 = 80小时"></i>
                                        </label>
                                        <select class="form-select" id="trendPeriod">
                                            <option value="20" selected>20周期 (短期趋势)</option>
                                            <option value="50">50周期 (中期趋势)</option>
                                            <option value="100">100周期 (长期趋势)</option>
                                        </select>
                                        <small class="text-muted">
                                            用于判断主趋势的移动平均线周期<br>
                                            <strong>时间长度 = 周期数 × 时间框架</strong><br>
                                            例如：H1框架下20周期 = 20小时，H4框架下20周期 = 80小时
                                        </small>
                                    </div>
                                    <div class="col-md-6 mb-3">
                                        <label class="form-label">回调幅度 (%)</label>
                                        <input type="number" class="form-control" id="callbackPercent" value="38.2" step="0.1" min="10" max="80">
                                        <small class="text-muted">价格回调的最小幅度</small>
                                    </div>
                                </div>
                                <div class="row">
                                    <div class="col-md-6 mb-3">
                                        <label class="form-label">止损比例 (%)</label>
                                        <input type="number" class="form-control" id="stopLossPercent" value="2.0" step="0.1" min="0.5" max="10">
                                    </div>
                                    <div class="col-md-6 mb-3">
                                        <label class="form-label">止盈比例 (%)</label>
                                        <input type="number" class="form-control" id="takeProfitPercent" value="4.0" step="0.1" min="1" max="20">
                                    </div>
                                </div>
                            </div>
                        </div>

                        <button class="btn btn-primary w-100" onclick="startCallbackBacktest()">
                            <i class="fas fa-play me-2"></i>
                            开始回测
                        </button>
                    </div>

                    <!-- 回测结果 -->
                    <div id="backtestResults" style="display: none;">
                        <h6 class="text-primary mb-3">回测结果</h6>
                        <div id="backtestResultsContent">
                            <!-- 回测结果将在这里显示 -->
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- 右侧：自动交易区域 -->
        <div class="col-lg-6">
            <div class="card">
                <div class="card-header bg-success text-white">
                    <h5 class="mb-0">
                        <i class="fas fa-robot me-2"></i>
                        回调自动交易
                    </h5>
                </div>
                <div class="card-body">
                    <!-- 交易配置 -->
                    <div class="mb-4">
                        <h6 class="text-success mb-3">交易配置</h6>
                        
                        <div class="row">
                            <div class="col-md-6 mb-3">
                                <label class="form-label">交易品种</label>
                                <select class="form-select" id="tradingSymbol">
                                    <option value="XAUUSD" selected>黄金 (XAUUSD)</option>
                                    <option value="EURUSD">欧美 (EURUSD)</option>
                                    <option value="GBPUSD">镑美 (GBPUSD)</option>
                                    <option value="USDJPY">美日 (USDJPY)</option>
                                </select>
                            </div>
                            <div class="col-md-6 mb-3">
                                <label class="form-label">监控周期</label>
                                <select class="form-select" id="monitorTimeframe">
                                    <option value="H1" selected>1小时</option>
                                    <option value="H4">4小时</option>
                                </select>
                            </div>
                        </div>

                        <div class="row">
                            <div class="col-md-6 mb-3">
                                <label class="form-label">最小手数</label>
                                <input type="number" class="form-control" id="minLotSize" value="0.01" step="0.01" min="0.01" max="1">
                            </div>
                            <div class="col-md-6 mb-3">
                                <label class="form-label">最大手数</label>
                                <input type="number" class="form-control" id="maxLotSize" value="0.1" step="0.01" min="0.01" max="10">
                            </div>
                        </div>

                        <!-- 回调交易参数 -->
                        <div class="card border-success mb-3">
                            <div class="card-header">
                                <h6 class="mb-0">回调交易参数</h6>
                            </div>
                            <div class="card-body">
                                <div class="row">
                                    <div class="col-md-6 mb-3">
                                        <label class="form-label">
                                            趋势判断周期
                                            <i class="fas fa-info-circle text-info ms-1" data-bs-toggle="tooltip"
                                               title="移动平均线的计算周期。例如：H1时间框架下，20周期 = 20小时；H4时间框架下，20周期 = 80小时"></i>
                                        </label>
                                        <select class="form-select" id="liveTrendPeriod">
                                            <option value="20" selected>20周期 (短期趋势)</option>
                                            <option value="50">50周期 (中期趋势)</option>
                                            <option value="100">100周期 (长期趋势)</option>
                                        </select>
                                        <small class="text-muted">
                                            移动平均线周期，时间长度 = 周期数 × 时间框架
                                        </small>
                                    </div>
                                    <div class="col-md-6 mb-3">
                                        <label class="form-label">回调幅度 (%)</label>
                                        <input type="number" class="form-control" id="liveCallbackPercent" value="38.2" step="0.1" min="10" max="80">
                                    </div>
                                </div>
                                <div class="row">
                                    <div class="col-md-6 mb-3">
                                        <label class="form-label">止损比例 (%)</label>
                                        <input type="number" class="form-control" id="liveStopLoss" value="2.0" step="0.1" min="0.5" max="10">
                                    </div>
                                    <div class="col-md-6 mb-3">
                                        <label class="form-label">止盈比例 (%)</label>
                                        <input type="number" class="form-control" id="liveTakeProfit" value="4.0" step="0.1" min="1" max="20">
                                    </div>
                                </div>
                                <div class="mb-3">
                                    <label class="form-label">每日交易限制</label>
                                    <input type="number" class="form-control" id="dailyTradeLimit" value="5" min="1" max="20">
                                    <small class="text-muted">每日最大交易次数</small>
                                </div>
                            </div>
                        </div>

                        <div class="d-grid gap-2">
                            <button class="btn btn-success" id="startCallbackTradingBtn" onclick="startCallbackTrading()">
                                <i class="fas fa-play me-2"></i>
                                启动回调交易
                            </button>
                            <button class="btn btn-danger" id="stopCallbackTradingBtn" onclick="stopCallbackTrading()" style="display: none;">
                                <i class="fas fa-stop me-2"></i>
                                停止交易
                            </button>
                        </div>
                    </div>

                    <!-- 交易状态 -->
                    <div id="callbackTradingStatus" style="display: none;">
                        <h6 class="text-success mb-3">交易状态</h6>
                        <div id="callbackTradingStatusContent">
                            <!-- 交易状态将在这里显示 -->
                        </div>
                    </div>

                    <!-- 实时监控 -->
                    <div id="callbackMonitoring" style="display: none;">
                        <h6 class="text-info mb-3">实时监控</h6>
                        <div id="callbackMonitoringContent">
                            <!-- 监控信息将在这里显示 -->
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- 交易记录 -->
    <div class="row mt-4">
        <div class="col-12">
            <div class="card">
                <div class="card-header">
                    <h5 class="mb-0">
                        <i class="fas fa-history me-2"></i>
                        回调交易记录
                    </h5>
                </div>
                <div class="card-body">
                    <div id="callbackTradeHistory">
                        <div class="text-center text-muted py-4">
                            <i class="fas fa-chart-line fa-3x mb-3"></i>
                            <p>暂无交易记录</p>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- 参数说明 -->
    <div class="row mt-4">
        <div class="col-12">
            <div class="card border-info">
                <div class="card-header bg-light">
                    <h6 class="mb-0">
                        <i class="fas fa-question-circle me-2"></i>
                        参数说明
                    </h6>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-6">
                            <h6 class="text-primary">
                                <i class="fas fa-chart-line me-2"></i>
                                趋势判断周期
                            </h6>
                            <p class="small mb-3">
                                <strong>定义：</strong>用于计算移动平均线的K线数量<br>
                                <strong>计算：</strong>实际时间长度 = 周期数 × 时间框架<br>
                                <strong>示例：</strong>
                            </p>
                            <ul class="small">
                                <li><strong>H1时间框架：</strong>20周期 = 20小时，50周期 = 50小时</li>
                                <li><strong>H4时间框架：</strong>20周期 = 80小时，50周期 = 200小时</li>
                                <li><strong>D1时间框架：</strong>20周期 = 20天，50周期 = 50天</li>
                            </ul>
                            <div class="alert alert-info py-2 small">
                                <i class="fas fa-lightbulb me-2"></i>
                                <strong>建议：</strong>短期交易选择20周期，中长期交易选择50-100周期
                            </div>
                        </div>
                        <div class="col-md-6">
                            <h6 class="text-success">
                                <i class="fas fa-percentage me-2"></i>
                                回调幅度
                            </h6>
                            <p class="small mb-3">
                                <strong>定义：</strong>价格从高点回调的最小百分比<br>
                                <strong>作用：</strong>过滤小幅波动，只在明显回调时入场<br>
                                <strong>常用值：</strong>
                            </p>
                            <ul class="small">
                                <li><strong>23.6%：</strong>浅度回调，入场机会多但风险较高</li>
                                <li><strong>38.2%：</strong>中度回调，平衡风险与机会（推荐）</li>
                                <li><strong>50.0%：</strong>深度回调，入场机会少但成功率高</li>
                            </ul>
                            <div class="alert alert-warning py-2 small">
                                <i class="fas fa-exclamation-triangle me-2"></i>
                                <strong>注意：</strong>回调幅度越大，交易机会越少，但单笔成功率可能更高
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- 回测进度模态框 -->
<div class="modal fade" id="backtestProgressModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">回测进行中</h5>
            </div>
            <div class="modal-body text-center">
                <div class="spinner-border text-primary mb-3" role="status">
                    <span class="visually-hidden">Loading...</span>
                </div>
                <p>正在执行回调策略回测，请稍候...</p>
                <div class="progress">
                    <div class="progress-bar" id="backtestProgressBar" style="width: 0%"></div>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
// 页面加载完成后初始化
document.addEventListener('DOMContentLoaded', function() {
    // 设置默认日期
    const endDate = new Date();
    const startDate = new Date();
    startDate.setMonth(startDate.getMonth() - 3); // 默认3个月前
    
    document.getElementById('backtestEndDate').value = endDate.toISOString().split('T')[0];
    document.getElementById('backtestStartDate').value = startDate.toISOString().split('T')[0];
    
    // 检查MT5连接状态
    checkMT5Connection();

    // 加载交易记录
    loadCallbackTradeHistory();

    // 检查并恢复交易状态
    checkAndRestoreTradingStatus();

    // 初始化tooltips
    var tooltipTriggerList = [].slice.call(document.querySelectorAll('[data-bs-toggle="tooltip"]'));
    var tooltipList = tooltipTriggerList.map(function (tooltipTriggerEl) {
        return new bootstrap.Tooltip(tooltipTriggerEl);
    });
});

// 快速设置日期范围
function setQuickDateRange(days) {
    const endDate = new Date();
    const startDate = new Date();

    // 计算开始日期
    startDate.setDate(endDate.getDate() - days);

    // 格式化日期为 YYYY-MM-DD
    const formatDate = (date) => {
        return date.toISOString().split('T')[0];
    };

    // 设置日期输入框的值
    document.getElementById('backtestEndDate').value = formatDate(endDate);
    document.getElementById('backtestStartDate').value = formatDate(startDate);

    // 添加视觉反馈
    const buttons = document.querySelectorAll('.btn-group .btn');
    buttons.forEach(btn => btn.classList.remove('active'));

    // 找到对应的按钮并添加active类
    const clickedButton = event.target.closest('button');
    if (clickedButton) {
        clickedButton.classList.add('active');

        // 2秒后移除active类
        setTimeout(() => {
            clickedButton.classList.remove('active');
        }, 2000);
    }

    // 显示设置成功的提示
    const dateRangeText = getDaysText(days);
    showAlert(`已设置回测时间范围为最近${dateRangeText}`, 'success');

    console.log(`📅 快速设置日期范围: ${formatDate(startDate)} 到 ${formatDate(endDate)} (${dateRangeText})`);
}

// 获取天数的中文描述
function getDaysText(days) {
    switch(days) {
        case 1: return '1天';
        case 3: return '3天';
        case 7: return '1周';
        case 30: return '1个月';
        case 60: return '2个月';
        default: return `${days}天`;
    }
}

// 检查并恢复交易状态
async function checkAndRestoreTradingStatus() {
    try {
        const response = await fetch('/api/callback-trading/status');

        if (response.ok) {
            const result = await response.json();

            if (result.success && result.status && result.status.running) {
                console.log('🔄 检测到回调交易正在运行，恢复UI状态');

                // 恢复UI状态
                document.getElementById('startCallbackTradingBtn').style.display = 'none';
                document.getElementById('stopCallbackTradingBtn').style.display = 'block';
                document.getElementById('callbackTradingStatus').style.display = 'block';
                document.getElementById('callbackMonitoring').style.display = 'block';

                // 更新状态信息
                if (result.status.symbol) {
                    document.getElementById('currentSymbol').textContent = result.status.symbol;
                }
                if (result.status.running_time) {
                    document.getElementById('runningTime').textContent = result.status.running_time;
                }
                if (result.status.today_trades !== undefined) {
                    document.getElementById('todayTrades').textContent = result.status.today_trades;
                }

                // 开始状态监控
                startStatusMonitoring();

                showAlert('已恢复回调交易状态', 'info');
            } else {
                console.log('📊 回调交易未运行');

                // 确保UI处于停止状态
                document.getElementById('startCallbackTradingBtn').style.display = 'block';
                document.getElementById('stopCallbackTradingBtn').style.display = 'none';
                document.getElementById('callbackTradingStatus').style.display = 'none';
                document.getElementById('callbackMonitoring').style.display = 'none';
            }
        }
    } catch (error) {
        console.error('检查交易状态失败:', error);
        // 默认显示停止状态
        document.getElementById('startCallbackTradingBtn').style.display = 'block';
        document.getElementById('stopCallbackTradingBtn').style.display = 'none';
        document.getElementById('callbackTradingStatus').style.display = 'none';
        document.getElementById('callbackMonitoring').style.display = 'none';
    }
}

// 检查MT5连接状态
async function checkMT5Connection() {
    try {
        const response = await fetch('/api/mt5/status');
        const data = await response.json();
        
        if (!data.success || !data.connected) {
            showAlert('MT5未连接，请先连接MT5终端', 'warning');
        }
    } catch (error) {
        console.error('检查MT5连接失败:', error);
    }
}

// 显示提示信息
function showAlert(message, type = 'info') {
    const alertDiv = document.createElement('div');
    alertDiv.className = `alert alert-${type} alert-dismissible fade show`;
    alertDiv.innerHTML = `
        ${message}
        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
    `;
    
    document.querySelector('.container-fluid').insertBefore(alertDiv, document.querySelector('.row'));
    
    // 5秒后自动消失
    setTimeout(() => {
        if (alertDiv.parentNode) {
            alertDiv.remove();
        }
    }, 5000);
}

// 开始回调策略回测
async function startCallbackBacktest() {
    // 获取回测配置
    const config = {
        symbol: document.getElementById('backtestSymbol').value,
        timeframe: document.getElementById('backtestTimeframe').value,
        start_date: document.getElementById('backtestStartDate').value,
        end_date: document.getElementById('backtestEndDate').value,
        // 资金配置
        initial_capital: parseFloat(document.getElementById('initialCapital').value),
        lot_size: parseFloat(document.getElementById('backtestLotSize').value),
        risk_percent: parseFloat(document.getElementById('riskPercent').value),
        max_positions: parseInt(document.getElementById('maxPositions').value),
        // 策略参数
        trend_period: parseInt(document.getElementById('trendPeriod').value),
        callback_percent: parseFloat(document.getElementById('callbackPercent').value),
        stop_loss_percent: parseFloat(document.getElementById('stopLossPercent').value),
        take_profit_percent: parseFloat(document.getElementById('takeProfitPercent').value)
    };
    
    // 验证配置
    if (!config.start_date || !config.end_date) {
        showAlert('请选择回测日期范围', 'warning');
        return;
    }
    
    if (new Date(config.start_date) >= new Date(config.end_date)) {
        showAlert('开始日期必须早于结束日期', 'warning');
        return;
    }
    
    try {
        // 显示进度模态框
        const modal = new bootstrap.Modal(document.getElementById('backtestProgressModal'));
        modal.show();
        
        // 启动回测
        const response = await fetch('/api/callback-trading/backtest', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify(config)
        });
        
        const result = await response.json();
        
        // 隐藏进度模态框
        modal.hide();
        
        if (result.success) {
            displayBacktestResults(result.results);
            showAlert('回测完成！', 'success');
        } else {
            showAlert(`回测失败: ${result.error}`, 'danger');
        }
        
    } catch (error) {
        console.error('回测失败:', error);
        showAlert('回测执行失败，请检查网络连接', 'danger');
        
        // 隐藏进度模态框
        const modal = bootstrap.Modal.getInstance(document.getElementById('backtestProgressModal'));
        if (modal) {
            modal.hide();
        }
    }
}

// 显示回测结果
function displayBacktestResults(results) {
    const resultsDiv = document.getElementById('backtestResults');
    const contentDiv = document.getElementById('backtestResultsContent');

    // 计算收益率
    const returnRate = ((results.final_balance - results.initial_capital) / results.initial_capital * 100);
    const profitFactor = results.losing_trades > 0 ? (results.gross_profit / Math.abs(results.gross_loss)) : 'N/A';

    const html = `
        <!-- 基础统计 -->
        <div class="row mb-3">
            <div class="col-md-3">
                <div class="card border-primary">
                    <div class="card-body text-center p-2">
                        <h6 class="text-primary mb-1">${results.total_trades}</h6>
                        <small class="text-muted">总交易次数</small>
                    </div>
                </div>
            </div>
            <div class="col-md-3">
                <div class="card border-success">
                    <div class="card-body text-center p-2">
                        <h6 class="text-success mb-1">${results.winning_trades}</h6>
                        <small class="text-muted">盈利次数</small>
                    </div>
                </div>
            </div>
            <div class="col-md-3">
                <div class="card border-danger">
                    <div class="card-body text-center p-2">
                        <h6 class="text-danger mb-1">${results.losing_trades}</h6>
                        <small class="text-muted">亏损次数</small>
                    </div>
                </div>
            </div>
            <div class="col-md-3">
                <div class="card border-${results.win_rate >= 50 ? 'success' : 'warning'}">
                    <div class="card-body text-center p-2">
                        <h6 class="text-${results.win_rate >= 50 ? 'success' : 'warning'} mb-1">${results.win_rate.toFixed(1)}%</h6>
                        <small class="text-muted">胜率</small>
                    </div>
                </div>
            </div>
        </div>

        <!-- 资金统计 -->
        <div class="row mb-3">
            <div class="col-md-3">
                <div class="card border-info">
                    <div class="card-body text-center p-2">
                        <h6 class="text-info mb-1">$${results.initial_capital.toLocaleString()}</h6>
                        <small class="text-muted">初始资金</small>
                    </div>
                </div>
            </div>
            <div class="col-md-3">
                <div class="card border-${results.final_balance >= results.initial_capital ? 'success' : 'danger'}">
                    <div class="card-body text-center p-2">
                        <h6 class="text-${results.final_balance >= results.initial_capital ? 'success' : 'danger'} mb-1">$${results.final_balance.toLocaleString()}</h6>
                        <small class="text-muted">最终资金</small>
                    </div>
                </div>
            </div>
            <div class="col-md-3">
                <div class="card border-${results.total_profit >= 0 ? 'success' : 'danger'}">
                    <div class="card-body text-center p-2">
                        <h6 class="text-${results.total_profit >= 0 ? 'success' : 'danger'} mb-1">$${results.total_profit.toFixed(2)}</h6>
                        <small class="text-muted">净盈亏</small>
                    </div>
                </div>
            </div>
            <div class="col-md-3">
                <div class="card border-${returnRate >= 0 ? 'success' : 'danger'}">
                    <div class="card-body text-center p-2">
                        <h6 class="text-${returnRate >= 0 ? 'success' : 'danger'} mb-1">${returnRate.toFixed(2)}%</h6>
                        <small class="text-muted">收益率</small>
                    </div>
                </div>
            </div>
        </div>

        <!-- 详细统计 -->
        <div class="row mb-3">
            <div class="col-md-3">
                <div class="card border-success">
                    <div class="card-body text-center p-2">
                        <h6 class="text-success mb-1">$${results.gross_profit.toFixed(2)}</h6>
                        <small class="text-muted">总盈利</small>
                    </div>
                </div>
            </div>
            <div class="col-md-3">
                <div class="card border-danger">
                    <div class="card-body text-center p-2">
                        <h6 class="text-danger mb-1">$${Math.abs(results.gross_loss).toFixed(2)}</h6>
                        <small class="text-muted">总亏损</small>
                    </div>
                </div>
            </div>
            <div class="col-md-3">
                <div class="card border-warning">
                    <div class="card-body text-center p-2">
                        <h6 class="text-warning mb-1">${results.max_drawdown.toFixed(2)}%</h6>
                        <small class="text-muted">最大回撤</small>
                    </div>
                </div>
            </div>
            <div class="col-md-3">
                <div class="card border-info">
                    <div class="card-body text-center p-2">
                        <h6 class="text-info mb-1">${typeof profitFactor === 'number' ? profitFactor.toFixed(2) : profitFactor}</h6>
                        <small class="text-muted">盈亏比</small>
                    </div>
                </div>
            </div>
        </div>

        <!-- 平均统计 -->
        <div class="row mb-3">
            <div class="col-md-4">
                <div class="card border-success">
                    <div class="card-body text-center p-2">
                        <h6 class="text-success mb-1">$${results.avg_win.toFixed(2)}</h6>
                        <small class="text-muted">平均盈利</small>
                    </div>
                </div>
            </div>
            <div class="col-md-4">
                <div class="card border-danger">
                    <div class="card-body text-center p-2">
                        <h6 class="text-danger mb-1">$${Math.abs(results.avg_loss).toFixed(2)}</h6>
                        <small class="text-muted">平均亏损</small>
                    </div>
                </div>
            </div>
            <div class="col-md-4">
                <div class="card border-primary">
                    <div class="card-body text-center p-2">
                        <h6 class="text-primary mb-1">${results.avg_trade_duration.toFixed(1)}h</h6>
                        <small class="text-muted">平均持仓时间</small>
                    </div>
                </div>
            </div>
        </div>

        <!-- 数据来源说明 -->
        <div class="alert alert-info py-2">
            <small>
                <i class="fas fa-info-circle me-2"></i>
                <strong>数据来源：</strong>MT5真实历史数据 |
                <strong>回测期间：</strong>${results.start_date} 至 ${results.end_date} |
                <strong>数据点数：</strong>${results.data_points} 个
            </small>
        </div>
    `;

    contentDiv.innerHTML = html;
    resultsDiv.style.display = 'block';
}

// 启动回调交易
async function startCallbackTrading() {
    // 获取交易配置
    const config = {
        symbol: document.getElementById('tradingSymbol').value,
        timeframe: document.getElementById('monitorTimeframe').value,
        min_lot_size: parseFloat(document.getElementById('minLotSize').value),
        max_lot_size: parseFloat(document.getElementById('maxLotSize').value),
        trend_period: parseInt(document.getElementById('liveTrendPeriod').value),
        callback_percent: parseFloat(document.getElementById('liveCallbackPercent').value),
        stop_loss_percent: parseFloat(document.getElementById('liveStopLoss').value),
        take_profit_percent: parseFloat(document.getElementById('liveTakeProfit').value),
        daily_trade_limit: parseInt(document.getElementById('dailyTradeLimit').value)
    };
    
    try {
        const response = await fetch('/api/callback-trading/start', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify(config)
        });
        
        const result = await response.json();
        
        if (result.success) {
            // 更新UI状态
            document.getElementById('startCallbackTradingBtn').style.display = 'none';
            document.getElementById('stopCallbackTradingBtn').style.display = 'block';
            document.getElementById('callbackTradingStatus').style.display = 'block';
            document.getElementById('callbackMonitoring').style.display = 'block';
            
            // 开始状态监控
            startStatusMonitoring();
            
            showAlert('回调交易已启动！', 'success');
        } else {
            showAlert(`启动失败: ${result.error}`, 'danger');
        }
        
    } catch (error) {
        console.error('启动回调交易失败:', error);
        showAlert('启动失败，请检查网络连接', 'danger');
    }
}

// 停止回调交易
async function stopCallbackTrading() {
    try {
        const response = await fetch('/api/callback-trading/stop', {
            method: 'POST'
        });
        
        const result = await response.json();
        
        if (result.success) {
            // 更新UI状态
            document.getElementById('startCallbackTradingBtn').style.display = 'block';
            document.getElementById('stopCallbackTradingBtn').style.display = 'none';
            document.getElementById('callbackTradingStatus').style.display = 'none';
            document.getElementById('callbackMonitoring').style.display = 'none';
            
            // 停止状态监控
            stopStatusMonitoring();
            
            showAlert('回调交易已停止！', 'info');
        } else {
            showAlert(`停止失败: ${result.error}`, 'danger');
        }
        
    } catch (error) {
        console.error('停止回调交易失败:', error);
        showAlert('停止失败，请检查网络连接', 'danger');
    }
}

let statusInterval = null;

// 开始状态监控
function startStatusMonitoring() {
    if (statusInterval) {
        clearInterval(statusInterval);
    }
    
    statusInterval = setInterval(async () => {
        await updateTradingStatus();
    }, 5000); // 每5秒更新一次
    
    // 立即执行一次
    updateTradingStatus();
}

// 停止状态监控
function stopStatusMonitoring() {
    if (statusInterval) {
        clearInterval(statusInterval);
        statusInterval = null;
    }
}

// 更新交易状态
async function updateTradingStatus() {
    try {
        const response = await fetch('/api/callback-trading/status');
        const data = await response.json();
        
        if (data.success) {
            updateStatusDisplay(data.status);
            updateMonitoringDisplay(data.monitoring);
        }
        
    } catch (error) {
        console.error('更新交易状态失败:', error);
    }
}

// 更新状态显示
function updateStatusDisplay(status) {
    const statusDiv = document.getElementById('callbackTradingStatusContent');
    
    const html = `
        <div class="alert alert-success">
            <div class="d-flex justify-content-between">
                <span><i class="fas fa-play-circle me-2"></i>运行中</span>
                <span class="badge bg-success">活跃</span>
            </div>
            <small class="d-block mt-2">
                运行时间: ${status.running_time || '00:00:00'} | 
                今日交易: ${status.today_trades || 0} 次
            </small>
        </div>
    `;
    
    statusDiv.innerHTML = html;
}

// 更新监控显示
function updateMonitoringDisplay(monitoring) {
    const monitoringDiv = document.getElementById('callbackMonitoringContent');
    
    const html = `
        <div class="row">
            <div class="col-md-6">
                <small class="text-muted">当前价格:</small><br>
                <strong>${monitoring.current_price || '--'}</strong>
            </div>
            <div class="col-md-6">
                <small class="text-muted">趋势方向:</small><br>
                <strong class="text-${monitoring.trend === 'up' ? 'success' : monitoring.trend === 'down' ? 'danger' : 'muted'}">
                    ${monitoring.trend === 'up' ? '上升' : monitoring.trend === 'down' ? '下降' : '震荡'}
                </strong>
            </div>
        </div>
        <div class="row mt-2">
            <div class="col-md-6">
                <small class="text-muted">回调状态:</small><br>
                <strong>${monitoring.callback_status || '监控中'}</strong>
            </div>
            <div class="col-md-6">
                <small class="text-muted">下次检查:</small><br>
                <strong>${monitoring.next_check || '--'}</strong>
            </div>
        </div>
    `;
    
    monitoringDiv.innerHTML = html;
}

// 加载交易记录
async function loadCallbackTradeHistory() {
    try {
        const response = await fetch('/api/callback-trading/history');
        const data = await response.json();
        
        if (data.success && data.trades && data.trades.length > 0) {
            displayTradeHistory(data.trades);
        }
        
    } catch (error) {
        console.error('加载交易记录失败:', error);
    }
}

// 显示交易记录
function displayTradeHistory(trades) {
    const historyDiv = document.getElementById('callbackTradeHistory');
    
    const html = `
        <div class="table-responsive">
            <table class="table table-striped">
                <thead>
                    <tr>
                        <th>时间</th>
                        <th>品种</th>
                        <th>方向</th>
                        <th>手数</th>
                        <th>开仓价</th>
                        <th>平仓价</th>
                        <th>盈亏</th>
                        <th>状态</th>
                    </tr>
                </thead>
                <tbody>
                    ${trades.map(trade => `
                        <tr>
                            <td>${new Date(trade.open_time).toLocaleString()}</td>
                            <td>${trade.symbol}</td>
                            <td>
                                <span class="badge bg-${trade.type === 'buy' ? 'success' : 'danger'}">
                                    ${trade.type.toUpperCase()}
                                </span>
                            </td>
                            <td>${trade.volume}</td>
                            <td>${trade.open_price}</td>
                            <td>${trade.close_price || '--'}</td>
                            <td class="text-${trade.profit >= 0 ? 'success' : 'danger'}">
                                ${trade.profit ? trade.profit.toFixed(2) : '--'}
                            </td>
                            <td>
                                <span class="badge bg-${trade.status === 'closed' ? 'secondary' : 'primary'}">
                                    ${trade.status}
                                </span>
                            </td>
                        </tr>
                    `).join('')}
                </tbody>
            </table>
        </div>
    `;
    
    historyDiv.innerHTML = html;
}
</script>
{% endblock %}
