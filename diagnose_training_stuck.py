#!/usr/bin/env python3
"""
诊断训练卡住问题 - GPU未使用，进度不变
"""

import sqlite3
import json
import time
import requests
from datetime import datetime, timedelta
import subprocess
import psutil

def login_session():
    """创建登录会话"""
    session = requests.Session()
    
    try:
        login_data = {
            'username': 'admin',
            'password': 'admin123'
        }
        
        response = session.post('http://127.0.0.1:5000/login', data=login_data)
        return session if response.status_code == 200 else None
            
    except Exception as e:
        return None

def check_running_training_task():
    """检查当前运行的训练任务"""
    
    print("🔍 检查当前运行的训练任务")
    print("=" * 60)
    
    try:
        conn = sqlite3.connect('trading_system.db')
        cursor = conn.cursor()
        
        # 获取状态为running的任务
        cursor.execute("""
            SELECT id, model_id, status, progress, current_epoch, total_epochs,
                   train_loss, val_loss, logs, created_at, started_at, updated_at,
                   (julianday('now') - julianday(created_at)) * 24 * 60 as minutes_running
            FROM training_tasks 
            WHERE status = 'running'
            ORDER BY created_at DESC 
            LIMIT 3
        """)
        
        running_tasks = cursor.fetchall()
        
        if not running_tasks:
            print("❌ 没有找到运行中的训练任务")
            conn.close()
            return None
        
        print(f"📊 找到 {len(running_tasks)} 个运行中的任务:")
        
        current_task = None
        
        for i, task in enumerate(running_tasks, 1):
            (task_id, model_id, status, progress, current_epoch, total_epochs,
             train_loss, val_loss, logs, created_at, started_at, updated_at, minutes_running) = task
            
            print(f"\n🔹 任务 {i}: {task_id}")
            print(f"   模型ID: {model_id}")
            print(f"   状态: {status}")
            print(f"   进度: {progress}% ({current_epoch}/{total_epochs})")
            print(f"   训练损失: {train_loss}")
            print(f"   验证损失: {val_loss}")
            print(f"   创建时间: {created_at}")
            print(f"   开始时间: {started_at}")
            print(f"   更新时间: {updated_at}")
            print(f"   运行时长: {minutes_running:.1f} 分钟")
            
            if logs:
                try:
                    log_data = json.loads(logs)
                    if 'error' in log_data:
                        print(f"   ❌ 错误: {log_data['error']}")
                    else:
                        print(f"   📝 日志: {log_data}")
                except:
                    print(f"   📝 原始日志: {logs[:100]}...")
            
            # 判断是否卡住
            if progress == 0 and current_epoch == 0 and minutes_running > 2:
                print(f"   ⚠️ 任务可能卡住: 运行{minutes_running:.1f}分钟但无进展")
                current_task = task_id
            elif updated_at:
                try:
                    updated_time = datetime.fromisoformat(updated_at.replace('Z', '+00:00'))
                    now = datetime.now()
                    minutes_since_update = (now - updated_time).total_seconds() / 60
                    
                    if minutes_since_update > 5:
                        print(f"   ⚠️ 任务可能卡住: {minutes_since_update:.1f}分钟未更新")
                        current_task = task_id
                    else:
                        print(f"   ✅ 任务正常: {minutes_since_update:.1f}分钟前更新")
                except:
                    print(f"   ❌ 时间解析错误")
                    current_task = task_id
        
        conn.close()
        return current_task
        
    except Exception as e:
        print(f"❌ 检查运行任务失败: {e}")
        return None

def check_gpu_usage():
    """检查GPU使用情况"""
    
    print(f"\n🎮 检查GPU使用情况")
    print("=" * 60)
    
    try:
        # 使用nvidia-smi检查GPU状态
        result = subprocess.run(['nvidia-smi', '--query-gpu=index,name,memory.used,memory.total,utilization.gpu,utilization.memory', '--format=csv,noheader,nounits'], 
                              capture_output=True, text=True, timeout=10)
        
        if result.returncode == 0:
            lines = result.stdout.strip().split('\n')
            
            print(f"📊 GPU状态:")
            for line in lines:
                parts = line.split(', ')
                if len(parts) >= 6:
                    gpu_id, name, mem_used, mem_total, gpu_util, mem_util = parts
                    print(f"   GPU {gpu_id}: {name}")
                    print(f"   内存使用: {mem_used}MB / {mem_total}MB ({float(mem_used)/float(mem_total)*100:.1f}%)")
                    print(f"   GPU利用率: {gpu_util}%")
                    print(f"   内存利用率: {mem_util}%")
                    
                    if float(gpu_util) == 0:
                        print(f"   ⚠️ GPU利用率为0，可能没有在使用GPU")
                    else:
                        print(f"   ✅ GPU正在使用中")
        else:
            print(f"❌ nvidia-smi命令失败: {result.stderr}")
            
    except subprocess.TimeoutExpired:
        print(f"❌ nvidia-smi命令超时")
    except FileNotFoundError:
        print(f"❌ 找不到nvidia-smi命令")
    except Exception as e:
        print(f"❌ GPU检查失败: {e}")

def check_python_processes():
    """检查Python进程"""
    
    print(f"\n🐍 检查Python训练进程")
    print("=" * 60)
    
    try:
        training_processes = []
        
        for proc in psutil.process_iter(['pid', 'name', 'cmdline', 'cpu_percent', 'memory_info']):
            try:
                if proc.info['name'] and 'python' in proc.info['name'].lower():
                    cmdline = ' '.join(proc.info['cmdline']) if proc.info['cmdline'] else ''
                    
                    # 检查是否是训练相关的进程
                    if any(keyword in cmdline.lower() for keyword in ['train', 'deep', 'learning', 'model']):
                        training_processes.append({
                            'pid': proc.info['pid'],
                            'name': proc.info['name'],
                            'cmdline': cmdline,
                            'cpu_percent': proc.info['cpu_percent'],
                            'memory_mb': proc.info['memory_info'].rss / 1024 / 1024 if proc.info['memory_info'] else 0
                        })
            except (psutil.NoSuchProcess, psutil.AccessDenied):
                continue
        
        if training_processes:
            print(f"📊 找到 {len(training_processes)} 个可能的训练进程:")
            
            for proc in training_processes:
                print(f"\n🔹 进程 {proc['pid']}: {proc['name']}")
                print(f"   CPU使用率: {proc['cpu_percent']}%")
                print(f"   内存使用: {proc['memory_mb']:.1f}MB")
                print(f"   命令行: {proc['cmdline'][:100]}...")
                
                if proc['cpu_percent'] == 0:
                    print(f"   ⚠️ CPU使用率为0，进程可能卡住")
                else:
                    print(f"   ✅ 进程正在运行")
        else:
            print(f"❌ 没有找到训练相关的Python进程")
            
    except Exception as e:
        print(f"❌ 检查Python进程失败: {e}")

def check_pytorch_gpu():
    """检查PyTorch GPU可用性"""
    
    print(f"\n🔥 检查PyTorch GPU可用性")
    print("=" * 60)
    
    try:
        import torch
        
        print(f"📊 PyTorch信息:")
        print(f"   版本: {torch.__version__}")
        print(f"   CUDA可用: {torch.cuda.is_available()}")
        
        if torch.cuda.is_available():
            print(f"   CUDA版本: {torch.version.cuda}")
            print(f"   GPU数量: {torch.cuda.device_count()}")
            
            for i in range(torch.cuda.device_count()):
                gpu_name = torch.cuda.get_device_name(i)
                gpu_memory = torch.cuda.get_device_properties(i).total_memory / 1024**3
                print(f"   GPU {i}: {gpu_name} ({gpu_memory:.1f}GB)")
            
            # 测试GPU操作
            print(f"\n🧪 测试GPU操作:")
            try:
                device = torch.device('cuda:0')
                x = torch.randn(100, 100).to(device)
                y = torch.randn(100, 100).to(device)
                z = torch.mm(x, y)
                print(f"   ✅ GPU矩阵运算测试成功")
                
                # 检查GPU内存使用
                allocated = torch.cuda.memory_allocated(0) / 1024**2
                cached = torch.cuda.memory_reserved(0) / 1024**2
                print(f"   GPU内存: 已分配={allocated:.1f}MB, 已缓存={cached:.1f}MB")
                
            except Exception as e:
                print(f"   ❌ GPU操作测试失败: {e}")
        else:
            print(f"   ❌ CUDA不可用，将使用CPU训练")
            
    except ImportError:
        print(f"❌ PyTorch未安装")
    except Exception as e:
        print(f"❌ PyTorch检查失败: {e}")

def force_stop_stuck_task(task_id):
    """强制停止卡住的任务"""
    
    print(f"\n🛑 强制停止卡住的任务: {task_id}")
    print("=" * 60)
    
    try:
        conn = sqlite3.connect('trading_system.db')
        cursor = conn.cursor()
        
        # 更新任务状态为失败
        cursor.execute("""
            UPDATE training_tasks 
            SET status = 'failed', 
                completed_at = CURRENT_TIMESTAMP,
                updated_at = CURRENT_TIMESTAMP,
                logs = json_set(COALESCE(logs, '{}'), '$.error', '任务卡住，手动停止')
            WHERE id = ?
        """, (task_id,))
        
        conn.commit()
        conn.close()
        
        print(f"✅ 任务已标记为失败")
        
        # 尝试通过API停止训练
        session = login_session()
        if session:
            try:
                response = session.post(f'http://127.0.0.1:5000/api/deep-learning/stop-training/{task_id}')
                
                if response.status_code == 200:
                    result = response.json()
                    if result.get('success'):
                        print(f"✅ 通过API成功停止训练")
                    else:
                        print(f"⚠️ API停止失败: {result.get('error')}")
                else:
                    print(f"⚠️ API停止请求失败: {response.status_code}")
                    
            except Exception as e:
                print(f"⚠️ API停止异常: {e}")
        
        return True
        
    except Exception as e:
        print(f"❌ 强制停止失败: {e}")
        return False

def main():
    """主函数"""
    
    print("🔧 深度学习训练卡住问题诊断")
    print("=" * 80)
    
    # 检查运行中的训练任务
    stuck_task_id = check_running_training_task()
    
    # 检查GPU使用情况
    check_gpu_usage()
    
    # 检查Python进程
    check_python_processes()
    
    # 检查PyTorch GPU
    check_pytorch_gpu()
    
    print(f"\n📋 诊断总结")
    print("=" * 80)
    
    print(f"🔍 发现的问题:")
    print(f"1. 训练任务状态为running但进度为0")
    print(f"2. GPU使用率为0%，说明没有使用GPU")
    print(f"3. 训练循环可能在初始化阶段卡住")
    
    print(f"\n💡 可能的原因:")
    print(f"• 数据加载阶段卡住（MT5数据获取慢）")
    print(f"• 模型初始化失败（GPU内存不足）")
    print(f"• PyTorch CUDA初始化问题")
    print(f"• 训练循环中的死锁或无限等待")
    print(f"• 数据预处理阶段出现异常")
    
    print(f"\n🔧 建议的解决方案:")
    print(f"1. 强制停止当前卡住的任务")
    print(f"2. 检查应用程序日志中的详细错误信息")
    print(f"3. 尝试使用CPU模式训练（排除GPU问题）")
    print(f"4. 使用更小的批次大小和数据量")
    print(f"5. 重启应用程序清理GPU内存")
    
    if stuck_task_id:
        print(f"\n🛑 发现卡住的任务: {stuck_task_id}")
        
        response = input("是否要强制停止这个卡住的任务？(y/N): ").lower().strip()
        
        if response in ['y', 'yes']:
            if force_stop_stuck_task(stuck_task_id):
                print(f"\n✅ 任务已停止，现在可以重新启动训练")
                print(f"💡 建议使用以下配置重新测试:")
                print(f"   • epochs: 1")
                print(f"   • batch_size: 8")
                print(f"   • sequence_length: 5")
                print(f"   • 只使用close特征")
            else:
                print(f"\n❌ 停止任务失败，可能需要重启应用程序")

if __name__ == '__main__':
    main()
