"""
形态测试数据生成器
用于生成测试用的价格数据和形态信号
"""

import pandas as pd
import numpy as np
from datetime import datetime, timedelta
from typing import List, Dict, Any, Tuple
import random
import logging

logger = logging.getLogger(__name__)

class PatternTestDataGenerator:
    """形态测试数据生成器"""
    
    def __init__(self):
        self.base_price = 2650.0  # 黄金基准价格
        self.volatility = 0.02    # 波动率
        
    def generate_ohlc_data(self, 
                          periods: int = 100, 
                          timeframe: str = "1H",
                          symbol: str = "XAUUSD",
                          start_time: datetime = None) -> pd.DataFrame:
        """生成OHLC数据"""
        
        if start_time is None:
            start_time = datetime.now() - timedelta(hours=periods)
        
        # 时间间隔映射
        interval_map = {
            "1M": timedelta(minutes=1),
            "5M": timedelta(minutes=5),
            "15M": timedelta(minutes=15),
            "30M": timedelta(minutes=30),
            "1H": timedelta(hours=1),
            "4H": timedelta(hours=4),
            "1D": timedelta(days=1)
        }
        
        interval = interval_map.get(timeframe, timedelta(hours=1))
        
        data = []
        current_time = start_time
        current_price = self.base_price
        
        for i in range(periods):
            # 生成价格变动
            change = np.random.normal(0, self.volatility * current_price)
            current_price += change
            
            # 确保价格在合理范围内
            current_price = max(current_price, self.base_price * 0.8)
            current_price = min(current_price, self.base_price * 1.2)
            
            # 生成OHLC
            volatility_range = current_price * 0.01
            
            open_price = current_price + np.random.normal(0, volatility_range * 0.5)
            close_price = current_price + np.random.normal(0, volatility_range * 0.5)
            
            high_price = max(open_price, close_price) + abs(np.random.normal(0, volatility_range * 0.3))
            low_price = min(open_price, close_price) - abs(np.random.normal(0, volatility_range * 0.3))
            
            # 生成成交量
            volume = random.randint(100, 1000)
            
            data.append({
                'time': current_time,
                'open': round(open_price, 2),
                'high': round(high_price, 2),
                'low': round(low_price, 2),
                'close': round(close_price, 2),
                'volume': volume,
                'symbol': symbol
            })
            
            current_time += interval
            current_price = close_price
        
        return pd.DataFrame(data)
    
    def generate_pattern_data(self, pattern_type: str, periods: int = 50) -> pd.DataFrame:
        """生成特定形态的数据"""
        
        if pattern_type == "head_and_shoulders":
            return self._generate_head_and_shoulders(periods)
        elif pattern_type == "double_top":
            return self._generate_double_top(periods)
        elif pattern_type == "double_bottom":
            return self._generate_double_bottom(periods)
        elif pattern_type == "ascending_triangle":
            return self._generate_ascending_triangle(periods)
        elif pattern_type == "descending_triangle":
            return self._generate_descending_triangle(periods)
        elif pattern_type == "support_resistance":
            return self._generate_support_resistance(periods)
        else:
            return self.generate_ohlc_data(periods)
    
    def _generate_head_and_shoulders(self, periods: int) -> pd.DataFrame:
        """生成头肩顶形态数据"""
        
        data = []
        current_time = datetime.now() - timedelta(hours=periods)
        base_price = self.base_price
        
        # 分为5个阶段：上升-左肩-下降-头部-右肩-下降
        stage_lengths = [
            periods // 6,  # 初始上升
            periods // 6,  # 左肩
            periods // 6,  # 下降到颈线
            periods // 6,  # 头部
            periods // 6,  # 右肩
            periods - 5 * (periods // 6)  # 最终下降
        ]
        
        current_price = base_price
        
        for stage, length in enumerate(stage_lengths):
            for i in range(length):
                if stage == 0:  # 初始上升
                    trend = 0.002
                elif stage == 1:  # 左肩
                    trend = 0.001 if i < length // 2 else -0.001
                elif stage == 2:  # 下降到颈线
                    trend = -0.002
                elif stage == 3:  # 头部
                    trend = 0.003 if i < length // 2 else -0.003
                elif stage == 4:  # 右肩
                    trend = 0.001 if i < length // 2 else -0.001
                else:  # 最终下降
                    trend = -0.002
                
                # 添加随机波动
                change = trend * current_price + np.random.normal(0, current_price * 0.005)
                current_price += change
                
                # 生成OHLC
                open_price = current_price + np.random.normal(0, current_price * 0.002)
                close_price = current_price + np.random.normal(0, current_price * 0.002)
                high_price = max(open_price, close_price) + abs(np.random.normal(0, current_price * 0.003))
                low_price = min(open_price, close_price) - abs(np.random.normal(0, current_price * 0.003))
                
                data.append({
                    'time': current_time,
                    'open': round(open_price, 2),
                    'high': round(high_price, 2),
                    'low': round(low_price, 2),
                    'close': round(close_price, 2),
                    'volume': random.randint(100, 1000),
                    'symbol': 'XAUUSD'
                })
                
                current_time += timedelta(hours=1)
                current_price = close_price
        
        return pd.DataFrame(data)
    
    def _generate_double_top(self, periods: int) -> pd.DataFrame:
        """生成双顶形态数据"""
        
        data = []
        current_time = datetime.now() - timedelta(hours=periods)
        base_price = self.base_price
        current_price = base_price
        
        # 分为5个阶段：上升-第一个顶-下降-第二个顶-下降
        stage_lengths = [
            periods // 5,  # 上升到第一个顶
            periods // 10, # 第一个顶
            periods // 5,  # 下降
            periods // 10, # 第二个顶
            periods - 4 * (periods // 10) - 2 * (periods // 5)  # 最终下降
        ]
        
        peak_price = base_price * 1.05
        valley_price = base_price * 0.98
        
        for stage, length in enumerate(stage_lengths):
            for i in range(length):
                if stage == 0:  # 上升到第一个顶
                    target_price = peak_price
                    trend = (target_price - current_price) / (length - i)
                elif stage == 1:  # 第一个顶
                    trend = np.random.normal(0, current_price * 0.001)
                elif stage == 2:  # 下降
                    target_price = valley_price
                    trend = (target_price - current_price) / (length - i)
                elif stage == 3:  # 第二个顶
                    trend = np.random.normal(0, current_price * 0.001)
                else:  # 最终下降
                    trend = -current_price * 0.003
                
                change = trend + np.random.normal(0, current_price * 0.005)
                current_price += change
                
                # 生成OHLC
                open_price = current_price + np.random.normal(0, current_price * 0.002)
                close_price = current_price + np.random.normal(0, current_price * 0.002)
                high_price = max(open_price, close_price) + abs(np.random.normal(0, current_price * 0.003))
                low_price = min(open_price, close_price) - abs(np.random.normal(0, current_price * 0.003))
                
                data.append({
                    'time': current_time,
                    'open': round(open_price, 2),
                    'high': round(high_price, 2),
                    'low': round(low_price, 2),
                    'close': round(close_price, 2),
                    'volume': random.randint(100, 1000),
                    'symbol': 'XAUUSD'
                })
                
                current_time += timedelta(hours=1)
                current_price = close_price
        
        return pd.DataFrame(data)
    
    def _generate_double_bottom(self, periods: int) -> pd.DataFrame:
        """生成双底形态数据"""
        
        data = []
        current_time = datetime.now() - timedelta(hours=periods)
        base_price = self.base_price
        current_price = base_price
        
        # 分为5个阶段：下降-第一个底-上升-第二个底-上升
        stage_lengths = [
            periods // 5,  # 下降到第一个底
            periods // 10, # 第一个底
            periods // 5,  # 上升
            periods // 10, # 第二个底
            periods - 4 * (periods // 10) - 2 * (periods // 5)  # 最终上升
        ]
        
        bottom_price = base_price * 0.95
        peak_price = base_price * 1.02
        
        for stage, length in enumerate(stage_lengths):
            for i in range(length):
                if stage == 0:  # 下降到第一个底
                    target_price = bottom_price
                    trend = (target_price - current_price) / (length - i)
                elif stage == 1:  # 第一个底
                    trend = np.random.normal(0, current_price * 0.001)
                elif stage == 2:  # 上升
                    target_price = peak_price
                    trend = (target_price - current_price) / (length - i)
                elif stage == 3:  # 第二个底
                    trend = np.random.normal(0, current_price * 0.001)
                else:  # 最终上升
                    trend = current_price * 0.003
                
                change = trend + np.random.normal(0, current_price * 0.005)
                current_price += change
                
                # 生成OHLC
                open_price = current_price + np.random.normal(0, current_price * 0.002)
                close_price = current_price + np.random.normal(0, current_price * 0.002)
                high_price = max(open_price, close_price) + abs(np.random.normal(0, current_price * 0.003))
                low_price = min(open_price, close_price) - abs(np.random.normal(0, current_price * 0.003))
                
                data.append({
                    'time': current_time,
                    'open': round(open_price, 2),
                    'high': round(high_price, 2),
                    'low': round(low_price, 2),
                    'close': round(close_price, 2),
                    'volume': random.randint(100, 1000),
                    'symbol': 'XAUUSD'
                })
                
                current_time += timedelta(hours=1)
                current_price = close_price
        
        return pd.DataFrame(data)
    
    def _generate_ascending_triangle(self, periods: int) -> pd.DataFrame:
        """生成上升三角形形态数据"""
        
        data = []
        current_time = datetime.now() - timedelta(hours=periods)
        base_price = self.base_price
        current_price = base_price
        
        resistance_level = base_price * 1.03
        initial_support = base_price * 0.98
        
        for i in range(periods):
            # 支撑位逐渐上升
            support_level = initial_support + (base_price - initial_support) * (i / periods)
            
            # 价格在支撑和阻力之间波动，但整体趋向突破
            if i < periods * 0.8:
                # 在三角形内波动
                range_size = resistance_level - support_level
                position_in_range = np.random.random()
                target_price = support_level + range_size * position_in_range
                
                # 添加向上的轻微偏向
                bias = (i / periods) * 0.3
                target_price += range_size * bias
            else:
                # 准备突破
                target_price = resistance_level + np.random.normal(0, base_price * 0.01)
            
            trend = (target_price - current_price) * 0.1
            change = trend + np.random.normal(0, current_price * 0.005)
            current_price += change
            
            # 生成OHLC
            open_price = current_price + np.random.normal(0, current_price * 0.002)
            close_price = current_price + np.random.normal(0, current_price * 0.002)
            high_price = max(open_price, close_price) + abs(np.random.normal(0, current_price * 0.003))
            low_price = min(open_price, close_price) - abs(np.random.normal(0, current_price * 0.003))
            
            data.append({
                'time': current_time,
                'open': round(open_price, 2),
                'high': round(high_price, 2),
                'low': round(low_price, 2),
                'close': round(close_price, 2),
                'volume': random.randint(100, 1000),
                'symbol': 'XAUUSD'
            })
            
            current_time += timedelta(hours=1)
            current_price = close_price
        
        return pd.DataFrame(data)
    
    def _generate_descending_triangle(self, periods: int) -> pd.DataFrame:
        """生成下降三角形形态数据"""
        # 与上升三角形相反的逻辑
        return self._generate_ascending_triangle(periods)  # 简化实现
    
    def _generate_support_resistance(self, periods: int) -> pd.DataFrame:
        """生成支撑阻力位数据"""
        
        data = []
        current_time = datetime.now() - timedelta(hours=periods)
        base_price = self.base_price
        current_price = base_price
        
        support_level = base_price * 0.98
        resistance_level = base_price * 1.02
        
        for i in range(periods):
            # 价格在支撑和阻力之间波动
            if current_price <= support_level:
                # 在支撑位附近反弹
                trend = abs(np.random.normal(0.001, 0.0005)) * current_price
            elif current_price >= resistance_level:
                # 在阻力位附近回落
                trend = -abs(np.random.normal(0.001, 0.0005)) * current_price
            else:
                # 在区间内随机波动
                trend = np.random.normal(0, 0.0005) * current_price
            
            change = trend + np.random.normal(0, current_price * 0.003)
            current_price += change
            
            # 确保价格不会偏离太远
            current_price = max(current_price, support_level * 0.995)
            current_price = min(current_price, resistance_level * 1.005)
            
            # 生成OHLC
            open_price = current_price + np.random.normal(0, current_price * 0.002)
            close_price = current_price + np.random.normal(0, current_price * 0.002)
            high_price = max(open_price, close_price) + abs(np.random.normal(0, current_price * 0.003))
            low_price = min(open_price, close_price) - abs(np.random.normal(0, current_price * 0.003))
            
            data.append({
                'time': current_time,
                'open': round(open_price, 2),
                'high': round(high_price, 2),
                'low': round(low_price, 2),
                'close': round(close_price, 2),
                'volume': random.randint(100, 1000),
                'symbol': 'XAUUSD'
            })
            
            current_time += timedelta(hours=1)
            current_price = close_price
        
        return pd.DataFrame(data)
    
    def get_sample_signals(self) -> List[Dict[str, Any]]:
        """获取示例信号数据"""
        
        signals = [
            {
                'pattern_name': 'Head and Shoulders Top',
                'pattern_type': 'bearish',
                'confidence': 0.85,
                'entry_point': 2645.50,
                'stop_loss': 2655.00,
                'take_profit': 2625.00,
                'timestamp': datetime.now().isoformat(),
                'timeframe': '1H',
                'symbol': 'XAUUSD',
                'description': '头肩顶形态，强烈看跌信号'
            },
            {
                'pattern_name': 'Double Bottom',
                'pattern_type': 'bullish',
                'confidence': 0.78,
                'entry_point': 2635.20,
                'stop_loss': 2625.00,
                'take_profit': 2655.00,
                'timestamp': datetime.now().isoformat(),
                'timeframe': '4H',
                'symbol': 'XAUUSD',
                'description': '双底形态，看涨信号'
            },
            {
                'pattern_name': 'Support Level',
                'pattern_type': 'bullish',
                'confidence': 0.72,
                'entry_point': 2640.00,
                'stop_loss': 2635.00,
                'take_profit': 2650.00,
                'timestamp': datetime.now().isoformat(),
                'timeframe': '1H',
                'symbol': 'XAUUSD',
                'description': '支撑位2635附近，看涨信号'
            }
        ]
        
        return signals
