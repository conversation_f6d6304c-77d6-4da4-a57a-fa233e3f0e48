<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>低风险交易系统 - 智能交易平台</title>
    
    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <!-- Font Awesome -->
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
    
    <style>
        body {
            background-color: #e8f5e8;
            color: #2d5a2d;
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
        }
        
        .navbar {
            background: linear-gradient(135deg, #28a745 0%, #20c997 100%);
            border-bottom: 2px solid #28a745;
        }
        
        .card {
            background-color: #f0f8f0;
            border: 1px solid #c8e6c8;
            border-radius: 10px;
        }
        
        .card-header {
            background: linear-gradient(135deg, #34495e 0%, #2c3e50 100%);
            border-bottom: 1px solid #404040;
            border-radius: 10px 10px 0 0 !important;
        }
        
        .btn-primary {
            background: linear-gradient(135deg, #28a745 0%, #20c997 100%);
            border: none;
        }
        
        .btn-success {
            background: linear-gradient(135deg, #27ae60 0%, #229954 100%);
            border: none;
        }
        
        .btn-danger {
            background: linear-gradient(135deg, #e74c3c 0%, #c0392b 100%);
            border: none;
        }
        
        .btn-warning {
            background: linear-gradient(135deg, #f39c12 0%, #e67e22 100%);
            border: none;
        }
        
        .btn-info {
            background: linear-gradient(135deg, #17a2b8 0%, #138496 100%);
            border: none;
        }
        
        .alert {
            border-radius: 8px;
        }
        
        .form-control, .form-select {
            background-color: #ffffff;
            border: 1px solid #a8d8a8;
            color: #2d5a2d;
        }

        .form-control:focus, .form-select:focus {
            background-color: #ffffff;
            border-color: #28a745;
            color: #2d5a2d;
            box-shadow: 0 0 0 0.2rem rgba(40, 167, 69, 0.25);
        }
        
        .form-check-input:checked {
            background-color: #3498db;
            border-color: #3498db;
        }
        
        .badge {
            font-size: 0.75em;
        }
        
        .text-muted {
            color: #adb5bd !important;
        }
        
        .border-info {
            border-color: #17a2b8 !important;
        }
        
        .bg-info {
            background-color: #17a2b8 !important;
        }
        
        .bg-opacity-10 {
            background-color: rgba(23, 162, 184, 0.1) !important;
        }
        
        .analysis-card {
            background: linear-gradient(135deg, #2c3e50 0%, #34495e 100%);
            border: 1px solid #3498db;
            border-radius: 8px;
            transition: all 0.3s ease;
        }
        
        .analysis-card:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 8px rgba(52, 152, 219, 0.3);
        }
        
        .opportunity-card {
            background: linear-gradient(135deg, #27ae60 0%, #229954 100%);
            border: 1px solid #2ecc71;
            border-radius: 8px;
            transition: all 0.3s ease;
        }
        
        .opportunity-card:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 8px rgba(46, 204, 113, 0.3);
        }
        
        .position-card {
            background: linear-gradient(135deg, #17a2b8 0%, #20c997 100%);
            border: 1px solid #17a2b8;
            border-radius: 8px;
            transition: all 0.3s ease;
        }
        
        .position-card:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 8px rgba(23, 162, 184, 0.3);
        }
        
        .stats-card {
            background: linear-gradient(135deg, #e67e22 0%, #d35400 100%);
            border: 1px solid #f39c12;
            border-radius: 8px;
            text-align: center;
            padding: 15px;
        }
        
        .control-button {
            border-radius: 8px;
            padding: 10px 20px;
            font-weight: 600;
            transition: all 0.3s ease;
        }
        
        .control-button:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 8px rgba(0, 0, 0, 0.3);
        }
        
        .system-status {
            border-radius: 8px;
            padding: 15px;
            margin-bottom: 20px;
            text-align: center;
            font-weight: 600;
        }
        
        .status-active {
            background: linear-gradient(135deg, #27ae60 0%, #229954 100%);
            border: 1px solid #2ecc71;
        }
        
        .status-inactive {
            background: linear-gradient(135deg, #95a5a6 0%, #7f8c8d 100%);
            border: 1px solid #bdc3c7;
        }
        
        .market-analysis-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 15px;
            margin-bottom: 20px;
        }
        
        .scrollable-content {
            max-height: 400px;
            overflow-y: auto;
        }
        
        .scrollable-content::-webkit-scrollbar {
            width: 6px;
        }
        
        .scrollable-content::-webkit-scrollbar-track {
            background: #2d2d2d;
        }
        
        .scrollable-content::-webkit-scrollbar-thumb {
            background: #3498db;
            border-radius: 3px;
        }
        
        .scrollable-content::-webkit-scrollbar-thumb:hover {
            background: #2980b9;
        }

        /* 回测相关样式 */
        .backtest-results {
            animation: fadeIn 0.5s ease-in;
        }

        @keyframes fadeIn {
            from { opacity: 0; transform: translateY(10px); }
            to { opacity: 1; transform: translateY(0); }
        }

        .backtest-results .bg-opacity-10 {
            border: 1px solid rgba(255, 255, 255, 0.1);
        }

        #backtestBtn:disabled {
            opacity: 0.6;
            cursor: not-allowed;
        }

        .backtest-loading {
            position: relative;
        }

        .backtest-loading::after {
            content: '';
            position: absolute;
            top: 50%;
            left: 50%;
            width: 16px;
            height: 16px;
            margin: -8px 0 0 -8px;
            border: 2px solid transparent;
            border-top: 2px solid #fff;
            border-radius: 50%;
            animation: spin 1s linear infinite;
        }

        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }

        /* 交易控制区域策略显示样式 */
        #autoTradingStatus .bg-white.bg-opacity-50 {
            border: 1px solid rgba(255, 255, 255, 0.3);
            backdrop-filter: blur(5px);
        }

        #autoTradingStrategyIcon {
            font-size: 1.2em;
            margin-right: 0.5rem;
        }

        #autoTradingStrategyName {
            font-size: 0.9rem;
            font-weight: 600;
            color: #2c3e50;
        }

        #autoTradingStrategyBadge {
            font-size: 0.7em !important;
            padding: 0.25rem 0.5rem;
        }
    </style>
</head>
<body>
    <!-- 导航栏 -->
    <nav class="navbar navbar-expand-lg navbar-dark">
        <div class="container-fluid">
            <a class="navbar-brand" href="/">
                <i class="fas fa-shield-alt me-2"></i>
                低风险交易系统
            </a>
            <div class="navbar-nav ms-auto">
                <a class="nav-link" href="/">
                    <i class="fas fa-home me-1"></i>
                    返回主页
                </a>
                <a class="nav-link" href="/pattern-monitoring">
                    <i class="fas fa-chart-line me-1"></i>
                    形态监测
                </a>
                <a class="nav-link" href="/real-trading">
                    <i class="fas fa-coins me-1"></i>
                    真实交易
                </a>
            </div>
        </div>
    </nav>

    <div class="container-fluid mt-4">
        <!-- 系统状态和控制 -->
        <div class="row mb-4">
            <div class="col-12">
                <div class="card">
                    <div class="card-header">
                        <div class="d-flex justify-content-between align-items-center">
                            <h5 class="mb-0 text-info">
                                <i class="fas fa-shield-alt me-2"></i>
                                低风险交易系统控制台
                                <span class="badge bg-info text-dark ms-2">SAFE</span>
                            </h5>
                            <div class="d-flex align-items-center gap-3">
                                <div class="form-check form-switch">
                                    <input class="form-check-input" type="checkbox" id="lowRiskTradingSwitch" onchange="toggleLowRiskTrading()">
                                    <label class="form-check-label text-info" for="lowRiskTradingSwitch">启用系统</label>
                                </div>
                                <button class="btn btn-outline-info btn-sm" onclick="showLowRiskSettings()" title="系统设置">
                                    <i class="fas fa-cog me-1"></i>设置
                                </button>
                            </div>
                        </div>
                    </div>
                    <div class="card-body">
                        <!-- 系统状态显示 -->
                        <div id="lowRiskSystemStatus" class="system-status status-inactive">
                            <i class="fas fa-power-off me-2"></i>
                            系统已停用 - 点击开关启用低风险交易系统
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- 主要内容区域 -->
        <div class="row">
            <!-- 左侧：市场分析和交易机会 -->
            <div class="col-md-8">
                <!-- 市场分析 -->
                <div class="card mb-4">
                    <div class="card-header">
                        <h6 class="mb-0 text-info">
                            <i class="fas fa-chart-area me-2"></i>
                            市场分析
                        </h6>
                    </div>
                    <div class="card-body">
                        <div class="market-analysis-grid">
                            <div class="analysis-card p-3">
                                <h6 class="text-info mb-2">年度趋势</h6>
                                <div id="yearlyTrend" class="text-warning">分析中...</div>
                                <small class="text-muted">当前位置</small>
                            </div>
                            <div class="analysis-card p-3">
                                <h6 class="text-info mb-2">周波段</h6>
                                <div id="weeklyRange" class="text-warning">分析中...</div>
                                <small class="text-muted">平均波段</small>
                            </div>
                            <div class="analysis-card p-3">
                                <h6 class="text-info mb-2">昨夜极值</h6>
                                <div id="overnightExtreme" class="text-warning">分析中...</div>
                                <small class="text-muted">反转信号</small>
                            </div>
                            <div class="analysis-card p-3">
                                <h6 class="text-info mb-2">单边检测</h6>
                                <div id="trendDetection" class="text-warning">分析中...</div>
                                <small class="text-muted">趋势强度</small>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- 交易机会 -->
                <div class="card mb-4">
                    <div class="card-header">
                        <h6 class="mb-0 text-success">
                            <i class="fas fa-bullseye me-2"></i>
                            交易机会
                        </h6>
                    </div>
                    <div class="card-body">
                        <div id="lowRiskOpportunities" class="scrollable-content">
                            <div class="text-center py-4">
                                <i class="fas fa-search fa-2x text-muted mb-3"></i>
                                <p class="text-muted">启用系统后显示交易机会</p>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- 入场信号 -->
                <div class="card mb-4">
                    <div class="card-header">
                        <h6 class="mb-0 text-primary">
                            <i class="fas fa-signal me-2"></i>
                            入场信号
                            <small class="text-muted ms-2">(自动交易执行记录)</small>
                        </h6>
                    </div>
                    <div class="card-body">
                        <div id="entrySignals" class="scrollable-content">
                            <div class="text-center py-4">
                                <i class="fas fa-wave-square fa-2x text-muted mb-3"></i>
                                <p class="text-muted">启用自动交易后显示入场信号</p>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- 历史记录查询 -->
                <div class="card">
                    <div class="card-header">
                        <h6 class="mb-0 text-info">
                            <i class="fas fa-history me-2"></i>
                            历史记录查询
                        </h6>
                    </div>
                    <div class="card-body">
                        <!-- 查询条件 -->
                        <div class="row mb-3">
                            <div class="col-md-4">
                                <label class="form-label small">查询时间范围</label>
                                <div class="btn-group w-100" role="group">
                                    <input type="radio" class="btn-check" name="historyTimeRange" id="historyToday" value="0">
                                    <label class="btn btn-outline-primary btn-sm" for="historyToday">今天</label>

                                    <input type="radio" class="btn-check" name="historyTimeRange" id="history1week" value="7" checked>
                                    <label class="btn btn-outline-primary btn-sm" for="history1week">1周</label>

                                    <input type="radio" class="btn-check" name="historyTimeRange" id="history1month" value="30">
                                    <label class="btn btn-outline-primary btn-sm" for="history1month">1个月</label>
                                </div>
                            </div>
                            <div class="col-md-4">
                                <label class="form-label small">开始日期</label>
                                <input type="date" class="form-control form-control-sm" id="historyStartDate">
                            </div>
                            <div class="col-md-4">
                                <label class="form-label small">结束日期</label>
                                <input type="date" class="form-control form-control-sm" id="historyEndDate">
                            </div>
                        </div>

                        <div class="row mb-3">
                            <div class="col-md-6">
                                <button class="btn btn-primary btn-sm" onclick="queryHistoryRecords()">
                                    <i class="fas fa-search me-1"></i>查询记录
                                </button>
                                <button class="btn btn-secondary btn-sm ms-2" onclick="exportHistoryRecords()">
                                    <i class="fas fa-download me-1"></i>导出Excel
                                </button>
                                <button class="btn btn-info btn-sm ms-2" onclick="syncTradingStatus()">
                                    <i class="fas fa-sync me-1"></i>同步状态
                                </button>
                            </div>
                            <div class="col-md-6 text-end">
                                <small class="text-muted">
                                    <i class="fas fa-info-circle me-1"></i>
                                    默认显示最近1周的交易记录
                                </small>
                                <br>
                                <small class="text-muted">
                                    <i class="fas fa-clock me-1"></i>
                                    时间显示为北京时间 (UTC+8)
                                </small>
                            </div>
                        </div>

                        <!-- 历史记录显示区域 -->
                        <div id="historyRecordsContainer" class="scrollable-content" style="max-height: 400px;">
                            <div class="text-center py-4" id="historyLoadingPlaceholder">
                                <i class="fas fa-clock fa-2x text-muted mb-3"></i>
                                <p class="text-muted">点击查询按钮获取历史记录</p>
                            </div>
                        </div>

                        <!-- 统计信息 -->
                        <div id="historyStats" class="mt-3 p-2 bg-light rounded" style="display: none;">
                            <div class="row text-center mb-2">
                                <div class="col-3">
                                    <small class="text-muted">总交易数</small>
                                    <div class="fw-bold" id="historyTotalTrades">0</div>
                                </div>
                                <div class="col-3">
                                    <small class="text-muted">盈利次数</small>
                                    <div class="fw-bold text-success" id="historyProfitTrades">0</div>
                                </div>
                                <div class="col-3">
                                    <small class="text-muted">胜率</small>
                                    <div class="fw-bold" id="historyWinRate">0%</div>
                                </div>
                                <div class="col-3">
                                    <small class="text-muted">总盈亏</small>
                                    <div class="fw-bold" id="historyTotalPnL">$0.00</div>
                                </div>
                            </div>
                            <div class="row text-center">
                                <div class="col-3">
                                    <small class="text-muted">自动交易</small>
                                    <div class="fw-bold text-info" id="historyAutoTrades">0</div>
                                </div>
                                <div class="col-3">
                                    <small class="text-muted">手动交易</small>
                                    <div class="fw-bold text-secondary" id="historyManualTrades">0</div>
                                </div>
                                <div class="col-3">
                                    <small class="text-muted">自动胜率</small>
                                    <div class="fw-bold text-success" id="historyAutoWinRate">0%</div>
                                </div>
                                <div class="col-3">
                                    <small class="text-muted">总盈亏</small>
                                    <div class="fw-bold text-primary" id="historyTotalPnL">$0.00</div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 右侧：统计和控制 -->
            <div class="col-md-4">
                <!-- 今日统计 -->
                <div class="card mb-4">
                    <div class="card-header">
                        <h6 class="mb-0 text-warning">
                            <i class="fas fa-chart-bar me-2"></i>
                            今日统计
                        </h6>
                    </div>
                    <div class="card-body">
                        <div class="row g-3">
                            <div class="col-4">
                                <div class="stats-card">
                                    <div class="h4 mb-1" id="todayTradeCount">0</div>
                                    <small>已交易</small>
                                </div>
                            </div>
                            <div class="col-4">
                                <div class="stats-card">
                                    <div class="h4 mb-1" id="dailyTradeLimit">1</div>
                                    <small>限制</small>
                                </div>
                            </div>
                            <div class="col-4">
                                <div class="stats-card">
                                    <div class="h4 mb-1 text-success" id="todayPnL">$0.00</div>
                                    <small>盈亏</small>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- 策略回测 -->
                <div class="card mb-4">
                    <div class="card-header d-flex justify-content-between align-items-center">
                        <h6 class="mb-0 text-warning">
                            <i class="fas fa-history me-2"></i>
                            策略回测
                        </h6>
                        <div class="d-flex align-items-center">
                            <span id="mt5ConnectionStatus" class="badge bg-secondary me-2">
                                <i class="fas fa-spinner fa-spin me-1"></i>检查中
                            </span>
                            <span class="badge bg-info">需要MT5</span>
                        </div>
                    </div>
                    <div class="card-body">
                        <!-- 当前策略显示 -->
                        <div class="alert alert-info py-2 mb-3" id="currentBacktestStrategy">
                            <div class="d-flex align-items-center">
                                <div class="me-2">
                                    <i class="fas fa-chess-queen text-primary"></i>
                                </div>
                                <div class="flex-grow-1">
                                    <small class="text-muted d-block mb-1">当前回测策略</small>
                                    <strong id="backtestStrategyName">优化策略 (推荐)</strong>
                                    <div class="mt-1">
                                        <small class="text-muted" id="backtestStrategyDetails">
                                            胜率: 57.1% | 月盈利: $119.96 | 每日限制: 5笔
                                        </small>
                                    </div>
                                </div>
                                <div class="text-end">
                                    <span class="badge bg-success" id="backtestStrategyStatus">
                                        <i class="fas fa-check-circle"></i>
                                        已选择
                                    </span>
                                </div>
                            </div>
                        </div>
                        <div class="mb-3">
                            <label class="form-label small">回测时间段</label>
                            <select class="form-select form-select-sm" id="backtestPeriod">
                                <option value="1">过去1天</option>
                                <option value="2">过去2天</option>
                                <option value="3">过去3天</option>
                                <option value="7" selected>过去1周</option>
                                <option value="30">过去1月</option>
                                <option value="60">过去2月</option>
                            </select>
                        </div>
                        <div class="mb-3">
                            <label class="form-label small">时间间隔</label>
                            <select class="form-select form-select-sm" id="backtestTimeframe">
                                <option value="1m">1分钟</option>
                                <option value="5m">5分钟</option>
                                <option value="15m">15分钟</option>
                                <option value="30m">30分钟</option>
                                <option value="1h" selected>1小时</option>
                                <option value="4h">4小时</option>
                            </select>
                            <small class="text-muted">MT5真实数据间隔</small>
                        </div>
                        <button class="btn btn-warning btn-sm w-100 mb-3" onclick="runBacktest()" id="backtestBtn">
                            <i class="fas fa-play me-2"></i>开始回测
                        </button>

                        <!-- 回测结果显示 -->
                        <div id="backtestResults" class="backtest-results" style="display: none;">
                            <div class="row g-2 mb-2">
                                <div class="col-6">
                                    <div class="text-center p-2 bg-info bg-opacity-10 rounded">
                                        <div class="small text-info">总交易次数</div>
                                        <div class="fw-bold" id="backtestTotalTrades">0</div>
                                    </div>
                                </div>
                                <div class="col-6">
                                    <div class="text-center p-2 bg-success bg-opacity-10 rounded">
                                        <div class="small text-success">盈利次数</div>
                                        <div class="fw-bold" id="backtestWinTrades">0</div>
                                    </div>
                                </div>
                            </div>
                            <div class="row g-2 mb-2">
                                <div class="col-6">
                                    <div class="text-center p-2 bg-danger bg-opacity-10 rounded">
                                        <div class="small text-danger">亏损次数</div>
                                        <div class="fw-bold" id="backtestLossTrades">0</div>
                                    </div>
                                </div>
                                <div class="col-6">
                                    <div class="text-center p-2 bg-warning bg-opacity-10 rounded">
                                        <div class="small text-warning">胜率</div>
                                        <div class="fw-bold" id="backtestWinRate">0%</div>
                                    </div>
                                </div>
                            </div>
                            <div class="text-center p-2 bg-primary bg-opacity-10 rounded mb-2">
                                <div class="small text-primary">总盈亏</div>
                                <div class="fw-bold" id="backtestTotalPnL">$0.00</div>
                            </div>

                            <!-- 买涨/买跌占比统计 -->
                            <div class="row g-2 mb-2">
                                <div class="col-6">
                                    <div class="text-center p-2 bg-success bg-opacity-10 rounded">
                                        <div class="small text-success">买涨盈利</div>
                                        <div class="fw-bold" id="backtestBuyWins">0 (0%)</div>
                                    </div>
                                </div>
                                <div class="col-6">
                                    <div class="text-center p-2 bg-danger bg-opacity-10 rounded">
                                        <div class="small text-danger">买涨亏损</div>
                                        <div class="fw-bold" id="backtestBuyLosses">0 (0%)</div>
                                    </div>
                                </div>
                            </div>
                            <div class="row g-2">
                                <div class="col-6">
                                    <div class="text-center p-2 bg-success bg-opacity-10 rounded">
                                        <div class="small text-success">买跌盈利</div>
                                        <div class="fw-bold" id="backtestSellWins">0 (0%)</div>
                                    </div>
                                </div>
                                <div class="col-6">
                                    <div class="text-center p-2 bg-danger bg-opacity-10 rounded">
                                        <div class="small text-danger">买跌亏损</div>
                                        <div class="fw-bold" id="backtestSellLosses">0 (0%)</div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- 交易控制 -->
                <div class="card mb-4">
                    <div class="card-header">
                        <h6 class="mb-0 text-primary">
                            <i class="fas fa-hand-paper me-2"></i>
                            交易控制
                        </h6>
                    </div>
                    <div class="card-body">
                        <div class="d-grid gap-2">
                            <!-- 自动交易控制 -->
                            <div class="row mb-3">
                                <div class="col-6">
                                    <button class="btn btn-primary w-100" onclick="startAutoTrading()" id="startAutoTradingBtn">
                                        <i class="fas fa-play me-2"></i>自动交易
                                    </button>
                                </div>
                                <div class="col-6">
                                    <button class="btn btn-secondary w-100" onclick="stopAutoTrading()" id="stopAutoTradingBtn" disabled>
                                        <i class="fas fa-stop me-2"></i>停止
                                    </button>
                                </div>
                            </div>

                            <!-- 自动交易状态显示 -->
                            <div class="alert alert-info mb-3" id="autoTradingStatus" style="display: none;">
                                <div class="d-flex justify-content-between align-items-center">
                                    <div>
                                        <i class="fas fa-robot me-2"></i>
                                        <strong>自动交易状态</strong>: <span id="autoTradingStatusText">已停止</span>
                                    </div>
                                    <div>
                                        <small>运行时间: <span id="autoTradingRunTime">00:00:00</span></small>
                                    </div>
                                </div>

                                <!-- 当前策略显示 -->
                                <div class="d-flex align-items-center mt-2 mb-2 p-2 bg-white bg-opacity-50 rounded">
                                    <div class="me-2">
                                        <i id="autoTradingStrategyIcon" class="fas fa-star text-success"></i>
                                    </div>
                                    <div class="flex-grow-1">
                                        <small class="text-muted d-block mb-0">使用策略</small>
                                        <strong class="small" id="autoTradingStrategyName">优化策略 (推荐)</strong>
                                    </div>
                                    <div class="text-end">
                                        <span class="badge bg-success" id="autoTradingStrategyBadge" style="font-size: 0.7em;">
                                            <i class="fas fa-check-circle"></i>
                                            已激活
                                        </span>
                                    </div>
                                </div>

                                <small class="d-block">
                                    执行交易: <span id="autoTradingCount">0</span> 笔 |
                                    当前信号强度: <span id="currentSignalStrength">--</span>
                                </small>
                            </div>



                            <!-- 测试和管理按钮 -->
                            <div class="row">
                                <div class="col-6">
                                    <button class="btn btn-info w-100" onclick="testOrder()" id="testOrderBtn">
                                        <i class="fas fa-vial me-2"></i>测试下单
                                    </button>
                                </div>
                                <div class="col-6">
                                    <button class="btn btn-warning w-100" onclick="closeAllLowRiskPositions()" id="lowRiskCloseBtn" disabled>
                                        <i class="fas fa-times me-2"></i>一键平仓
                                    </button>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- 当前持仓 -->
                <div class="card">
                    <div class="card-header" style="background: linear-gradient(135deg, #28a745 0%, #20c997 100%);">
                        <div class="d-flex justify-content-between align-items-center">
                            <h6 class="mb-0" style="color: white;">
                                <i class="fas fa-list me-2"></i>
                                当前持仓
                            </h6>
                            <small style="color: rgba(255,255,255,0.8);" id="positionRefreshStatus">
                                <i class="fas fa-sync-alt me-1"></i>
                                每10秒刷新
                            </small>
                        </div>
                    </div>
                    <div class="card-body">
                        <div id="lowRiskPositions" class="scrollable-content">
                            <div class="text-center py-4">
                                <i class="fas fa-inbox fa-2x text-muted mb-3"></i>
                                <p class="text-muted">暂无持仓</p>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- 低风险交易设置模态框 -->
    <div class="modal fade" id="lowRiskSettingsModal" tabindex="-1">
        <div class="modal-dialog modal-lg">
            <div class="modal-content" style="background-color: #f0f8f0;">
                <div class="modal-header border-secondary">
                    <h5 class="modal-title text-info">
                        <i class="fas fa-shield-alt me-2"></i>低风险交易设置
                    </h5>
                    <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal"></button>
                </div>
                <div class="modal-body">
                    <div class="row">
                        <div class="col-md-6">
                            <h6 class="text-info">基础设置</h6>

                            <!-- 策略预设选择器 -->
                            <div class="mb-3">
                                <label class="form-label">
                                    <i class="fas fa-magic me-1"></i>策略预设
                                </label>
                                <select class="form-select" id="strategyPreset" onchange="applyStrategyPreset(this.value); saveLowRiskTradingState();">
                                    <option value="custom">自定义配置</option>
                                    <option value="conservative">保守策略 (基准)</option>
                                    <option value="optimized" selected>优化策略 (推荐) ⭐</option>
                                    <option value="aggressive">激进策略</option>
                                    <option value="easyTrigger">易触发策略 (测试用) 🧪</option>
                                </select>
                                <small class="text-muted">选择预设策略可快速应用经过验证的配置</small>
                            </div>

                            <div class="mb-3">
                                <label class="form-label">自动交易时间间隔</label>
                                <select class="form-select" id="autoTradingTimeframe" onchange="saveLowRiskTradingState()">
                                    <option value="1m">1分钟 (测试用)</option>
                                    <option value="2m">2分钟 (测试用)</option>
                                    <option value="5m">5分钟 (测试用)</option>
                                    <option value="15m">15分钟</option>
                                    <option value="30m">30分钟</option>
                                    <option value="1h" selected>1小时 (推荐)</option>
                                    <option value="4h">4小时</option>
                                </select>
                                <small class="text-muted">自动交易系统检查信号的时间间隔</small>
                            </div>

                            <!-- 优化策略详细说明 -->
                            <div id="optimizedStrategyDetails" class="mb-3">
                                <div class="card border-success" style="font-size: 0.85em;">
                                    <div class="card-header bg-success bg-opacity-10 py-2">
                                        <h6 class="mb-0 text-success">
                                            <i class="fas fa-star me-1"></i>优化策略详细参数
                                        </h6>
                                    </div>
                                    <div class="card-body py-2">
                                        <div class="row g-2">
                                            <!-- 基础交易参数 -->
                                            <div class="col-12">
                                                <h6 class="text-info mb-2" style="font-size: 0.9em;">
                                                    <i class="fas fa-cog me-1"></i>基础交易参数
                                                </h6>
                                                <div class="row g-1">
                                                    <div class="col-6">
                                                        <small><strong>每日限制:</strong> 5笔交易</small><br>
                                                        <small><strong>交易手数:</strong> 0.01手</small><br>
                                                        <small><strong>止损:</strong> 1.2% (优化后)</small>
                                                    </div>
                                                    <div class="col-6">
                                                        <small><strong>止盈:</strong> 2.4% (优化后)</small><br>
                                                        <small><strong>最小信号:</strong> 2个确认</small><br>
                                                        <small><strong>风险收益比:</strong> 1:2</small>
                                                    </div>
                                                </div>
                                            </div>

                                            <!-- 技术指标参数 -->
                                            <div class="col-12 mt-2">
                                                <h6 class="text-info mb-2" style="font-size: 0.9em;">
                                                    <i class="fas fa-chart-line me-1"></i>技术指标参数
                                                </h6>
                                                <div class="row g-1">
                                                    <div class="col-6">
                                                        <small><strong>RSI买入:</strong> 27-78 (放宽)</small><br>
                                                        <small><strong>RSI卖出:</strong> 22-73 (放宽)</small><br>
                                                        <small><strong>MACD买入:</strong> >0.82倍信号线</small>
                                                    </div>
                                                    <div class="col-6">
                                                        <small><strong>MACD卖出:</strong> <1.18倍信号线</small><br>
                                                        <small><strong>布林带买入:</strong> >中轨*0.996</small><br>
                                                        <small><strong>布林带卖出:</strong> <中轨*1.004</small>
                                                    </div>
                                                </div>
                                            </div>

                                            <!-- 趋势检测参数 -->
                                            <div class="col-12 mt-2">
                                                <h6 class="text-info mb-2" style="font-size: 0.9em;">
                                                    <i class="fas fa-trending-up me-1"></i>趋势检测参数
                                                </h6>
                                                <div class="row g-1">
                                                    <div class="col-6">
                                                        <small><strong>趋势强度:</strong> 23% (一般)</small><br>
                                                        <small><strong>明确环境:</strong> 16% (降低)</small><br>
                                                        <small><strong>波动倍数:</strong> 1.5倍</small>
                                                    </div>
                                                    <div class="col-6">
                                                        <small><strong>确认时间:</strong> 30分钟</small><br>
                                                        <small><strong>多时间框架:</strong> 启用</small><br>
                                                        <small><strong>检测状态:</strong> 启用</small>
                                                    </div>
                                                </div>
                                            </div>

                                            <!-- 均值回归参数 -->
                                            <div class="col-12 mt-2">
                                                <h6 class="text-info mb-2" style="font-size: 0.9em;">
                                                    <i class="fas fa-exchange-alt me-1"></i>均值回归参数
                                                </h6>
                                                <div class="row g-1">
                                                    <div class="col-6">
                                                        <small><strong>超买卖出:</strong> RSI>66, 价格>上轨*0.998</small><br>
                                                        <small><strong>超卖买入:</strong> RSI<34, 价格<下轨*1.002</small>
                                                    </div>
                                                    <div class="col-6">
                                                        <small><strong>强制超买:</strong> RSI>76 触发</small><br>
                                                        <small><strong>强制超卖:</strong> RSI<24 触发</small>
                                                    </div>
                                                </div>
                                            </div>

                                            <!-- 信号强度参数 -->
                                            <div class="col-12 mt-2">
                                                <h6 class="text-info mb-2" style="font-size: 0.9em;">
                                                    <i class="fas fa-signal me-1"></i>信号强度参数
                                                </h6>
                                                <div class="row g-1">
                                                    <div class="col-6">
                                                        <small><strong>执行层强度:</strong> ≥0.18 (微调)</small><br>
                                                        <small><strong>策略层强度:</strong> ≥0.13 (微调)</small>
                                                    </div>
                                                    <div class="col-6">
                                                        <small><strong>强制信号强度:</strong> 0.45</small><br>
                                                        <small><strong>信号有效期:</strong> 5分钟</small>
                                                    </div>
                                                </div>
                                            </div>

                                            <!-- 优化效果 -->
                                            <div class="col-12 mt-2">
                                                <div class="alert alert-success py-2 mb-0" style="font-size: 0.8em;">
                                                    <div class="row g-1">
                                                        <div class="col-6">
                                                            <strong class="text-success">📊 回测验证结果:</strong><br>
                                                            <small>• 胜率: 57.1% (vs 基准37.5%)</small><br>
                                                            <small>• 月盈利: $119.96 (vs $26.44)</small>
                                                        </div>
                                                        <div class="col-6">
                                                            <strong class="text-success">🎯 优化特点:</strong><br>
                                                            <small>• 交易质量提升938%</small><br>
                                                            <small>• 三步渐进式优化</small>
                                                        </div>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <!-- 易触发策略详细说明 -->
                            <div id="easyTriggerStrategyDetails" class="mb-3" style="display: none;">
                                <div class="card border-warning" style="font-size: 0.85em;">
                                    <div class="card-header bg-warning bg-opacity-10 py-2">
                                        <h6 class="mb-0 text-warning">
                                            <i class="fas fa-flask me-1"></i>易触发策略详细参数 (测试专用)
                                        </h6>
                                    </div>
                                    <div class="card-body py-2">
                                        <div class="row g-2">
                                            <!-- 基础交易参数 -->
                                            <div class="col-12">
                                                <h6 class="text-info mb-2" style="font-size: 0.9em;">
                                                    <i class="fas fa-cog me-1"></i>基础交易参数
                                                </h6>
                                                <div class="row g-1">
                                                    <div class="col-6">
                                                        <small><strong>每日限制:</strong> 20笔交易 (高频)</small><br>
                                                        <small><strong>交易手数:</strong> 0.01手</small><br>
                                                        <small><strong>止损:</strong> 0.5% (快速触发)</small>
                                                    </div>
                                                    <div class="col-6">
                                                        <small><strong>止盈:</strong> 1.0% (快速触发)</small><br>
                                                        <small><strong>最小信号:</strong> 1个确认</small><br>
                                                        <small><strong>风险收益比:</strong> 1:2</small>
                                                    </div>
                                                </div>
                                            </div>

                                            <!-- 趋势检测参数 -->
                                            <div class="col-12 mt-2">
                                                <h6 class="text-info mb-2" style="font-size: 0.9em;">
                                                    <i class="fas fa-trending-up me-1"></i>趋势检测参数 (极低门槛)
                                                </h6>
                                                <div class="row g-1">
                                                    <div class="col-6">
                                                        <small><strong>趋势强度:</strong> 20% (极低)</small><br>
                                                        <small><strong>波动倍数:</strong> 1.1倍 (极低)</small><br>
                                                        <small><strong>确认时间:</strong> 5秒 (极短)</small>
                                                    </div>
                                                    <div class="col-6">
                                                        <small><strong>多时间框架:</strong> 禁用</small><br>
                                                        <small><strong>检测状态:</strong> 启用</small><br>
                                                        <small><strong>触发频率:</strong> 极高</small>
                                                    </div>
                                                </div>
                                            </div>

                                            <!-- 自动交易时间间隔 -->
                                            <div class="col-12 mt-2">
                                                <h6 class="text-info mb-2" style="font-size: 0.9em;">
                                                    <i class="fas fa-clock me-1"></i>自动交易时间间隔 (快速测试)
                                                </h6>
                                                <div class="row g-1">
                                                    <div class="col-6">
                                                        <small><strong>默认间隔:</strong> 2分钟 (自动设置)</small><br>
                                                        <small><strong>可选间隔:</strong> 1分钟、2分钟、5分钟</small>
                                                    </div>
                                                    <div class="col-6">
                                                        <small><strong>测试优势:</strong> 快速验证功能</small><br>
                                                        <small><strong>触发频率:</strong> 每2分钟检查一次</small>
                                                    </div>
                                                </div>
                                            </div>

                                            <!-- 测试特点 -->
                                            <div class="col-12 mt-2">
                                                <div class="alert alert-warning py-2 mb-0" style="font-size: 0.8em;">
                                                    <div class="row g-1">
                                                        <div class="col-6">
                                                            <strong class="text-warning">🧪 测试特点:</strong><br>
                                                            <small>• 极低触发条件</small><br>
                                                            <small>• 高频交易机会</small><br>
                                                            <small>• 快速止盈止损</small>
                                                        </div>
                                                        <div class="col-6">
                                                            <strong class="text-warning">⚠️ 注意事项:</strong><br>
                                                            <small>• 仅供测试自动交易功能</small><br>
                                                            <small>• 不建议实盘使用</small><br>
                                                            <small>• 风险较高，盈利不稳定</small>
                                                        </div>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <div class="mb-3">
                                <label class="form-label">每日交易限制</label>
                                <select class="form-select" id="lowRiskDailyLimit">
                                    <option value="1">1单/天</option>
                                    <option value="2">2单/天</option>
                                    <option value="3">3单/天</option>
                                    <option value="5" selected>5单/天</option>
                                    <option value="10">10单/天</option>
                                    <option value="20">20单/天</option>
                                    <option value="50">50单/天 (测试用)</option>
                                </select>
                            </div>

                            <div class="mb-3">
                                <label class="form-label">交易手数</label>
                                <input type="number" class="form-control" id="lowRiskLotSize" value="0.01" min="0.01" max="0.1" step="0.01">
                            </div>

                            <div class="mb-3">
                                <label class="form-label">止损百分比</label>
                                <div class="d-flex align-items-center gap-2">
                                    <input type="range" class="form-range" id="lowRiskStopLoss" min="0.3" max="1.2" step="0.1" value="1.2">
                                    <span id="lowRiskStopLossValue">1.2%</span>
                                </div>
                            </div>

                            <div class="mb-3">
                                <label class="form-label">止盈百分比</label>
                                <div class="d-flex align-items-center gap-2">
                                    <input type="range" class="form-range" id="lowRiskTakeProfit" min="1.0" max="3.0" step="0.1" value="2.4">
                                    <span id="lowRiskTakeProfitValue">2.4%</span>
                                </div>
                            </div>
                        </div>

                        <div class="col-md-6">
                            <h6 class="text-info">风险控制</h6>

                            <div class="mb-3">
                                <div class="form-check">
                                    <input class="form-check-input" type="checkbox" id="lowRiskAutoTrade">
                                    <label class="form-check-label" for="lowRiskAutoTrade">
                                        启用自动交易
                                    </label>
                                </div>
                            </div>

                            <div class="mb-3">
                                <div class="form-check">
                                    <input class="form-check-input" type="checkbox" id="lowRiskTrendDetection" checked>
                                    <label class="form-check-label" for="lowRiskTrendDetection">
                                        单边行情检测
                                    </label>
                                </div>
                            </div>

                            <!-- 单边行情检测详细配置 -->
                            <div id="trendDetectionDetails" class="mb-3" style="margin-left: 20px;">
                                <div class="card border-info" style="font-size: 0.9em;">
                                    <div class="card-header bg-light py-2">
                                        <small class="text-info">
                                            <i class="fas fa-cogs"></i>
                                            单边行情检测参数
                                        </small>
                                    </div>
                                    <div class="card-body py-2">
                                        <div class="row g-2">
                                            <div class="col-6">
                                                <label class="form-label" style="font-size: 0.8em;">
                                                    <i class="fas fa-tachometer-alt"></i>
                                                    趋势强度阈值
                                                </label>
                                                <div class="input-group input-group-sm">
                                                    <input type="number" class="form-control" id="trendStrengthThreshold"
                                                           value="60" min="30" max="90" step="5">
                                                    <span class="input-group-text">%</span>
                                                </div>
                                                <small class="text-muted">建议: 60%</small>
                                            </div>
                                            <div class="col-6">
                                                <label class="form-label" style="font-size: 0.8em;">
                                                    <i class="fas fa-expand-arrows-alt"></i>
                                                    波动突破倍数
                                                </label>
                                                <div class="input-group input-group-sm">
                                                    <input type="number" class="form-control" id="volatilityBreakoutMultiplier"
                                                           value="1.5" min="1.2" max="3.0" step="0.1">
                                                    <span class="input-group-text">倍</span>
                                                </div>
                                                <small class="text-muted">建议: 1.5倍</small>
                                            </div>
                                        </div>
                                        <div class="row g-2 mt-1">
                                            <div class="col-6">
                                                <label class="form-label" style="font-size: 0.8em;">
                                                    <i class="fas fa-clock"></i>
                                                    确认时间
                                                </label>
                                                <select class="form-select form-select-sm" id="trendConfirmationTime">
                                                    <option value="15">15分钟</option>
                                                    <option value="30" selected>30分钟</option>
                                                    <option value="45">45分钟</option>
                                                    <option value="60">60分钟</option>
                                                </select>
                                                <small class="text-muted">建议: 30分钟</small>
                                            </div>
                                            <div class="col-6">
                                                <label class="form-label" style="font-size: 0.8em;">
                                                    <i class="fas fa-layer-group"></i>
                                                    多时间框架
                                                </label>
                                                <div class="form-check form-switch">
                                                    <input class="form-check-input" type="checkbox" id="multiTimeframeConfirm" checked>
                                                    <label class="form-check-label" style="font-size: 0.8em;">
                                                        1H+15M确认
                                                    </label>
                                                </div>
                                                <small class="text-muted">推荐开启</small>
                                            </div>
                                        </div>
                                        <div class="mt-2">
                                            <div class="btn-group w-100" role="group">
                                                <button type="button" class="btn btn-outline-secondary btn-sm" onclick="setTrendDetectionPreset('conservative')">
                                                    保守
                                                </button>
                                                <button type="button" class="btn btn-outline-primary btn-sm" onclick="setTrendDetectionPreset('balanced')">
                                                    平衡
                                                </button>
                                                <button type="button" class="btn btn-outline-warning btn-sm" onclick="setTrendDetectionPreset('aggressive')">
                                                    激进
                                                </button>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <div class="mb-3">
                                <label class="form-label">最小确认信号数</label>
                                <select class="form-select" id="lowRiskMinSignals">
                                    <option value="1">1个信号</option>
                                    <option value="2" selected>2个信号</option>
                                    <option value="3">3个信号</option>
                                </select>
                            </div>

                            <div class="mb-3">
                                <label class="form-label">交易时间段</label>
                                <div class="row g-2">
                                    <div class="col-6">
                                        <input type="time" class="form-control" id="lowRiskStartTime" value="00:05">
                                    </div>
                                    <div class="col-6">
                                        <input type="time" class="form-control" id="lowRiskEndTime" value="23:55">
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <hr class="border-secondary">

                    <div class="row">
                        <div class="col-12">
                            <h6 class="text-info">分析参数</h6>

                            <div class="row g-3">
                                <div class="col-md-4">
                                    <label class="form-label">年度数据周期</label>
                                    <select class="form-select" id="lowRiskYearlyPeriod">
                                        <option value="365" selected>1年</option>
                                        <option value="180">6个月</option>
                                        <option value="90">3个月</option>
                                    </select>
                                </div>

                                <div class="col-md-4">
                                    <label class="form-label">周波段分析天数</label>
                                    <select class="form-select" id="lowRiskWeeklyDays">
                                        <option value="7" selected>7天</option>
                                        <option value="5">5天</option>
                                        <option value="3">3天</option>
                                    </select>
                                </div>

                                <div class="col-md-4">
                                    <label class="form-label">昨夜分析起始时间</label>
                                    <input type="time" class="form-control" id="lowRiskOvernightStart" value="21:00">
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="modal-footer border-secondary">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">取消</button>
                    <button type="button" class="btn btn-info" onclick="saveLowRiskSettings()">保存设置</button>
                </div>
            </div>
        </div>
    </div>

    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>

    <script>
        // 低风险交易配置
        let lowRiskTradingConfig = {
            enabled: false,
            dailyLimit: 5,             // 优化：24小时运行允许5次交易
            lotSize: 0.01,
            stopLossPercent: 1.2,      // 1小时框架：更大止损1.2% (上限已调整)
            takeProfitPercent: 2.4,    // 1小时框架：更大止盈2.4%
            autoTrade: false,          // 默认关闭自动交易
            trendDetection: true,
            minSignals: 2,             // 与界面选项一致：2个信号确认（默认选中）
            tradingHours: {
                start: '00:05',        // 优化：24小时运行，避开维护时间
                end: '23:55'
            },
            analysis: {
                yearlyPeriod: 365,
                weeklyDays: 7,
                overnightStart: '21:00'
            },
            // 新增：信号时效性配置
            signalExpiry: {
                maxAge: 300,        // 信号最大有效期（秒）
                warningAge: 180,    // 信号警告年龄（秒）
                autoRefresh: true   // 自动刷新过期信号
            },
            // 新增：行情持续性配置
            trendTracking: {
                minDuration: 600,       // 最小趋势持续时间（秒）
                maxDuration: 3600,      // 最大趋势持续时间（秒）
                decayFactor: 0.95,      // 趋势衰减因子
                reversalThreshold: 0.3  // 反转检测阈值
            }
        };

        console.log('🔧 初始化lowRiskTradingConfig，autoTrade:', lowRiskTradingConfig.autoTrade);

        // 低风险交易状态
        let lowRiskTradingState = {
            active: false,
            todayTradeCount: 0,
            todayPnL: 0,
            lastAnalysisTime: null,
            marketAnalysis: {
                yearlyTrend: null,
                weeklyRange: null,
                overnightExtreme: null,
                trendDirection: null
            },
            opportunities: [],
            positions: [],
            // 新增：信号管理
            signals: {
                active: [],         // 活跃信号列表
                history: [],        // 历史信号记录
                lastUpdate: null    // 最后更新时间
            },
            // 新增：趋势状态跟踪
            trendState: {
                current: null,      // 当前趋势
                startTime: null,    // 趋势开始时间
                strength: 0,        // 趋势强度
                duration: 0,        // 持续时间
                isDecaying: false,  // 是否衰减中
                reversalSignals: 0  // 反转信号计数
            }
        };

        // 低风险交易定时器
        let lowRiskAnalysisInterval = null;
        let positionRefreshInterval = null;

        // 自动交易状态管理
        let autoTradingState = {
            isRunning: false,
            startTime: null,
            tradeCount: 0,
            intervalId: null,
            runTimeIntervalId: null,
            currentSignalStrength: 0,
            lastSignalTime: null
        };

        // 持仓刷新状态管理
        let positionRefreshState = {
            isActive: false,
            lastRefreshTime: null,
            refreshCount: 0
        };

        // 单边行情检测配置管理
        let trendDetectionConfig = {
            enabled: true,
            trendStrengthThreshold: 60,
            volatilityBreakoutMultiplier: 1.5,
            trendConfirmationTime: 30,
            multiTimeframeConfirm: true
        };

        // 策略预设配置
        const strategyPresets = {
            conservative: {
                name: "保守策略",
                description: "基准配置，交易频率高但质量一般 (37.5%胜率)",
                config: {
                    dailyLimit: 2,
                    lotSize: 0.01,
                    stopLossPercent: 1.0,
                    takeProfitPercent: 2.0,
                    minSignals: 25,
                    autoTrade: false
                },
                trendConfig: {
                    enabled: false,
                    trendStrengthThreshold: 60,
                    volatilityBreakoutMultiplier: 1.5,
                    trendConfirmationTime: 30,
                    multiTimeframeConfirm: true
                }
            },

            optimized: {
                name: "优化策略 (推荐)",
                description: "经过三步优化的高质量策略 (57.1%胜率，月盈利$119.96)",
                config: {
                    dailyLimit: 5,
                    lotSize: 0.01,
                    stopLossPercent: 1.2,
                    takeProfitPercent: 2.4,
                    minSignals: 2,
                    autoTrade: false
                },
                trendConfig: {
                    enabled: true,
                    trendStrengthThreshold: 60,
                    volatilityBreakoutMultiplier: 1.5,
                    trendConfirmationTime: 30,
                    multiTimeframeConfirm: true
                }
            },

            aggressive: {
                name: "激进策略",
                description: "更宽松的条件，交易频率更高但风险较大",
                config: {
                    dailyLimit: 10,
                    lotSize: 0.01,
                    stopLossPercent: 0.8,
                    takeProfitPercent: 1.6,
                    minSignals: 1,
                    autoTrade: false
                },
                trendConfig: {
                    enabled: true,
                    trendStrengthThreshold: 40,
                    volatilityBreakoutMultiplier: 1.2,
                    trendConfirmationTime: 15,
                    multiTimeframeConfirm: false
                }
            },

            easyTrigger: {
                name: "易触发策略 (测试用)",
                description: "极低触发条件，用于测试自动交易功能 (高频率，仅供测试)",
                config: {
                    dailyLimit: 20,           // 增加每日交易限制
                    lotSize: 0.01,
                    stopLossPercent: 0.5,     // 较小止损，快速触发
                    takeProfitPercent: 1.0,   // 较小止盈，快速触发
                    minSignals: 1,            // 最少信号确认
                    autoTrade: false
                },
                trendConfig: {
                    enabled: true,
                    trendStrengthThreshold: 20,    // 极低趋势强度要求
                    volatilityBreakoutMultiplier: 1.1,  // 极低波动要求
                    trendConfirmationTime: 5,      // 极短确认时间
                    multiTimeframeConfirm: false   // 关闭多时间框架确认
                }
            }
        };

        // 获取时间框架对应的毫秒间隔
        function getTimeframeInterval(timeframe) {
            const intervals = {
                '1m': 1 * 60 * 1000,      // 1分钟 (测试用)
                '2m': 2 * 60 * 1000,      // 2分钟 (测试用)
                '5m': 5 * 60 * 1000,      // 5分钟 (测试用)
                '15m': 15 * 60 * 1000,    // 15分钟
                '30m': 30 * 60 * 1000,    // 30分钟
                '1h': 60 * 60 * 1000,     // 1小时
                '4h': 4 * 60 * 60 * 1000  // 4小时
            };
            return intervals[timeframe] || intervals['1h']; // 默认1小时
        }

        // 保存自动交易状态到localStorage
        function saveAutoTradingState() {
            const stateToSave = {
                isRunning: autoTradingState.isRunning,
                startTime: autoTradingState.startTime,
                tradeCount: autoTradingState.tradeCount,
                currentSignalStrength: autoTradingState.currentSignalStrength,
                lastSignalTime: autoTradingState.lastSignalTime
            };
            localStorage.setItem('autoTradingState', JSON.stringify(stateToSave));
        }

        // 从localStorage恢复自动交易状态
        function restoreAutoTradingState() {
            try {
                const savedState = localStorage.getItem('autoTradingState');
                if (savedState) {
                    const state = JSON.parse(savedState);

                    if (state.isRunning) {
                        // 恢复运行状态
                        autoTradingState.isRunning = state.isRunning;
                        autoTradingState.startTime = state.startTime ? new Date(state.startTime) : new Date();
                        autoTradingState.tradeCount = state.tradeCount || 0;
                        autoTradingState.currentSignalStrength = state.currentSignalStrength || 0;
                        autoTradingState.lastSignalTime = state.lastSignalTime ? new Date(state.lastSignalTime) : null;

                        // 更新UI状态
                        document.getElementById('startAutoTradingBtn').disabled = true;
                        document.getElementById('stopAutoTradingBtn').disabled = false;
                        document.getElementById('autoTradingStatus').style.display = 'block';
                        document.getElementById('autoTradingStatusText').textContent = '运行中';
                        document.getElementById('autoTradingCount').textContent = autoTradingState.tradeCount;
                        document.getElementById('currentSignalStrength').textContent = autoTradingState.currentSignalStrength.toFixed(3);

                        // 重新启动定时器
                        const timeframe = document.getElementById('autoTradingTimeframe').value;
                        const intervalMs = getTimeframeInterval(timeframe);
                        autoTradingState.intervalId = setInterval(executeAutoTradingCycle, intervalMs);
                        autoTradingState.runTimeIntervalId = setInterval(updateAutoTradingRunTime, 1000);

                        console.log('✅ 自动交易状态已恢复，继续运行');
                        showNotification('自动交易状态已恢复，继续运行', 'info');
                    }
                }
            } catch (error) {
                console.error('❌ 恢复自动交易状态失败:', error);
            }
        }

        // 当前交易品种
        let currentSymbol = 'XAUUSD';

        // 历史记录查询功能
        async function queryHistoryRecords() {
            try {
                const container = document.getElementById('historyRecordsContainer');
                const statsContainer = document.getElementById('historyStats');

                // 显示加载状态
                container.innerHTML = `
                    <div class="text-center py-4">
                        <div class="spinner-border spinner-border-sm text-primary me-2"></div>
                        <span>正在查询历史记录...</span>
                    </div>
                `;

                // 获取查询参数
                const timeRange = document.querySelector('input[name="historyTimeRange"]:checked').value;
                const startDate = document.getElementById('historyStartDate').value;
                const endDate = document.getElementById('historyEndDate').value;

                // 构建查询参数
                let queryParams = {};
                const days = parseInt(timeRange);

                if (days === 0) {
                    // 今天：使用今天的开始和结束时间
                    const today = new Date();
                    const todayStart = new Date(today);
                    todayStart.setHours(0, 0, 0, 0);

                    queryParams = {
                        start_date: todayStart.toISOString().split('T')[0],
                        end_date: today.toISOString().split('T')[0]
                    };
                } else if (startDate && endDate) {
                    // 如果有自定义日期，使用自定义日期
                    queryParams = {
                        start_date: startDate,
                        end_date: endDate
                    };
                } else {
                    // 使用天数范围
                    queryParams = {
                        days: days
                    };
                }

                console.log('📡 查询历史记录参数:', queryParams);

                const response = await fetch('/api/low-risk-trading/history', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify(queryParams)
                });

                const result = await response.json();

                if (result.success) {
                    displayHistoryRecords(result.data);
                    updateHistoryStats(result.data);
                } else {
                    // 检查是否是数据库表不存在的错误
                    if (result.error && result.error.includes('数据库表不存在')) {
                        showDatabaseTableMissingError();
                    } else {
                        container.innerHTML = `
                            <div class="text-center py-4">
                                <i class="fas fa-exclamation-triangle fa-2x text-warning mb-3"></i>
                                <p class="text-muted">查询失败: ${result.error}</p>
                            </div>
                        `;
                    }
                }

            } catch (error) {
                console.error('❌ 查询历史记录失败:', error);
                document.getElementById('historyRecordsContainer').innerHTML = `
                    <div class="text-center py-4">
                        <i class="fas fa-exclamation-triangle fa-2x text-danger mb-3"></i>
                        <p class="text-muted">查询失败，请稍后重试</p>
                    </div>
                `;
            }
        }

        // 显示数据库表缺失错误
        function showDatabaseTableMissingError() {
            const container = document.getElementById('historyRecordsContainer');
            container.innerHTML = `
                <div class="text-center py-5">
                    <i class="fas fa-database fa-3x text-warning mb-3"></i>
                    <h5 class="text-warning">数据库表不存在</h5>
                    <p class="text-muted mb-4">
                        系统检测到低风险交易历史记录表尚未创建。<br>
                        请联系管理员运行数据库初始化脚本。
                    </p>
                    <div class="alert alert-info">
                        <h6><i class="fas fa-terminal me-2"></i>管理员操作步骤：</h6>
                        <ol class="mb-0 text-start">
                            <li>打开命令行终端</li>
                            <li>进入项目目录</li>
                            <li>运行命令：<code>python create_low_risk_tables.py</code></li>
                            <li>等待脚本执行完成</li>
                            <li>刷新页面重新查询</li>
                        </ol>
                    </div>
                    <div class="mt-3">
                        <button class="btn btn-outline-primary" onclick="queryHistoryRecords()">
                            <i class="fas fa-redo me-2"></i>重新查询
                        </button>
                    </div>
                    <div class="mt-3">
                        <small class="text-muted">
                            <i class="fas fa-info-circle me-1"></i>
                            此操作需要服务器管理员权限
                        </small>
                    </div>
                </div>
            `;
        }

        // 同步交易状态
        async function syncTradingStatus() {
            try {
                showInfo('正在同步交易状态，请稍候...');

                const response = await fetch('/api/low-risk-trading/sync-status', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    }
                });

                const result = await response.json();

                if (result.success) {
                    showSuccess(`同步完成！更新了 ${result.updated_count} 条交易记录`);

                    // 同步完成后自动刷新历史记录
                    setTimeout(() => {
                        queryHistoryRecords();
                    }, 1000);
                } else {
                    showError('同步失败: ' + result.error);
                }

            } catch (error) {
                console.error('❌ 同步交易状态失败:', error);
                showError('同步失败，请检查网络连接');
            }
        }

        // 显示历史记录
        function displayHistoryRecords(records) {
            const container = document.getElementById('historyRecordsContainer');

            if (!records || records.length === 0) {
                container.innerHTML = `
                    <div class="text-center py-4">
                        <i class="fas fa-inbox fa-2x text-muted mb-3"></i>
                        <p class="text-muted">暂无历史记录</p>
                    </div>
                `;
                return;
            }

            let html = '<div class="table-responsive">';
            html += '<table class="table table-sm table-hover">';
            html += `
                <thead class="table-light">
                    <tr>
                        <th style="font-size: 0.8em;" title="北京时间 (UTC+8)">时间</th>
                        <th style="font-size: 0.8em;">类型</th>
                        <th style="font-size: 0.8em;">操作类型</th>
                        <th style="font-size: 0.8em;">信号强度</th>
                        <th style="font-size: 0.8em;">入场价</th>
                        <th style="font-size: 0.8em;">出场价</th>
                        <th style="font-size: 0.8em;">盈亏</th>
                        <th style="font-size: 0.8em;">状态</th>
                        <th style="font-size: 0.8em;">原因</th>
                    </tr>
                </thead>
                <tbody>
            `;

            records.forEach(record => {
                const pnlClass = record.pnl > 0 ? 'text-success' : record.pnl < 0 ? 'text-danger' : 'text-muted';
                const statusClass = record.status === 'closed' ? 'success' : record.status === 'open' ? 'primary' : 'secondary';
                const operationTypeClass = record.operation_type === 'auto' ? 'info' : 'warning';
                const operationTypeText = record.operation_type === 'auto' ? '自动' : '手动';

                html += `
                    <tr>
                        <td style="font-size: 0.75em;">${formatDateTime(record.timestamp)}</td>
                        <td>
                            <span class="badge bg-${record.type === 'buy' ? 'success' : 'danger'} badge-sm">
                                ${record.type === 'buy' ? '买入' : '卖出'}
                            </span>
                        </td>
                        <td>
                            <span class="badge bg-${operationTypeClass} badge-sm">
                                ${operationTypeText}
                            </span>
                        </td>
                        <td style="font-size: 0.8em;">${record.signal_strength || 'N/A'}</td>
                        <td style="font-size: 0.8em;">${record.entry_price ? record.entry_price.toFixed(2) : 'N/A'}</td>
                        <td style="font-size: 0.8em;">${record.exit_price ? record.exit_price.toFixed(2) : '-'}</td>
                        <td class="${pnlClass}" style="font-size: 0.8em;">
                            ${record.pnl || '-'}
                        </td>
                        <td>
                            <span class="badge bg-${statusClass} badge-sm">
                                ${record.status === 'closed' ? '已平仓' :
                                  record.status === 'open' ? '持仓中' :
                                  record.status === 'pending' ? '待成交' :
                                  record.status === 'cancelled' ? '已取消' :
                                  record.status || '未知'}
                            </span>
                        </td>
                        <td style="font-size: 0.75em;" title="${record.reason || ''}">
                            ${(() => {
                                const reason = record.reason || 'N/A';
                                const reasonMap = {
                                    'TREND_BUY': '趋势买入',
                                    'TREND_SELL': '趋势卖出',
                                    'MEAN_REVERSION_BUY': '均值回归买入',
                                    'MEAN_REVERSION_SELL': '均值回归卖出',
                                    'MANUAL_TRADE': '手动交易',
                                    'BREAKOUT_BUY': '突破买入',
                                    'BREAKOUT_SELL': '突破卖出'
                                };
                                const displayReason = reasonMap[reason] || reason;
                                return displayReason.length > 8 ? displayReason.substring(0, 8) + '...' : displayReason;
                            })()}
                        </td>
                    </tr>
                `;
            });

            html += '</tbody></table></div>';
            container.innerHTML = html;
        }

        // 更新历史统计信息
        function updateHistoryStats(records) {
            const statsContainer = document.getElementById('historyStats');

            if (!records || records.length === 0) {
                statsContainer.style.display = 'none';
                return;
            }

            // 解析pnl字符串为数字进行统计计算
            const parsePnL = (pnlStr) => {
                if (!pnlStr || pnlStr === '-' || pnlStr === 'N/A') return 0;
                // 移除$符号和+号，保留-号
                const numStr = pnlStr.replace(/[\$\+]/g, '');
                return parseFloat(numStr) || 0;
            };

            const totalTrades = records.length;
            const profitTrades = records.filter(r => parsePnL(r.pnl) > 0).length;
            const winRate = totalTrades > 0 ? (profitTrades / totalTrades * 100).toFixed(1) : 0;
            const totalPnL = records.reduce((sum, r) => sum + parsePnL(r.pnl), 0);

            // 按操作类型统计
            const autoTrades = records.filter(r => r.operation_type === 'auto');
            const manualTrades = records.filter(r => r.operation_type === 'manual');
            const autoProfitTrades = autoTrades.filter(r => parsePnL(r.pnl) > 0).length;
            const autoWinRate = autoTrades.length > 0 ? (autoProfitTrades / autoTrades.length * 100).toFixed(1) : 0;

            // 更新基础统计
            document.getElementById('historyTotalTrades').textContent = totalTrades;
            document.getElementById('historyProfitTrades').textContent = profitTrades;
            document.getElementById('historyWinRate').textContent = winRate + '%';
            document.getElementById('historyTotalPnL').textContent = (totalPnL >= 0 ? '+' : '') + '$' + totalPnL.toFixed(2);
            document.getElementById('historyTotalPnL').className = 'fw-bold ' + (totalPnL > 0 ? 'text-success' : totalPnL < 0 ? 'text-danger' : 'text-muted');

            // 更新操作类型统计
            document.getElementById('historyAutoTrades').textContent = autoTrades.length;
            document.getElementById('historyManualTrades').textContent = manualTrades.length;
            document.getElementById('historyAutoWinRate').textContent = autoWinRate + '%';

            statsContainer.style.display = 'block';
        }

        // 导出历史记录到Excel
        async function exportHistoryRecords() {
            try {
                // 获取查询参数
                const timeRange = document.querySelector('input[name="historyTimeRange"]:checked').value;
                const startDate = document.getElementById('historyStartDate').value;
                const endDate = document.getElementById('historyEndDate').value;

                let queryParams = {
                    export: true
                };

                const days = parseInt(timeRange);

                if (days === 0) {
                    // 今天：使用今天的开始和结束时间
                    const today = new Date();
                    const todayStart = new Date(today);
                    todayStart.setHours(0, 0, 0, 0);

                    queryParams.start_date = todayStart.toISOString().split('T')[0];
                    queryParams.end_date = today.toISOString().split('T')[0];
                } else if (startDate && endDate) {
                    queryParams.start_date = startDate;
                    queryParams.end_date = endDate;
                } else {
                    queryParams.days = days;
                }

                const response = await fetch('/api/low-risk-trading/history/export', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify(queryParams)
                });

                if (response.ok) {
                    const blob = await response.blob();
                    const url = window.URL.createObjectURL(blob);
                    const a = document.createElement('a');
                    a.href = url;
                    a.download = `低风险交易历史记录_${new Date().toISOString().split('T')[0]}.xlsx`;
                    document.body.appendChild(a);
                    a.click();
                    window.URL.revokeObjectURL(url);
                    document.body.removeChild(a);

                    showSuccess('历史记录已导出到Excel文件');
                } else {
                    showError('导出失败，请稍后重试');
                }

            } catch (error) {
                console.error('❌ 导出历史记录失败:', error);
                showError('导出失败，请稍后重试');
            }
        }

        // 格式化日期时间 - 确保显示北京时间
        function formatDateTime(timestamp) {
            if (!timestamp) return 'N/A';
            const date = new Date(timestamp);

            // 明确指定北京时间 (UTC+8)
            return date.toLocaleString('zh-CN', {
                timeZone: 'Asia/Shanghai',  // 北京时间
                year: 'numeric',
                month: '2-digit',
                day: '2-digit',
                hour: '2-digit',
                minute: '2-digit',
                hour12: false  // 使用24小时制
            });
        }

        // 初始化历史记录查询
        function initHistoryQuery() {
            // 设置默认日期
            const today = new Date();
            const oneWeekAgo = new Date(today.getTime() - 7 * 24 * 60 * 60 * 1000);

            document.getElementById('historyStartDate').value = oneWeekAgo.toISOString().split('T')[0];
            document.getElementById('historyEndDate').value = today.toISOString().split('T')[0];

            // 监听时间范围选择变化
            document.querySelectorAll('input[name="historyTimeRange"]').forEach(radio => {
                radio.addEventListener('change', function() {
                    if (this.checked) {
                        const days = parseInt(this.value);
                        const endDate = new Date();
                        let startDate;

                        if (days === 0) {
                            // 今天：从今天00:00:00到现在
                            startDate = new Date();
                            startDate.setHours(0, 0, 0, 0);
                        } else {
                            // 其他时间范围：从N天前到现在
                            startDate = new Date(endDate.getTime() - days * 24 * 60 * 60 * 1000);
                        }

                        document.getElementById('historyStartDate').value = startDate.toISOString().split('T')[0];
                        document.getElementById('historyEndDate').value = endDate.toISOString().split('T')[0];
                    }
                });
            });

            // 默认查询1周的记录
            setTimeout(() => {
                queryHistoryRecords();
            }, 1000);
        }

        // 检查MT5连接状态
        async function checkMT5ConnectionStatus() {
            try {
                const response = await fetch('/api/mt5/connection-status');
                const data = await response.json();

                const statusElement = document.getElementById('mt5ConnectionStatus');

                if (data.connected) {
                    statusElement.innerHTML = '<i class="fas fa-check me-1"></i>MT5已连接';
                    statusElement.className = 'badge bg-success me-2';
                    console.log('✅ MT5连接正常:', data.account_info);
                } else {
                    statusElement.innerHTML = '<i class="fas fa-times me-1"></i>MT5未连接';
                    statusElement.className = 'badge bg-danger me-2';
                    console.log('❌ MT5连接失败:', data.error);
                }

                return data.connected;
            } catch (error) {
                console.error('❌ MT5连接状态检查失败:', error);
                const statusElement = document.getElementById('mt5ConnectionStatus');
                statusElement.innerHTML = '<i class="fas fa-exclamation-triangle me-1"></i>检查失败';
                statusElement.className = 'badge bg-warning me-2';
                return false;
            }
        }

        // 页面加载完成后初始化
        document.addEventListener('DOMContentLoaded', function() {
            console.log('🛡️ 低风险交易系统页面加载完成');

            // 恢复自动交易状态
            restoreAutoTradingState();

            // 恢复低风险交易配置状态
            console.log('🔧 开始恢复低风险交易配置状态...');
            restoreLowRiskTradingState();
            console.log('🔧 当前autoTrade状态:', lowRiskTradingConfig.autoTrade);

            // 检查MT5连接状态
            checkMT5ConnectionStatus();

            // 定期检查MT5连接状态（每30秒）
            setInterval(checkMT5ConnectionStatus, 30000);

            // 加载单边行情检测配置
            loadTrendDetectionConfig();

            // 应用默认策略（仅在没有保存状态时）
            setTimeout(() => {
                const savedState = localStorage.getItem('lowRiskTradingState');
                if (!savedState) {
                    // 没有保存状态，应用默认的优化策略（静默）
                    console.log('🔧 首次访问，应用默认优化策略');
                    applyStrategyPreset('optimized', false);

                    // 确保优化策略详细说明显示
                    const detailsElement = document.getElementById('optimizedStrategyDetails');
                    if (detailsElement) {
                        detailsElement.style.display = 'block';
                    }
                } else {
                    console.log('🔧 发现保存状态，跳过默认策略应用');
                }
            }, 500);

            // 初始化历史记录查询
            initHistoryQuery();

            // 初始化回测策略显示
            setTimeout(() => {
                updateBacktestStrategyDisplay();
            }, 600);

            // 添加单边行情检测事件监听器
            const trendDetectionCheckbox = document.getElementById('lowRiskTrendDetection');
            if (trendDetectionCheckbox) {
                trendDetectionCheckbox.addEventListener('change', toggleTrendDetectionDetails);
            }

            // 添加配置参数变化监听器
            const configInputs = [
                'trendStrengthThreshold',
                'volatilityBreakoutMultiplier',
                'trendConfirmationTime',
                'multiTimeframeConfirm'
            ];

            configInputs.forEach(id => {
                const element = document.getElementById(id);
                if (element) {
                    element.addEventListener('change', saveTrendDetectionConfig);
                }
            });

            initializeLowRiskTrading();

            // 加载入场信号
            loadEntrySignals();
        });

        // 初始化低风险交易系统
        async function initializeLowRiskTrading() {
            console.log('🛡️ 初始化低风险交易系统...');

            // 恢复保存的状态
            restoreLowRiskTradingState();

            // 更新UI
            updateTodayStats();
            updateSystemStatus();

            // 获取并显示当前价格
            try {
                const currentPrice = await getCurrentRealPrice();
                console.log(`📊 当前XAUUSD价格: ${currentPrice.toFixed(4)}`);

                // 可以在这里更新页面上的价格显示
                updateCurrentPriceDisplay(currentPrice);
            } catch (error) {
                console.error('❌ 初始化时获取价格失败:', error);
            }

            console.log('✅ 低风险交易系统初始化完成');
        }

        // 更新当前价格显示
        function updateCurrentPriceDisplay(price) {
            // 如果页面上有价格显示元素，在这里更新
            const priceElements = document.querySelectorAll('.current-price');
            priceElements.forEach(element => {
                element.textContent = price.toFixed(4);
            });

            console.log(`📊 页面价格显示已更新: ${price.toFixed(4)}`);
        }

        // 启用/禁用低风险交易
        function toggleLowRiskTrading() {
            const switchElement = document.getElementById('lowRiskTradingSwitch');

            if (switchElement.checked) {
                startLowRiskTrading();
            } else {
                stopLowRiskTrading();
            }
        }

        // 启动低风险交易系统
        async function startLowRiskTrading() {
            try {
                console.log('🛡️ 启动低风险交易系统...');

                lowRiskTradingConfig.enabled = true;
                lowRiskTradingState.active = true;

                // 更新UI状态
                updateSystemStatus();

                // 启动市场分析
                await performLowRiskMarketAnalysis();

                // 启动定时分析
                lowRiskAnalysisInterval = setInterval(async () => {
                    await performLowRiskMarketAnalysis();
                    await checkLowRiskTradingOpportunities();
                }, 60000); // 每分钟分析一次

                // 启动持仓数据定时刷新 (每10秒)
                positionRefreshState.isActive = true;
                positionRefreshInterval = setInterval(async () => {
                    await loadLowRiskPositions();
                }, 10000); // 每10秒刷新一次持仓数据

                // 加载今日交易统计
                await loadTodayLowRiskStats();

                // 加载当前持仓
                await loadLowRiskPositions();

                // 保存状态
                saveLowRiskTradingState();

                showSuccess('低风险交易系统已启动');
                console.log('✅ 低风险交易系统启动成功');

            } catch (error) {
                console.error('❌ 启动低风险交易系统失败:', error);
                showError('启动低风险交易系统失败: ' + error.message);

                // 回滚状态
                const switchElement = document.getElementById('lowRiskTradingSwitch');
                switchElement.checked = false;
                updateSystemStatus();
            }
        }

        // 停止低风险交易系统
        function stopLowRiskTrading() {
            console.log('🛑 停止低风险交易系统...');

            lowRiskTradingConfig.enabled = false;
            lowRiskTradingState.active = false;

            // 停止定时分析
            if (lowRiskAnalysisInterval) {
                clearInterval(lowRiskAnalysisInterval);
                lowRiskAnalysisInterval = null;
            }

            // 停止持仓数据刷新
            if (positionRefreshInterval) {
                clearInterval(positionRefreshInterval);
                positionRefreshInterval = null;
                positionRefreshState.isActive = false;
            }

            // 更新UI状态
            updateSystemStatus();

            // 保存状态
            saveLowRiskTradingState();

            showInfo('低风险交易系统已停止');
            console.log('✅ 低风险交易系统已停止');
        }

        // 更新系统状态显示
        function updateSystemStatus() {
            const statusElement = document.getElementById('lowRiskSystemStatus');
            const closeBtn = document.getElementById('lowRiskCloseBtn');

            if (lowRiskTradingState.active) {
                statusElement.className = 'system-status status-active';
                statusElement.innerHTML = '<i class="fas fa-play-circle me-2"></i>低风险交易系统运行中 - 正在分析市场机会';

                // 启用关闭按钮
                if (closeBtn) closeBtn.disabled = false;
            } else {
                statusElement.className = 'system-status status-inactive';
                statusElement.innerHTML = '<i class="fas fa-power-off me-2"></i>系统已停用 - 点击开关启用低风险交易系统';

                // 禁用关闭按钮
                if (closeBtn) closeBtn.disabled = true;
            }
        }

        // ==================== 趋势持续性和反转检测 ====================

        // 更新趋势状态
        function updateTrendState(newTrend) {
            const now = new Date();
            const currentState = lowRiskTradingState.trendState;

            // 如果趋势发生变化
            if (currentState.current !== newTrend.direction) {
                console.log(`📈 趋势变化: ${currentState.current || '无'} → ${newTrend.direction}`);

                // 记录前一个趋势的持续时间
                if (currentState.startTime) {
                    const previousDuration = (now - currentState.startTime) / 1000;
                    console.log(`📊 前趋势持续时间: ${Math.floor(previousDuration/60)}分钟`);
                }

                // 重置趋势状态
                currentState.current = newTrend.direction;
                currentState.startTime = now;
                currentState.strength = newTrend.strength || 0;
                currentState.duration = 0;
                currentState.isDecaying = false;
                currentState.reversalSignals = 0;
            } else {
                // 更新现有趋势
                currentState.duration = (now - currentState.startTime) / 1000;
                currentState.strength = newTrend.strength || currentState.strength;

                // 检查趋势衰减
                checkTrendDecay();

                // 检查反转信号
                checkTrendReversal(newTrend);
            }

            console.log(`📊 趋势状态: ${currentState.current}, 强度: ${currentState.strength}, 持续: ${Math.floor(currentState.duration/60)}分钟`);
        }

        // 检查趋势衰减
        function checkTrendDecay() {
            const state = lowRiskTradingState.trendState;
            const config = lowRiskTradingConfig.trendTracking;

            // 如果趋势持续时间超过最大持续时间
            if (state.duration > config.maxDuration) {
                state.isDecaying = true;

                // 应用衰减因子
                state.strength *= config.decayFactor;

                console.log(`📉 趋势衰减: 强度降至 ${state.strength.toFixed(2)}`);

                // 如果强度降得太低，标记为即将反转
                if (state.strength < 0.3) {
                    console.warn('⚠️ 趋势强度过低，可能即将反转');
                }
            }
        }

        // 检查趋势反转信号
        function checkTrendReversal(currentData) {
            const state = lowRiskTradingState.trendState;
            const config = lowRiskTradingConfig.trendTracking;

            // 反转信号检测逻辑
            let reversalSignals = 0;

            // 1. 强度下降检测
            if (currentData.strength < state.strength * 0.8) {
                reversalSignals++;
                console.log('🔍 检测到强度下降信号');
            }

            // 2. 持续时间过长
            if (state.duration > config.maxDuration) {
                reversalSignals++;
                console.log('🔍 检测到持续时间过长信号');
            }

            // 3. 价格背离（如果有价格数据）
            if (currentData.priceAction && currentData.priceAction.divergence) {
                reversalSignals++;
                console.log('🔍 检测到价格背离信号');
            }

            // 更新反转信号计数
            state.reversalSignals = reversalSignals;

            // 如果反转信号达到阈值
            if (reversalSignals >= config.reversalThreshold * 10) { // 转换为整数比较
                console.warn(`⚠️ 检测到 ${reversalSignals} 个反转信号，趋势可能即将反转`);

                // 可以在这里触发反转警告或自动平仓
                if (lowRiskTradingConfig.autoTrade) {
                    showWarning('检测到趋势反转信号，建议谨慎交易');
                }

                return true; // 返回true表示检测到反转
            }

            return false;
        }

        // 获取趋势有效性评分
        function getTrendValidityScore() {
            const state = lowRiskTradingState.trendState;
            const config = lowRiskTradingConfig.trendTracking;

            if (!state.current || !state.startTime) {
                return 0; // 无趋势
            }

            let score = 1.0;

            // 基于持续时间的评分
            if (state.duration < config.minDuration) {
                score *= 0.5; // 趋势太新，可靠性降低
            } else if (state.duration > config.maxDuration) {
                score *= 0.7; // 趋势太老，可能即将反转
            }

            // 基于强度的评分
            score *= state.strength;

            // 基于衰减状态的评分
            if (state.isDecaying) {
                score *= 0.6;
            }

            // 基于反转信号的评分
            score *= Math.max(0.1, 1 - state.reversalSignals * 0.2);

            return Math.max(0, Math.min(1, score));
        }

        // 执行低风险市场分析
        async function performLowRiskMarketAnalysis() {
            try {
                console.log('📊 执行低风险市场分析...');

                // 调用实时市场分析API
                const response = await fetch('/api/low-risk-trading/market-analysis');
                const data = await response.json();

                if (data.success) {
                    const analysis = data.analysis;
                    console.log('✅ 获取实时市场分析数据:', analysis);

                    lowRiskTradingState.marketAnalysis = analysis;
                    updateMarketAnalysisDisplay();

                    // 更新趋势状态
                    updateTrendState(analysis.trendDirection);

                    // 记录分析时间戳
                    lowRiskTradingState.lastAnalysisTime = new Date();

                    // 为所有新生成的信号添加时间戳
                    if (lowRiskTradingState.opportunities) {
                        const now = new Date().toISOString();
                        lowRiskTradingState.opportunities.forEach(opportunity => {
                            opportunity.signalTimestamp = opportunity.signalTimestamp || now;
                        });
                    }

                    console.log('✅ 低风险市场分析完成');
                } else {
                    console.error('❌ 获取市场分析数据失败:', data.error);

                    // 如果API失败，使用基础分析
                    const fallbackAnalysis = {
                        yearlyTrend: {
                            direction: 'neutral',
                            strength: 0,
                            currentPosition: 50
                        },
                        weeklyRange: {
                            averageRange: 0,
                            rangeBreakout: false
                        },
                        overnightExtreme: {
                            extremeType: null,
                            reversal: false,
                            priceChange: 0
                        },
                        trendDirection: {
                            direction: 'neutral',
                            isTrending: false,
                            strength: 0
                        }
                    };

                    lowRiskTradingState.marketAnalysis = fallbackAnalysis;
                    updateMarketAnalysisDisplay();
                    updateTrendState(fallbackAnalysis.trendDirection);
                    lowRiskTradingState.lastAnalysisTime = new Date();
                }

            } catch (error) {
                console.error('❌ 低风险市场分析失败:', error);

                // 网络错误时的回退处理
                const fallbackAnalysis = {
                    yearlyTrend: {
                        direction: 'neutral',
                        strength: 0,
                        currentPosition: 50
                    },
                    weeklyRange: {
                        averageRange: 0,
                        rangeBreakout: false
                    },
                    overnightExtreme: {
                        extremeType: null,
                        reversal: false,
                        priceChange: 0
                    },
                    trendDirection: {
                        direction: 'neutral',
                        isTrending: false,
                        strength: 0
                    }
                };

                lowRiskTradingState.marketAnalysis = fallbackAnalysis;
                updateMarketAnalysisDisplay();
                updateTrendState(fallbackAnalysis.trendDirection);
                lowRiskTradingState.lastAnalysisTime = new Date();
            }
        }

        // 更新市场分析显示
        function updateMarketAnalysisDisplay() {
            const analysis = lowRiskTradingState.marketAnalysis;

            // 年度趋势
            const yearlyElement = document.getElementById('yearlyTrend');
            if (analysis.yearlyTrend) {
                const trend = analysis.yearlyTrend;
                const directionText = trend.direction === 'bullish' ? '上涨' :
                                     trend.direction === 'bearish' ? '下跌' : '横盘';
                const positionText = trend.currentPosition ? `${trend.currentPosition.toFixed(0)}%位置` : '';
                yearlyElement.innerHTML = `${directionText}<br><small>${positionText}</small>`;
                yearlyElement.className = trend.direction === 'bullish' ? 'text-success' :
                                         trend.direction === 'bearish' ? 'text-danger' : 'text-warning';
            }

            // 周波段
            const weeklyElement = document.getElementById('weeklyRange');
            if (analysis.weeklyRange) {
                const range = analysis.weeklyRange;
                weeklyElement.innerHTML = `${range.averageRange.toFixed(2)}%<br><small>平均波段</small>`;
                weeklyElement.className = range.rangeBreakout ? 'text-warning' : 'text-info';
            }

            // 昨夜极值
            const overnightElement = document.getElementById('overnightExtreme');
            if (analysis.overnightExtreme) {
                const extreme = analysis.overnightExtreme;
                const typeText = extreme.extremeType === 'high' ? '高点' : extreme.extremeType === 'low' ? '低点' : '无';
                const changeText = extreme.priceChange ? `${extreme.priceChange > 0 ? '+' : ''}${extreme.priceChange.toFixed(2)}%` : '';
                overnightElement.innerHTML = `${typeText}<br><small>${changeText}</small>`;
                overnightElement.className = extreme.reversal ? 'text-warning' : 'text-muted';
            }

            // 单边检测
            const trendElement = document.getElementById('trendDetection');
            if (analysis.trendDirection) {
                const trend = analysis.trendDirection;
                const directionText = trend.direction === 'bullish' ? '上涨' :
                                     trend.direction === 'bearish' ? '下跌' : '震荡';
                const strengthText = trend.isTrending ? '单边' : '震荡';
                trendElement.innerHTML = `${strengthText}<br><small>${directionText}</small>`;
                trendElement.className = trend.isTrending ? 'text-success' : 'text-muted';
            }
        }

        // ==================== 信号时效性管理 ====================

        // 检查信号是否过期
        function isSignalExpired(signal) {
            if (!signal || !signal.timestamp) return true;

            const now = new Date();
            const signalTime = new Date(signal.timestamp);
            const ageInSeconds = (now - signalTime) / 1000;

            return ageInSeconds > lowRiskTradingConfig.signalExpiry.maxAge;
        }

        // 检查信号是否接近过期（警告状态）
        function isSignalAging(signal) {
            if (!signal || !signal.timestamp) return true;

            const now = new Date();
            const signalTime = new Date(signal.timestamp);
            const ageInSeconds = (now - signalTime) / 1000;

            return ageInSeconds > lowRiskTradingConfig.signalExpiry.warningAge;
        }

        // 获取信号年龄（秒）
        function getSignalAge(signal) {
            if (!signal || !signal.timestamp) return Infinity;

            const now = new Date();
            const signalTime = new Date(signal.timestamp);
            return (now - signalTime) / 1000;
        }

        // 添加新信号
        function addNewSignal(signal) {
            // 确保信号有时间戳
            if (!signal.timestamp) {
                signal.timestamp = new Date().toISOString();
            }

            // 添加到活跃信号列表
            lowRiskTradingState.signals.active.push(signal);
            lowRiskTradingState.signals.lastUpdate = new Date();

            console.log(`✅ 添加新信号: ${signal.type}, 时间: ${signal.timestamp}`);

            // 限制活跃信号数量
            if (lowRiskTradingState.signals.active.length > 10) {
                const oldestSignal = lowRiskTradingState.signals.active.shift();
                lowRiskTradingState.signals.history.push(oldestSignal);

                // 限制历史记录长度
                if (lowRiskTradingState.signals.history.length > 50) {
                    lowRiskTradingState.signals.history.shift();
                }
            }

            return signal;
        }

        // 清理过期信号
        function cleanExpiredSignals() {
            const initialCount = lowRiskTradingState.signals.active.length;
            const now = new Date();

            // 过滤出未过期的信号
            const activeSignals = lowRiskTradingState.signals.active.filter(signal => {
                if (isSignalExpired(signal)) {
                    // 移动到历史记录
                    signal.expiryTime = now.toISOString();
                    lowRiskTradingState.signals.history.push(signal);
                    return false;
                }
                return true;
            });

            // 更新活跃信号列表
            lowRiskTradingState.signals.active = activeSignals;

            const removedCount = initialCount - activeSignals.length;
            if (removedCount > 0) {
                console.log(`🧹 清理了 ${removedCount} 个过期信号`);
            }

            return removedCount;
        }

        // 检查低风险交易机会
        async function checkLowRiskTradingOpportunities() {
            try {
                console.log('🔍 检查低风险交易机会...');

                // 清理过期信号
                cleanExpiredSignals();

                const analysis = lowRiskTradingState.marketAnalysis;
                const opportunities = [];

                // 检查今日交易限制
                if (lowRiskTradingState.todayTradeCount >= lowRiskTradingConfig.dailyLimit) {
                    console.log('⚠️ 今日交易次数已达限制');
                    updateTradingOpportunities([]);
                    return;
                }

                // 检查交易时间
                if (!isWithinTradingHours()) {
                    console.log('⚠️ 当前不在交易时间内');
                    updateTradingOpportunities([]);
                    return;
                }

                // 检查信号更新时间
                if (lowRiskTradingState.signals.lastUpdate) {
                    const signalAge = (new Date() - lowRiskTradingState.signals.lastUpdate) / 1000;
                    console.log(`📊 信号数据年龄: ${signalAge.toFixed(0)}秒`);

                    // 如果信号太旧且启用了自动刷新
                    if (signalAge > lowRiskTradingConfig.signalExpiry.maxAge &&
                        lowRiskTradingConfig.signalExpiry.autoRefresh) {
                        console.log('🔄 信号数据过期，自动刷新...');
                        await performLowRiskMarketAnalysis();
                    }
                }

                // 基于昨夜极值的机会
                if (analysis.overnightExtreme && analysis.overnightExtreme.reversal) {
                    const extreme = analysis.overnightExtreme;

                    if (extreme.extremeType === 'low' && extreme.priceChange > 0.1) {
                        opportunities.push({
                            type: 'reversal_buy',
                            signal: '低点反弹',
                            confidence: 75,
                            reasoning: `从昨夜低点反弹${extreme.priceChange.toFixed(2)}%`
                        });
                    } else if (extreme.extremeType === 'high' && extreme.priceChange < -0.1) {
                        opportunities.push({
                            type: 'reversal_sell',
                            signal: '高点回落',
                            confidence: 75,
                            reasoning: `从昨夜高点回落${Math.abs(extreme.priceChange).toFixed(2)}%`
                        });
                    }
                }

                // 基于单边行情的机会（使用用户配置的参数）
                if (trendDetectionConfig.enabled && analysis.trendDirection && analysis.trendDirection.isTrending) {
                    const trend = analysis.trendDirection;
                    const config = trendDetectionConfig;

                    console.log(`📊 单边行情检测: 趋势强度${trend.strength}%, 阈值${config.trendStrengthThreshold}%`);

                    // 检查趋势强度是否达到用户设置的阈值
                    if (trend.strength >= config.trendStrengthThreshold) {

                        // 检查波动突破（如果有波动数据）
                        let volatilityConfirmed = true;
                        if (analysis.weeklyRange && analysis.weeklyRange.averageRange > 0) {
                            const currentVolatility = analysis.weeklyRange.currentRange || analysis.weeklyRange.averageRange;
                            const volatilityRatio = currentVolatility / analysis.weeklyRange.averageRange;
                            volatilityConfirmed = volatilityRatio >= config.volatilityBreakoutMultiplier;
                            console.log(`📈 波动检测: 当前${volatilityRatio.toFixed(2)}倍, 要求${config.volatilityBreakoutMultiplier}倍`);
                        }

                        // 检查多时间框架确认
                        let multiTimeframeConfirmed = true;
                        if (config.multiTimeframeConfirm) {
                            // 这里假设analysis.trendDirection.isTrending已经包含了多时间框架确认
                            multiTimeframeConfirmed = analysis.trendDirection.isTrending;
                            console.log(`🔄 多时间框架确认: ${multiTimeframeConfirmed ? '通过' : '未通过'}`);
                        }

                        // 检查趋势持续时间（简化版本，实际应该检查趋势开始时间）
                        let timeConfirmed = true;
                        if (analysis.timestamp) {
                            const analysisTime = new Date(analysis.timestamp);
                            const now = new Date();
                            const durationMinutes = (now - analysisTime) / (1000 * 60);
                            // 这里简化处理，实际应该跟踪趋势开始时间
                            timeConfirmed = durationMinutes >= (config.trendConfirmationTime / 4); // 放宽时间要求
                            console.log(`⏰ 时间确认: 已持续${durationMinutes.toFixed(0)}分钟, 要求${config.trendConfirmationTime}分钟`);
                        }

                        // 所有条件都满足才生成交易机会
                        if (volatilityConfirmed && multiTimeframeConfirmed && timeConfirmed) {

                            if (trend.direction === 'bullish') {
                                opportunities.push({
                                    type: 'trend_buy',
                                    signal: '单边上涨',
                                    confidence: Math.min(95, 60 + (trend.strength - config.trendStrengthThreshold)),
                                    reasoning: `单边上涨趋势确认，强度${trend.strength.toFixed(1)}%`,
                                    trendDetectionUsed: true,
                                    configUsed: {
                                        strengthThreshold: config.trendStrengthThreshold,
                                        volatilityMultiplier: config.volatilityBreakoutMultiplier,
                                        confirmationTime: config.trendConfirmationTime
                                    }
                                });
                                console.log(`✅ 生成做多机会: 趋势强度${trend.strength}% >= ${config.trendStrengthThreshold}%`);
                            } else if (trend.direction === 'bearish') {
                                opportunities.push({
                                    type: 'trend_sell',
                                    signal: '单边下跌',
                                    confidence: Math.min(95, 60 + (trend.strength - config.trendStrengthThreshold)),
                                    reasoning: `单边下跌趋势确认，强度${trend.strength.toFixed(1)}%`,
                                    trendDetectionUsed: true,
                                    configUsed: {
                                        strengthThreshold: config.trendStrengthThreshold,
                                        volatilityMultiplier: config.volatilityBreakoutMultiplier,
                                        confirmationTime: config.trendConfirmationTime
                                    }
                                });
                                console.log(`✅ 生成做空机会: 趋势强度${trend.strength}% >= ${config.trendStrengthThreshold}%`);
                            }
                        } else {
                            console.log(`⚠️ 单边行情条件未完全满足: 波动${volatilityConfirmed}, 多时间框架${multiTimeframeConfirmed}, 时间${timeConfirmed}`);
                        }
                    } else {
                        console.log(`⚠️ 趋势强度不足: ${trend.strength}% < ${config.trendStrengthThreshold}%`);
                    }
                } else if (!trendDetectionConfig.enabled) {
                    console.log('📊 单边行情检测已禁用');
                }

                // 过滤低置信度机会 (易触发策略使用更低的门槛)
                const currentPreset = document.getElementById('strategyPreset')?.value || 'optimized';
                const confidenceThreshold = currentPreset === 'easyTrigger' ? 30 : 60; // 易触发策略使用30%门槛

                if (currentPreset === 'easyTrigger') {
                    console.log(`🧪 易触发策略模式: 使用${confidenceThreshold}%置信度门槛`);

                    // 为易触发策略生成额外的测试机会
                    if (opportunities.length === 0) {
                        // 基于简单价格波动生成测试机会
                        const randomFactor = Math.random();
                        const isBuy = randomFactor > 0.5;

                        // 确保生成的测试机会置信度高于门槛
                        const testConfidence = Math.max(confidenceThreshold + 5, 35 + Math.floor(Math.random() * 30));

                        opportunities.push({
                            type: isBuy ? 'test_buy' : 'test_sell',
                            signal: isBuy ? '测试买入信号' : '测试卖出信号',
                            confidence: testConfidence, // 确保高于门槛
                            reasoning: `易触发策略测试机会 (当前价格: $${analysis.currentPrice || 2650})`,
                            price: analysis.currentPrice || 2650,
                            isTestSignal: true
                        });

                        console.log('🧪 易触发策略: 生成测试交易机会');
                    }

                    // 降低现有机会的置信度要求，增加触发概率
                    opportunities.forEach(op => {
                        if (op.confidence < 50) {
                            op.confidence = Math.min(op.confidence + 15, 65); // 提升置信度
                            op.reasoning += ' (易触发策略增强)';
                        }
                    });
                }

                // 在增强机会后再进行过滤
                const filteredOpportunities = opportunities.filter(op => op.confidence >= confidenceThreshold);

                console.log(`🔧 过滤前机会数量: ${opportunities.length}, 过滤后: ${filteredOpportunities.length}`);
                if (currentPreset === 'easyTrigger' && opportunities.length > 0) {
                    opportunities.forEach((op, index) => {
                        console.log(`🔧 机会${index + 1}: ${op.signal}, 置信度: ${op.confidence}%, 门槛: ${confidenceThreshold}%`);
                    });
                }

                lowRiskTradingState.opportunities = filteredOpportunities;
                updateTradingOpportunities(filteredOpportunities);

                // 自动交易前检查信号时效性
                if (lowRiskTradingConfig.autoTrade && filteredOpportunities.length > 0) {
                    const opportunity = filteredOpportunities[0];

                    // 检查机会是否基于过期信号
                    if (opportunity.signalTimestamp) {
                        const signalAge = getSignalAge({ timestamp: opportunity.signalTimestamp });

                        if (signalAge > lowRiskTradingConfig.signalExpiry.maxAge) {
                            console.warn(`⚠️ 交易机会基于过期信号 (${signalAge.toFixed(0)}秒前)，跳过自动交易`);
                            showWarning(`信号已过期 (${Math.floor(signalAge/60)}分钟前)，请刷新后重试`);
                            return;
                        } else if (signalAge > lowRiskTradingConfig.signalExpiry.warningAge) {
                            console.warn(`⚠️ 交易机会基于较旧信号 (${signalAge.toFixed(0)}秒前)`);
                        }
                    }

                    await executeAutoLowRiskTrade(opportunity);
                }

                console.log(`✅ 发现${filteredOpportunities.length}个低风险交易机会`);

            } catch (error) {
                console.error('❌ 检查低风险交易机会失败:', error);
            }
        }

        // 更新交易机会显示
        function updateTradingOpportunities(opportunities) {
            const container = document.getElementById('lowRiskOpportunities');

            if (opportunities.length === 0) {
                container.innerHTML = `
                    <div class="text-center py-4">
                        <i class="fas fa-search fa-2x text-muted mb-3"></i>
                        <p class="text-muted">暂无交易机会</p>
                    </div>
                `;
                return;
            }

            let html = '';
            opportunities.forEach((op, index) => {
                const actionText = op.type.includes('buy') ? '做多' : '做空';
                const actionClass = op.type.includes('buy') ? 'text-success' : 'text-danger';
                const confidenceColor = op.confidence >= 80 ? 'bg-success' : op.confidence >= 70 ? 'bg-warning' : 'bg-info';

                html += `
                    <div class="opportunity-card p-3 mb-3">
                        <div class="d-flex justify-content-between align-items-center">
                            <div>
                                <h6 class="${actionClass} mb-1">${actionText}</h6>
                                <div class="text-white mb-1">${op.signal}</div>
                                <small class="text-light">${op.reasoning}</small>
                            </div>
                            <div class="text-end">
                                <div class="badge ${confidenceColor} mb-2">${op.confidence.toFixed(0)}%</div>
                                ${lowRiskTradingConfig.autoTrade ? '' :
                                  `<br><button class="btn btn-sm btn-outline-light" onclick="executeOpportunity(${index})">执行</button>`}
                            </div>
                        </div>
                    </div>
                `;
            });

            container.innerHTML = html;
        }

        // 执行特定机会
        async function executeOpportunity(index) {
            const opportunity = lowRiskTradingState.opportunities[index];
            if (!opportunity) return;

            const direction = opportunity.type.includes('buy') ? 'buy' : 'sell';
            await executeLowRiskTrade(direction);
        }

        // 检查是否在交易时间内
        function isWithinTradingHours() {
            const now = new Date();
            const dayOfWeek = now.getDay(); // 0=周日, 1=周一, ..., 6=周六

            // 检查是否是周末 (周六=6, 周日=0)
            if (dayOfWeek === 0 || dayOfWeek === 6) {
                console.log(`⚠️ 周末非交易时间: ${dayOfWeek === 0 ? '周日' : '周六'}`);
                return false;
            }

            // 检查具体时间 (周一到周五)
            const currentTime = now.getHours() * 100 + now.getMinutes();
            const startTime = parseInt(lowRiskTradingConfig.tradingHours.start.replace(':', ''));
            const endTime = parseInt(lowRiskTradingConfig.tradingHours.end.replace(':', ''));

            const isWithinTime = currentTime >= startTime && currentTime <= endTime;

            if (!isWithinTime) {
                console.log(`⚠️ 不在交易时间内: 当前${Math.floor(currentTime/100)}:${(currentTime%100).toString().padStart(2,'0')}, 交易时间${lowRiskTradingConfig.tradingHours.start}-${lowRiskTradingConfig.tradingHours.end}`);
            }

            return isWithinTime;
        }

        // 防重复点击状态
        let isExecutingTrade = false;

        // 执行低风险交易
        async function executeLowRiskTrade(direction, signalId = null, isAutoTrade = false) {
            try {
                // 防重复点击检查
                if (isExecutingTrade) {
                    console.log('⚠️ 交易正在执行中，忽略重复点击');
                    return;
                }

                isExecutingTrade = true;
                const tradeType = isAutoTrade ? '自动' : '手动';
                console.log(`🛡️ 执行${tradeType}低风险交易: ${direction}`);

                // 交易执行中，无需禁用按钮（手动交易按钮已删除）

                // 检查交易限制
                if (lowRiskTradingState.todayTradeCount >= lowRiskTradingConfig.dailyLimit) {
                    showError('今日交易次数已达限制');
                    return;
                }

                if (!isWithinTradingHours()) {
                    showError('当前不在交易时间内');
                    return;
                }

                // 获取当前真实价格
                const currentPrice = await getCurrentRealPrice();

                // 计算止损止盈
                const stopLoss = direction === 'buy' ?
                    currentPrice * (1 - lowRiskTradingConfig.stopLossPercent / 100) :
                    currentPrice * (1 + lowRiskTradingConfig.stopLossPercent / 100);

                const takeProfit = direction === 'buy' ?
                    currentPrice * (1 + lowRiskTradingConfig.takeProfitPercent / 100) :
                    currentPrice * (1 - lowRiskTradingConfig.takeProfitPercent / 100);

                // 直接执行交易，无需用户确认
                console.log(`🚀 执行低风险${direction === 'buy' ? '做多' : '做空'}交易:`, {
                    入场价: currentPrice.toFixed(4),
                    止损价: stopLoss.toFixed(4),
                    止盈价: takeProfit.toFixed(4),
                    手数: lowRiskTradingConfig.lotSize,
                    风险: lowRiskTradingConfig.stopLossPercent + '%'
                });

                // 执行真实交易 - 调用API
                const tradeData = {
                    symbol: currentSymbol,
                    action: direction,
                    volume: lowRiskTradingConfig.lotSize,
                    entry_price: currentPrice,
                    stop_loss: stopLoss,
                    take_profit: takeProfit,
                    comment: isAutoTrade ? `Auto_${direction.toUpperCase()}_${Date.now().toString().slice(-6)}` : `Manual_${direction.toUpperCase()}_${Date.now().toString().slice(-6)}`
                };

                const response = await fetch('/api/low-risk-trading/execute-trade', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify(tradeData)
                });

                const result = await response.json();

                if (result.success) {
                    const tradeType = isAutoTrade ? '自动' : '手动';
                    showSuccess(`${tradeType}交易执行成功: ${direction.toUpperCase()} ${currentSymbol}`, isAutoTrade);
                    lowRiskTradingState.todayTradeCount++;

                    // 刷新持仓显示
                    await loadLowRiskPositions();
                    updateTodayStats();
                } else {
                    showError('手动交易执行失败: ' + result.error);
                }

            } catch (error) {
                console.error('❌ 执行手动低风险交易失败:', error);
                showError('执行交易失败: ' + error.message);
            } finally {
                // 重置交易执行状态
                isExecutingTrade = false;
            }
        }

        // 执行自动低风险交易
        async function executeAutoLowRiskTrade(opportunity) {
            let signalId = null;

            try {
                console.log('🤖 执行自动低风险交易:', opportunity);

                // 首先保存入场信号
                const signalData = {
                    signal_type: opportunity.type,
                    symbol: currentSymbol,
                    action: opportunity.type.includes('buy') ? 'buy' : 'sell',
                    strength: opportunity.strength || 0,
                    confidence: opportunity.confidence || 0,
                    signal_price: opportunity.price || 0,
                    market_analysis: {
                        opportunity: opportunity,
                        timestamp: new Date().toISOString()
                    },
                    is_executed: false,
                    execution_result: 'pending',
                    execution_reason: '准备执行自动交易'
                };

                signalId = await saveEntrySignal(signalData);
                console.log('📊 入场信号已保存，ID:', signalId);

                // 检查置信度 (易触发策略使用更低的门槛)
                const currentPreset = document.getElementById('strategyPreset')?.value || 'optimized';
                const requiredConfidence = currentPreset === 'easyTrigger' ? 30 : 70;

                if (opportunity.confidence < requiredConfidence) {
                    console.log(`⚠️ 机会置信度不足，跳过自动交易 (${opportunity.confidence}% < ${requiredConfidence}%)`);

                    // 更新信号状态为跳过
                    if (signalId) {
                        await updateEntrySignalExecution(signalId, {
                            is_executed: false,
                            execution_result: 'skipped',
                            execution_reason: `置信度不足（${opportunity.confidence}% < ${requiredConfidence}%）`
                        });
                    }
                    return;
                }

                if (currentPreset === 'easyTrigger') {
                    console.log(`🧪 易触发策略: 置信度${opportunity.confidence}%通过${requiredConfidence}%门槛`);
                }

                const direction = opportunity.type.includes('buy') ? 'buy' : 'sell';

                // 如果是基于单边行情检测的交易，调整交易参数
                let tradeResult = null;
                if (opportunity.trendDetectionUsed && trendDetectionConfig.enabled) {
                    console.log('📊 使用单边行情检测参数执行交易');
                    tradeResult = await executeEnhancedLowRiskTrade(direction, opportunity, signalId);
                } else {
                    // 使用标准参数
                    tradeResult = await executeLowRiskTrade(direction, signalId, true);
                }

                // 更新信号执行状态
                if (signalId && tradeResult) {
                    await updateEntrySignalExecution(signalId, {
                        is_executed: tradeResult.success,
                        execution_result: tradeResult.success ? 'success' : 'failed',
                        execution_reason: tradeResult.message || '交易执行完成',
                        entry_price: tradeResult.entry_price,
                        trade_id: tradeResult.trade_id
                    });
                }

            } catch (error) {
                console.error('❌ 执行自动低风险交易失败:', error);

                // 更新信号状态为失败
                if (signalId) {
                    await updateEntrySignalExecution(signalId, {
                        is_executed: false,
                        execution_result: 'failed',
                        execution_reason: `交易执行异常: ${error.message}`
                    });
                }
            }
        }

        // 使用增强参数执行低风险交易（单边行情模式）
        async function executeEnhancedLowRiskTrade(direction, opportunity, signalId = null) {
            try {
                console.log(`🚀 执行增强低风险交易: ${direction}`, opportunity.configUsed);

                // 检查交易限制
                if (lowRiskTradingState.todayTradeCount >= lowRiskTradingConfig.dailyLimit) {
                    showError('今日交易次数已达限制');
                    return;
                }

                // 获取当前价格
                const currentPrice = await getCurrentPrice(currentSymbol);
                if (!currentPrice) {
                    showError('无法获取当前价格');
                    return;
                }

                // 根据单边行情强度调整止损止盈
                const baseStopLoss = lowRiskTradingConfig.stopLoss;
                const baseTakeProfit = lowRiskTradingConfig.takeProfit;

                // 单边行情中适当放宽止损，扩大止盈
                const trendStrengthFactor = opportunity.confidence / 100; // 0.7-0.95
                const adjustedStopLoss = baseStopLoss * (1 + trendStrengthFactor * 0.5); // 最多增加50%
                const adjustedTakeProfit = baseTakeProfit * (1 + trendStrengthFactor * 0.8); // 最多增加80%

                let stopLoss, takeProfit;

                if (direction === 'buy') {
                    stopLoss = currentPrice * (1 - adjustedStopLoss / 100);
                    takeProfit = currentPrice * (1 + adjustedTakeProfit / 100);
                } else {
                    stopLoss = currentPrice * (1 + adjustedStopLoss / 100);
                    takeProfit = currentPrice * (1 - adjustedTakeProfit / 100);
                }

                console.log(`📊 单边行情交易参数:
                    方向: ${direction}
                    当前价: ${currentPrice}
                    调整后止损: ${adjustedStopLoss.toFixed(2)}% (原${baseStopLoss}%)
                    调整后止盈: ${adjustedTakeProfit.toFixed(2)}% (原${baseTakeProfit}%)
                    止损价: ${stopLoss.toFixed(2)}
                    止盈价: ${takeProfit.toFixed(2)}
                    置信度: ${opportunity.confidence}%`);

                // 执行真实交易 - 调用API
                const tradeData = {
                    symbol: currentSymbol,
                    action: direction,
                    volume: lowRiskTradingConfig.lotSize,
                    entry_price: currentPrice,
                    stop_loss: stopLoss,
                    take_profit: takeProfit,
                    comment: `TrendAuto_${direction.toUpperCase()}_${Math.round(opportunity.confidence)}`
                };

                console.log('📤 发送增强交易请求:', tradeData);

                const response = await fetch('/api/low-risk-trading/execute-trade', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify(tradeData)
                });

                const result = await response.json();

                if (result.success) {
                    console.log('✅ 增强低风险交易执行成功:', result);

                    // 更新统计
                    lowRiskTradingState.todayTradeCount++;
                    lowRiskTradingState.totalTrades++;

                    // 保存状态
                    saveLowRiskTradingState();

                    // 更新显示
                    updateTodayStats();

                    // 刷新持仓
                    setTimeout(() => loadLowRiskPositions(), 2000);

                    showSuccess(`自动交易执行成功: ${direction.toUpperCase()} ${currentSymbol} (单边行情模式, 置信度${opportunity.confidence}%)`, true);
                } else {
                    console.error('❌ 增强低风险交易执行失败:', result.error);
                    showError('增强交易执行失败: ' + result.error);
                }

            } catch (error) {
                console.error('❌ 执行增强低风险交易失败:', error);
                showError('执行增强交易失败: ' + error.message);
            }
        }

        // 更新今日统计
        function updateTodayStats() {
            document.getElementById('todayTradeCount').textContent = lowRiskTradingState.todayTradeCount;
            document.getElementById('dailyTradeLimit').textContent = lowRiskTradingConfig.dailyLimit;
            document.getElementById('todayPnL').textContent = `$${lowRiskTradingState.todayPnL.toFixed(2)}`;
            document.getElementById('todayPnL').className = lowRiskTradingState.todayPnL >= 0 ? 'h4 mb-1 text-success' : 'h4 mb-1 text-danger';
        }

        // 更新持仓显示
        function updatePositionsDisplay() {
            const container = document.getElementById('lowRiskPositions');
            const positions = lowRiskTradingState.positions;

            if (positions.length === 0) {
                container.innerHTML = `
                    <div class="text-center py-4">
                        <i class="fas fa-inbox fa-2x text-muted mb-3"></i>
                        <p class="text-muted">暂无持仓</p>
                    </div>
                `;
                return;
            }

            let html = '';
            positions.forEach(pos => {
                const pnlClass = pos.pnl >= 0 ? 'text-success' : 'text-danger';
                const directionText = pos.type === 'buy' ? '多' : '空';
                const directionClass = pos.type === 'buy' ? 'text-success' : 'text-danger';

                // 确定策略类型和显示信息
                let strategyInfo = '';
                let strategyBadgeClass = '';
                let strategyText = '';

                if (pos.operation_type === 'auto') {
                    strategyBadgeClass = 'badge bg-primary';

                    // 优先显示具体的策略名称
                    if (pos.strategy_name) {
                        // 如果策略名称太长，进行缩短处理
                        let displayName = pos.strategy_name;
                        if (displayName.length > 20) {
                            displayName = displayName.substring(0, 17) + '...';
                        }
                        strategyText = `🤖 ${displayName}`;
                    } else {
                        // 回退到从comment中提取策略信息
                        if (pos.comment && pos.comment.includes('AI_')) {
                            strategyText = '🤖 AI策略';
                        } else if (pos.comment && pos.comment.includes('TrendAuto_')) {
                            strategyText = '📈 趋势策略';
                        } else if (pos.comment && pos.comment.includes('ScalpAuto_')) {
                            strategyText = '⚡ 剥头皮策略';
                        } else {
                            strategyText = '🤖 自动交易';
                        }
                    }
                } else {
                    strategyBadgeClass = 'badge bg-secondary';
                    strategyText = '👤 手动交易';
                }

                strategyInfo = `<span class="${strategyBadgeClass} mb-1" style="font-size: 0.7rem;" title="${pos.strategy_name || strategyText}">${strategyText}</span>`;

                html += `
                    <div class="position-card p-3 mb-3">
                        <div class="d-flex justify-content-between align-items-center">
                            <div>
                                <div class="d-flex align-items-center mb-1">
                                    <h6 class="${directionClass} mb-0 me-2">${directionText}</h6>
                                    ${strategyInfo}
                                </div>
                                <div class="text-white mb-1">${pos.volume}手</div>
                                <small class="text-light">入场: ${pos.entry_price}</small>
                            </div>
                            <div class="text-end">
                                <div class="${pnlClass} mb-2">${pos.pnl >= 0 ? '+' : ''}${pos.pnl.toFixed(2)}</div>
                                <button class="btn btn-sm btn-outline-warning" onclick="closeLowRiskPosition('${pos.ticket}')">平仓</button>
                            </div>
                        </div>
                    </div>
                `;
            });

            container.innerHTML = html;
        }

        // 平仓单个低风险持仓
        async function closeLowRiskPosition(ticket) {
            try {
                console.log('🔒 直接平仓持仓:', ticket);

                // 调用真实平仓API
                const response = await fetch('/api/low-risk-trading/close-position', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({ position_id: ticket })
                });

                const result = await response.json();

                if (result.success) {
                    showSuccess('持仓平仓成功');
                    await loadLowRiskPositions(); // 刷新持仓显示
                    updateTodayStats();
                } else {
                    showError('平仓失败: ' + result.error);
                }

            } catch (error) {
                console.error('❌ 平仓失败:', error);
                showError('平仓失败: ' + error.message);
            }
        }

        // 一键平仓所有低风险持仓
        async function closeAllLowRiskPositions() {
            try {
                // 添加确认对话框
                const confirmed = confirm('确定要平仓所有低风险交易持仓吗？\n\n此操作不可撤销，将关闭所有当前持仓。');
                if (!confirmed) {
                    console.log('🚫 用户取消了一键平仓操作');
                    return;
                }

                console.log('🔒 执行一键平仓所有低风险持仓');

                // 调用真实一键平仓API
                const response = await fetch('/api/low-risk-trading/close-all-positions', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    }
                });

                const result = await response.json();

                if (result.success) {
                    showSuccess(result.message || '一键平仓执行成功');
                    await loadLowRiskPositions(); // 刷新持仓显示
                    updateTodayStats();
                } else {
                    showError('一键平仓失败: ' + result.error);
                }

            } catch (error) {
                console.error('❌ 批量平仓失败:', error);
                showError('批量平仓失败: ' + error.message);
            }
        }

        // 显示低风险设置
        function showLowRiskSettings() {
            console.log('🔧 打开设置时的autoTrade状态:', lowRiskTradingConfig.autoTrade);

            // 加载当前设置到模态框
            document.getElementById('lowRiskDailyLimit').value = lowRiskTradingConfig.dailyLimit;
            document.getElementById('lowRiskLotSize').value = lowRiskTradingConfig.lotSize;
            document.getElementById('lowRiskStopLoss').value = lowRiskTradingConfig.stopLossPercent;
            document.getElementById('lowRiskTakeProfit').value = lowRiskTradingConfig.takeProfitPercent;
            const autoTradeCheckbox = document.getElementById('lowRiskAutoTrade');
            console.log('🔧 checkbox元素:', autoTradeCheckbox);
            console.log('🔧 设置前checkbox状态:', autoTradeCheckbox ? autoTradeCheckbox.checked : 'null');

            if (autoTradeCheckbox) {
                autoTradeCheckbox.checked = lowRiskTradingConfig.autoTrade;
                console.log('🔧 设置后checkbox状态:', autoTradeCheckbox.checked);
            } else {
                console.error('❌ 找不到lowRiskAutoTrade元素');
            }

            console.log('🔧 设置checkbox为:', lowRiskTradingConfig.autoTrade);
            document.getElementById('lowRiskTrendDetection').checked = lowRiskTradingConfig.trendDetection;
            document.getElementById('lowRiskMinSignals').value = lowRiskTradingConfig.minSignals;
            document.getElementById('lowRiskStartTime').value = lowRiskTradingConfig.tradingHours.start;
            document.getElementById('lowRiskEndTime').value = lowRiskTradingConfig.tradingHours.end;
            document.getElementById('lowRiskYearlyPeriod').value = lowRiskTradingConfig.analysis.yearlyPeriod;
            document.getElementById('lowRiskWeeklyDays').value = lowRiskTradingConfig.analysis.weeklyDays;
            document.getElementById('lowRiskOvernightStart').value = lowRiskTradingConfig.analysis.overnightStart;

            // 更新滑块显示
            updateSliderDisplay();

            // 显示模态框
            const modal = new bootstrap.Modal(document.getElementById('lowRiskSettingsModal'));
            modal.show();
        }

        // 更新滑块显示
        function updateSliderDisplay() {
            const stopLossSlider = document.getElementById('lowRiskStopLoss');
            const takeProfitSlider = document.getElementById('lowRiskTakeProfit');

            document.getElementById('lowRiskStopLossValue').textContent = stopLossSlider.value + '%';
            document.getElementById('lowRiskTakeProfitValue').textContent = takeProfitSlider.value + '%';

            stopLossSlider.oninput = function() {
                document.getElementById('lowRiskStopLossValue').textContent = this.value + '%';
            };

            takeProfitSlider.oninput = function() {
                document.getElementById('lowRiskTakeProfitValue').textContent = this.value + '%';
            };
        }

        // 保存低风险设置
        function saveLowRiskSettings() {
            try {
                // 获取设置值
                lowRiskTradingConfig.dailyLimit = parseInt(document.getElementById('lowRiskDailyLimit').value);
                lowRiskTradingConfig.lotSize = parseFloat(document.getElementById('lowRiskLotSize').value);
                lowRiskTradingConfig.stopLossPercent = parseFloat(document.getElementById('lowRiskStopLoss').value);
                lowRiskTradingConfig.takeProfitPercent = parseFloat(document.getElementById('lowRiskTakeProfit').value);
                lowRiskTradingConfig.autoTrade = document.getElementById('lowRiskAutoTrade').checked;
                console.log('🔧 保存autoTrade状态:', lowRiskTradingConfig.autoTrade);
                lowRiskTradingConfig.trendDetection = document.getElementById('lowRiskTrendDetection').checked;
                lowRiskTradingConfig.minSignals = parseInt(document.getElementById('lowRiskMinSignals').value);
                lowRiskTradingConfig.tradingHours.start = document.getElementById('lowRiskStartTime').value;
                lowRiskTradingConfig.tradingHours.end = document.getElementById('lowRiskEndTime').value;
                lowRiskTradingConfig.analysis.yearlyPeriod = parseInt(document.getElementById('lowRiskYearlyPeriod').value);
                lowRiskTradingConfig.analysis.weeklyDays = parseInt(document.getElementById('lowRiskWeeklyDays').value);
                lowRiskTradingConfig.analysis.overnightStart = document.getElementById('lowRiskOvernightStart').value;

                // 保存到本地存储
                saveLowRiskTradingState();

                // 更新UI
                updateTodayStats();

                // 关闭模态框
                const modal = bootstrap.Modal.getInstance(document.getElementById('lowRiskSettingsModal'));
                modal.hide();

                showSuccess('低风险交易设置已保存');
                console.log('✅ 低风险交易设置已保存:', lowRiskTradingConfig);

            } catch (error) {
                console.error('❌ 保存低风险设置失败:', error);
                showError('保存设置失败: ' + error.message);
            }
        }

        // 加载今日低风险交易统计
        async function loadTodayLowRiskStats() {
            try {
                // 从服务器获取今日实际统计数据
                const response = await fetch('/api/low-risk-trading/today-stats');
                if (response.ok) {
                    const stats = await response.json();
                    if (stats.success) {
                        lowRiskTradingState.todayTradeCount = stats.trade_count || 0;
                        lowRiskTradingState.todayPnL = stats.total_pnl || 0;
                        console.log(`📊 加载今日统计: 交易${lowRiskTradingState.todayTradeCount}笔, 盈亏$${lowRiskTradingState.todayPnL.toFixed(2)}`);
                    } else {
                        // 如果API失败，使用默认值
                        lowRiskTradingState.todayTradeCount = 0;
                        lowRiskTradingState.todayPnL = 0;
                    }
                } else {
                    // 如果请求失败，使用默认值
                    lowRiskTradingState.todayTradeCount = 0;
                    lowRiskTradingState.todayPnL = 0;
                }
                updateTodayStats();
            } catch (error) {
                console.error('加载今日统计失败:', error);
                // 使用默认值
                lowRiskTradingState.todayTradeCount = 0;
                lowRiskTradingState.todayPnL = 0;
                updateTodayStats();
            }
        }

        // 旧的loadLowRiskPositions函数已删除，使用增强版本

        // 保存低风险交易状态到本地存储
        function saveLowRiskTradingState() {
            try {
                console.log('🔧 准备保存时的autoTrade状态:', lowRiskTradingConfig.autoTrade);

                // 获取当前选择的策略预设和时间间隔
                const currentPreset = document.getElementById('strategyPreset')?.value || 'optimized';
                const currentTimeframe = document.getElementById('autoTradingTimeframe')?.value || '1h';

                const stateData = {
                    config: lowRiskTradingConfig,
                    state: {
                        active: lowRiskTradingState.active,
                        todayTradeCount: lowRiskTradingState.todayTradeCount,
                        todayPnL: lowRiskTradingState.todayPnL
                    },
                    selectedPreset: currentPreset,  // 保存当前选择的策略
                    selectedTimeframe: currentTimeframe  // 保存当前选择的时间间隔
                };

                console.log('🔧 即将保存的config.autoTrade:', stateData.config.autoTrade);
                localStorage.setItem('lowRiskTradingState', JSON.stringify(stateData));
                console.log('✅ 低风险交易状态已保存');
            } catch (error) {
                console.error('❌ 保存低风险交易状态失败:', error);
            }
        }

        // 从本地存储恢复低风险交易状态
        function restoreLowRiskTradingState() {
            try {
                const savedState = localStorage.getItem('lowRiskTradingState');
                console.log('🔧 localStorage中的数据:', savedState);
                if (!savedState) {
                    console.log('🔧 没有找到保存的状态');
                    return;
                }

                const stateData = JSON.parse(savedState);
                console.log('🔧 解析后的数据:', stateData);

                // 恢复配置
                if (stateData.config) {
                    console.log('🔧 恢复前的autoTrade:', lowRiskTradingConfig.autoTrade);
                    console.log('🔧 保存的autoTrade:', stateData.config.autoTrade);
                    lowRiskTradingConfig = { ...lowRiskTradingConfig, ...stateData.config };
                    console.log('🔧 恢复后的autoTrade:', lowRiskTradingConfig.autoTrade);
                }

                // 恢复策略预设选择
                if (stateData.selectedPreset) {
                    const strategySelect = document.getElementById('strategyPreset');
                    if (strategySelect) {
                        strategySelect.value = stateData.selectedPreset;
                        console.log('🔧 恢复策略预设选择:', stateData.selectedPreset);

                        // 静默应用策略（不显示提示）
                        applyStrategyPreset(stateData.selectedPreset, false);
                    }
                }

                // 恢复时间间隔选择
                if (stateData.selectedTimeframe) {
                    const timeframeSelect = document.getElementById('autoTradingTimeframe');
                    if (timeframeSelect) {
                        timeframeSelect.value = stateData.selectedTimeframe;
                        console.log('🔧 恢复时间间隔选择:', stateData.selectedTimeframe);
                    }
                }

                // 恢复状态
                if (stateData.state) {
                    lowRiskTradingState.todayTradeCount = stateData.state.todayTradeCount || 0;
                    lowRiskTradingState.todayPnL = stateData.state.todayPnL || 0;

                    // 如果之前是激活状态，重新启动
                    if (stateData.state.active && lowRiskTradingConfig.enabled) {
                        const switchElement = document.getElementById('lowRiskTradingSwitch');
                        if (switchElement) {
                            switchElement.checked = true;
                            console.log('🔧 系统恢复启动，正在重新启动系统...');
                            setTimeout(() => {
                                startLowRiskTrading();
                            }, 1000);
                        }
                    }
                }

                console.log('✅ 低风险交易状态已恢复');

                // 延迟检查配置是否被其他代码修改
                setTimeout(() => {
                    console.log('🔧 延迟检查autoTrade状态:', lowRiskTradingConfig.autoTrade);
                }, 2000);
            } catch (error) {
                console.error('❌ 恢复低风险交易状态失败:', error);
            }
        }

        // ==================== 入场信号功能 ====================

        // 保存入场信号到数据库
        async function saveEntrySignal(signalData) {
            try {
                console.log('🔧 准备保存入场信号:', signalData);

                const response = await fetch('/api/entry-signals', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify(signalData)
                });

                console.log('🔧 API响应状态:', response.status);

                if (!response.ok) {
                    console.error('❌ API响应错误:', response.status, response.statusText);
                    return null;
                }

                const result = await response.json();
                console.log('🔧 API响应结果:', result);

                if (result.success) {
                    console.log('✅ 入场信号已保存:', result.signal_id);
                    // 刷新入场信号显示
                    loadEntrySignals();
                    return result.signal_id;
                } else {
                    console.error('❌ 保存入场信号失败:', result.error);
                    return null;
                }
            } catch (error) {
                console.error('❌ 保存入场信号异常:', error);
                return null;
            }
        }

        // 更新入场信号执行状态
        async function updateEntrySignalExecution(signalId, executionData) {
            try {
                const response = await fetch(`/api/entry-signals/${signalId}`, {
                    method: 'PUT',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify(executionData)
                });

                const result = await response.json();
                if (result.success) {
                    console.log('✅ 入场信号执行状态已更新');
                    loadEntrySignals();
                } else {
                    console.error('❌ 更新入场信号失败:', result.error);
                }
            } catch (error) {
                console.error('❌ 更新入场信号异常:', error);
            }
        }

        // 加载入场信号列表
        async function loadEntrySignals() {
            try {
                const response = await fetch('/api/entry-signals?limit=20');
                const result = await response.json();

                if (result.success) {
                    updateEntrySignalsDisplay(result.signals);
                } else {
                    console.error('❌ 加载入场信号失败:', result.error);
                }
            } catch (error) {
                console.error('❌ 加载入场信号异常:', error);
            }
        }

        // 更新入场信号显示
        function updateEntrySignalsDisplay(signals) {
            const container = document.getElementById('entrySignals');

            if (signals.length === 0) {
                container.innerHTML = `
                    <div class="text-center py-4">
                        <i class="fas fa-wave-square fa-2x text-muted mb-3"></i>
                        <p class="text-muted">暂无入场信号</p>
                    </div>
                `;
                return;
            }

            const signalsHtml = signals.map(signal => {
                const signalTime = new Date(signal.signal_time).toLocaleString();
                const executionTime = signal.execution_time ? new Date(signal.execution_time).toLocaleString() : '-';

                const actionClass = signal.action === 'buy' ? 'text-success' : 'text-danger';
                const actionIcon = signal.action === 'buy' ? 'fa-arrow-up' : 'fa-arrow-down';

                const statusClass = signal.is_executed ?
                    (signal.execution_result === 'success' ? 'success' : 'warning') : 'secondary';
                const statusText = signal.is_executed ?
                    (signal.execution_result === 'success' ? '已执行' : '执行失败') : '未执行';

                return `
                    <div class="alert alert-light border mb-2">
                        <div class="row align-items-center">
                            <div class="col-md-3">
                                <div class="d-flex align-items-center">
                                    <i class="fas ${actionIcon} ${actionClass} me-2"></i>
                                    <div>
                                        <strong class="${actionClass}">${signal.action.toUpperCase()}</strong>
                                        <br>
                                        <small class="text-muted">${signal.signal_type}</small>
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-2">
                                <small class="text-muted">信号价格</small>
                                <br>
                                <strong>$${signal.signal_price}</strong>
                            </div>
                            <div class="col-md-2">
                                <small class="text-muted">执行价格</small>
                                <br>
                                <strong>${signal.entry_price ? '$' + signal.entry_price : '-'}</strong>
                            </div>
                            <div class="col-md-2">
                                <span class="badge bg-${statusClass}">${statusText}</span>
                                ${signal.execution_reason ? `<br><small class="text-muted">${signal.execution_reason}</small>` : ''}
                            </div>
                            <div class="col-md-3">
                                <small class="text-muted">信号时间: ${signalTime}</small>
                                ${signal.execution_time ? `<br><small class="text-muted">执行时间: ${executionTime}</small>` : ''}
                            </div>
                        </div>
                    </div>
                `;
            }).join('');

            container.innerHTML = signalsHtml;
        }

        // ==================== 回测功能 ====================

        // 回测状态
        let backtestState = {
            isRunning: false,
            results: null
        };

        // 运行回测
        async function runBacktest() {
            if (backtestState.isRunning) {
                showInfo('回测正在进行中，请稍候...');
                return;
            }

            try {
                const period = parseInt(document.getElementById('backtestPeriod').value);
                const backtestBtn = document.getElementById('backtestBtn');
                const resultsDiv = document.getElementById('backtestResults');

                // 预检查MT5连接状态
                console.log('🔍 检查MT5连接状态...');
                try {
                    const connectionCheck = await fetch('/api/mt5/connection-status');
                    const connectionData = await connectionCheck.json();

                    if (!connectionData.connected) {
                        throw new Error('MT5未连接。回测需要真实的历史数据，请确保MT5终端正在运行并已登录。');
                    }

                    console.log('✅ MT5连接正常，开始回测');
                } catch (connectionError) {
                    console.error('❌ MT5连接检查失败:', connectionError);
                    showError('MT5连接检查失败: ' + connectionError.message);
                    return;
                }

                // 设置加载状态
                backtestState.isRunning = true;
                backtestBtn.disabled = true;
                backtestBtn.classList.add('backtest-loading');
                backtestBtn.innerHTML = '<i class="fas fa-spinner fa-spin me-2"></i>回测中...';

                console.log(`🔄 开始回测，时间段: ${period}天`);

                // 隐藏之前的结果
                resultsDiv.style.display = 'none';

                // 模拟回测过程（实际应该调用后端API）
                const results = await simulateBacktest(period);

                // 显示结果
                displayBacktestResults(results);
                resultsDiv.style.display = 'block';

                showSuccess(`回测完成！时间段: ${period}天`);

            } catch (error) {
                console.error('❌ 回测失败:', error);
                showError('回测失败: ' + error.message);
            } finally {
                // 恢复按钮状态
                backtestState.isRunning = false;
                const backtestBtn = document.getElementById('backtestBtn');
                backtestBtn.disabled = false;
                backtestBtn.classList.remove('backtest-loading');
                backtestBtn.innerHTML = '<i class="fas fa-play me-2"></i>开始回测';
            }
        }

        // 执行回测（调用后端API）
        async function simulateBacktest(days) {
            try {
                const timeframe = document.getElementById('backtestTimeframe').value;
                const currentPreset = document.getElementById('strategyPreset')?.value || 'optimized';

                console.log(`📡 调用MT5时间序列回测API，时间段: ${days}天，间隔: ${timeframe}，策略: ${currentPreset}`);

                const response = await fetch('/api/low-risk-trading/backtest', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({
                        days: days,
                        symbol: 'XAUUSD',
                        timeframe: timeframe,
                        strategy_preset: currentPreset,  // 添加策略预设参数
                        lot_size: lowRiskTradingConfig.lotSize,
                        stop_loss_percent: lowRiskTradingConfig.stopLossPercent,
                        take_profit_percent: lowRiskTradingConfig.takeProfitPercent,
                        daily_limit: lowRiskTradingConfig.dailyLimit,
                        min_signals: lowRiskTradingConfig.minSignals,
                        // 添加单边行情检测配置
                        trend_detection_enabled: trendDetectionConfig.enabled,
                        trend_strength_threshold: trendDetectionConfig.trendStrengthThreshold,
                        volatility_breakout_multiplier: trendDetectionConfig.volatilityBreakoutMultiplier,
                        trend_confirmation_time: trendDetectionConfig.trendConfirmationTime,
                        multi_timeframe_confirm: trendDetectionConfig.multiTimeframeConfirm,
                        // 添加交易时间配置
                        trading_hours_start: lowRiskTradingConfig.tradingHours.start,
                        trading_hours_end: lowRiskTradingConfig.tradingHours.end
                    })
                });

                if (!response.ok) {
                    throw new Error(`HTTP ${response.status}: ${response.statusText}`);
                }

                const data = await response.json();

                if (!data.success) {
                    throw new Error(data.error || '回测失败');
                }

                // 转换API返回的数据格式为前端需要的格式
                const apiResults = data.results;
                const results = {
                    period: apiResults.period_days,
                    totalTrades: apiResults.summary.total_trades,
                    winTrades: apiResults.summary.win_trades,
                    lossTrades: apiResults.summary.loss_trades,
                    winRate: apiResults.summary.win_rate,
                    totalPnL: apiResults.summary.total_pnl,
                    trades: apiResults.trades,
                    config: {
                        lotSize: apiResults.config.lot_size,
                        stopLoss: apiResults.config.stop_loss_percent,
                        takeProfit: apiResults.config.take_profit_percent
                    },
                    advanced: {
                        maxDrawdown: apiResults.summary.max_drawdown,
                        profitLossRatio: apiResults.summary.profit_loss_ratio,
                        sharpeRatio: apiResults.summary.sharpe_ratio,
                        avgPnLPerTrade: apiResults.summary.avg_pnl_per_trade
                    }
                };

                console.log('📊 回测结果:', results);
                backtestState.results = results;
                return results;

            } catch (error) {
                console.error('❌ 回测API调用失败:', error);

                // 不再使用本地模拟数据作为备用，直接抛出错误
                // 确保用户知道需要MT5真实数据
                if (error.message.includes('MT5') || error.message.includes('连接')) {
                    throw new Error('MT5连接失败，回测需要真实的历史数据。请确保MT5终端正在运行并已登录。');
                } else {
                    throw new Error(`回测失败: ${error.message}`);
                }
            }
        }

        // 本地模拟回测（备用方案）
        async function simulateBacktestLocal(days) {
            // 模拟网络延迟
            await new Promise(resolve => setTimeout(resolve, 1000));

            // 根据天数生成不同的回测结果
            const baseTradesPerDay = 1.2; // 平均每天1.2笔交易
            const totalTrades = Math.floor(days * baseTradesPerDay + Math.random() * days * 0.5);

            // 模拟交易结果
            const trades = [];
            let totalPnL = 0;
            let winTrades = 0;
            let lossTrades = 0;

            // 系统严禁生成模拟交易结果，必须使用真实的交易历史记录
            console.error('❌ 系统严禁生成模拟交易结果，请使用真实的MT5交易历史');

            // 返回空交易数组，强制使用真实交易数据
            return [];

            const winRate = totalTrades > 0 ? (winTrades / totalTrades * 100) : 0;

            const results = {
                period: days,
                totalTrades: totalTrades,
                winTrades: winTrades,
                lossTrades: lossTrades,
                winRate: winRate,
                totalPnL: totalPnL,
                trades: trades,
                config: {
                    lotSize: lowRiskTradingConfig.lotSize,
                    stopLoss: lowRiskTradingConfig.stopLossPercent,
                    takeProfit: lowRiskTradingConfig.takeProfitPercent
                }
            };

            console.log('📊 本地模拟回测结果:', results);
            backtestState.results = results;
            return results;
        }

        // 计算买涨/买跌方向统计
        function calculateDirectionStats(trades) {
            let buyWins = 0, buyLosses = 0, sellWins = 0, sellLosses = 0;

            trades.forEach(trade => {
                const isBuy = trade.direction === 'buy' || trade.type === 'buy' || trade.action === 'buy';
                const isProfit = trade.pnl > 0 || trade.profit > 0;

                if (isBuy) {
                    if (isProfit) {
                        buyWins++;
                    } else {
                        buyLosses++;
                    }
                } else {
                    if (isProfit) {
                        sellWins++;
                    } else {
                        sellLosses++;
                    }
                }
            });

            const totalTrades = trades.length;
            const totalBuyTrades = buyWins + buyLosses;
            const totalSellTrades = sellWins + sellLosses;

            return {
                buyWins,
                buyLosses,
                sellWins,
                sellLosses,
                buyWinRate: totalBuyTrades > 0 ? (buyWins / totalBuyTrades) * 100 : 0,
                buyLossRate: totalBuyTrades > 0 ? (buyLosses / totalBuyTrades) * 100 : 0,
                sellWinRate: totalSellTrades > 0 ? (sellWins / totalSellTrades) * 100 : 0,
                sellLossRate: totalSellTrades > 0 ? (sellLosses / totalSellTrades) * 100 : 0,
                totalBuyTrades,
                totalSellTrades
            };
        }

        // 显示回测结果
        function displayBacktestResults(results) {
            document.getElementById('backtestTotalTrades').textContent = results.totalTrades;
            document.getElementById('backtestWinTrades').textContent = results.winTrades;
            document.getElementById('backtestLossTrades').textContent = results.lossTrades;
            document.getElementById('backtestWinRate').textContent = results.winRate.toFixed(1) + '%';

            // 格式化总盈亏显示
            const totalPnLElement = document.getElementById('backtestTotalPnL');
            const pnlText = results.totalPnL >= 0 ?
                `+$${results.totalPnL.toFixed(2)}` :
                `-$${Math.abs(results.totalPnL).toFixed(2)}`;

            totalPnLElement.textContent = pnlText;
            totalPnLElement.className = results.totalPnL >= 0 ? 'fw-bold text-success' : 'fw-bold text-danger';

            // 更新父容器的背景色
            const pnlContainer = totalPnLElement.parentElement;
            if (results.totalPnL >= 0) {
                pnlContainer.className = 'text-center p-2 bg-success bg-opacity-10 rounded mb-2';
                pnlContainer.querySelector('.small').className = 'small text-success';
            } else {
                pnlContainer.className = 'text-center p-2 bg-danger bg-opacity-10 rounded mb-2';
                pnlContainer.querySelector('.small').className = 'small text-danger';
            }

            // 计算买涨/买跌占比统计
            const directionStats = calculateDirectionStats(results.trades || []);

            // 更新买涨/买跌统计显示
            document.getElementById('backtestBuyWins').textContent =
                `${directionStats.buyWins} (${directionStats.buyWinRate.toFixed(1)}%)`;
            document.getElementById('backtestBuyLosses').textContent =
                `${directionStats.buyLosses} (${directionStats.buyLossRate.toFixed(1)}%)`;
            document.getElementById('backtestSellWins').textContent =
                `${directionStats.sellWins} (${directionStats.sellWinRate.toFixed(1)}%)`;
            document.getElementById('backtestSellLosses').textContent =
                `${directionStats.sellLosses} (${directionStats.sellLossRate.toFixed(1)}%)`;

            console.log('✅ 回测结果已显示，包含方向统计:', directionStats);
        }

        // 获取回测详细报告
        function getBacktestReport() {
            if (!backtestState.results) {
                showInfo('请先运行回测');
                return;
            }

            const results = backtestState.results;
            const report = `
回测报告 - 低风险交易策略
================================
回测时间段: ${results.period}天
总交易次数: ${results.totalTrades}
盈利次数: ${results.winTrades}
亏损次数: ${results.lossTrades}
胜率: ${results.winRate.toFixed(2)}%
总盈亏: $${results.totalPnL.toFixed(2)}
平均每笔盈亏: $${(results.totalPnL / results.totalTrades).toFixed(2)}

策略参数:
- 交易手数: ${results.config.lotSize}
- 止损百分比: ${results.config.stopLoss}%
- 止盈百分比: ${results.config.takeProfit}%

风险评估:
- 最大回撤: 待计算
- 夏普比率: 待计算
- 盈亏比: ${results.winTrades > 0 && results.lossTrades > 0 ?
    ((results.totalPnL / results.winTrades) / Math.abs(results.totalPnL / results.lossTrades)).toFixed(2) : 'N/A'}
            `;

            console.log(report);
            alert(report);
        }

        // ==================== 价格获取功能 ====================

        // 获取当前真实价格
        async function getCurrentRealPrice() {
            try {
                console.log('📊 获取当前真实价格...');

                const response = await fetch('/api/market-price/XAUUSD', {
                    method: 'GET',
                    headers: {
                        'Content-Type': 'application/json'
                    }
                });

                if (!response.ok) {
                    throw new Error(`HTTP ${response.status}: ${response.statusText}`);
                }

                const data = await response.json();

                if (data.success && data.price) {
                    console.log(`✅ 获取到真实价格: ${data.price}`);
                    return data.price;
                } else {
                    console.warn('⚠️ 无法获取真实价格，使用默认价格');
                    return getDefaultPrice();
                }

            } catch (error) {
                console.error('❌ 获取价格失败:', error);
                console.log('🔄 使用默认价格作为备用');
                return getDefaultPrice();
            }
        }

        // 系统严禁生成默认价格，必须从MT5获取真实价格
        function getDefaultPrice() {
            console.error('❌ 系统严禁生成默认价格，请从MT5获取真实市场价格');
            return null; // 返回null，强制使用真实价格数据
        }

        // 消息提示函数
        function showSuccess(message, isAutoTrade = false) {
            console.log('✅ ' + message);

            if (isAutoTrade) {
                // 自动交易使用页面提示
                showNotification(message, 'success');
            } else {
                // 手动交易使用弹出对话框
                alert('成功: ' + message);
            }
        }

        function showError(message) {
            console.error('❌ ' + message);
            // 这里可以添加更好的UI提示
            alert('错误: ' + message);
        }

        function showInfo(message) {
            console.log('ℹ️ ' + message);
            // 这里可以添加更好的UI提示
            alert('信息: ' + message);
        }
        // ==================== 自动交易功能 ====================

        // 启动自动交易
        function startAutoTrading() {
            if (autoTradingState.isRunning) {
                console.log('⚠️ 自动交易已在运行中');
                return;
            }

            console.log('🤖 启动自动交易...');

            autoTradingState.isRunning = true;
            autoTradingState.startTime = new Date();
            autoTradingState.tradeCount = 0;

            // 更新UI
            document.getElementById('startAutoTradingBtn').disabled = true;
            document.getElementById('stopAutoTradingBtn').disabled = false;
            document.getElementById('autoTradingStatus').style.display = 'block';
            document.getElementById('autoTradingStatusText').textContent = '运行中';

            // 启动自动交易循环 (根据配置的时间框架调整检查间隔)
            const timeframe = document.getElementById('autoTradingTimeframe').value;
            const intervalMs = getTimeframeInterval(timeframe);
            autoTradingState.intervalId = setInterval(executeAutoTradingCycle, intervalMs);

            // 启动运行时间计时器
            autoTradingState.runTimeIntervalId = setInterval(updateAutoTradingRunTime, 1000);

            // 保存状态到localStorage
            saveAutoTradingState();

            // 立即执行一次检查
            executeAutoTradingCycle();

            console.log('✅ 自动交易已启动');
            const timeframeName = {
                '1m': '1分钟',
                '2m': '2分钟',
                '5m': '5分钟',
                '15m': '15分钟',
                '30m': '30分钟',
                '1h': '1小时',
                '4h': '4小时'
            }[timeframe] || '1小时';
            showNotification(`自动交易已启动，系统将根据${timeframeName}间隔检查信号并自动执行交易`, 'success');
        }

        // 停止自动交易
        function stopAutoTrading() {
            if (!autoTradingState.isRunning) {
                console.log('⚠️ 自动交易未在运行');
                return;
            }

            console.log('🛑 停止自动交易...');

            autoTradingState.isRunning = false;

            // 清除定时器
            if (autoTradingState.intervalId) {
                clearInterval(autoTradingState.intervalId);
                autoTradingState.intervalId = null;
            }

            if (autoTradingState.runTimeIntervalId) {
                clearInterval(autoTradingState.runTimeIntervalId);
                autoTradingState.runTimeIntervalId = null;
            }

            // 更新UI
            document.getElementById('startAutoTradingBtn').disabled = false;
            document.getElementById('stopAutoTradingBtn').disabled = true;
            document.getElementById('autoTradingStatusText').textContent = '已停止';

            // 清除保存的状态
            localStorage.removeItem('autoTradingState');

            console.log('✅ 自动交易已停止');
            showNotification('自动交易已停止', 'info');
        }

        // 执行自动交易周期 (集成低风险交易逻辑)
        async function executeAutoTradingCycle() {
            try {
                console.log('🔄 执行自动交易周期检查...');

                // 检查自动交易是否启用
                if (!lowRiskTradingConfig.autoTrade) {
                    console.log('⚠️ 自动交易未启用，跳过执行');
                    return;
                }

                // 检查交易时间
                if (!isWithinTradingHours()) {
                    console.log('⏰ 当前不在交易时间内');
                    return;
                }

                // 检查每日交易限制
                if (lowRiskTradingState.todayTradeCount >= lowRiskTradingConfig.dailyLimit) {
                    console.log('📊 已达到每日交易限制');
                    return;
                }

                // 执行市场分析
                await performLowRiskMarketAnalysis();

                // 检查低风险交易机会 (这会自动保存入场信号并执行交易)
                await checkLowRiskTradingOpportunities();

                // 更新自动交易状态显示
                const currentTime = new Date();
                autoTradingState.lastSignalTime = currentTime;

                // 更新交易计数显示 (使用低风险交易系统的计数)
                document.getElementById('autoTradingCount').textContent = lowRiskTradingState.todayTradeCount;

                // 更新信号强度显示 (基于最新的市场分析)
                const analysis = lowRiskTradingState.marketAnalysis;
                if (analysis && analysis.trendDirection) {
                    const signalStrength = analysis.trendDirection.strength / 100; // 转换为0-1范围
                    autoTradingState.currentSignalStrength = signalStrength;
                    document.getElementById('currentSignalStrength').textContent = signalStrength.toFixed(3);
                } else {
                    document.getElementById('currentSignalStrength').textContent = '--';
                }

                // 保存更新后的状态
                saveAutoTradingState();

                console.log('✅ 自动交易周期检查完成');

            } catch (error) {
                console.error('❌ 自动交易周期执行失败:', error);
            }
        }

        // 执行自动交易
        async function executeAutoTrade(signal) {
            try {
                console.log('🤖 执行自动交易:', signal);

                const tradeData = {
                    symbol: currentSymbol,
                    action: signal.type, // 'buy' or 'sell'
                    volume: lowRiskTradingConfig.lotSize,
                    stop_loss_percent: lowRiskTradingConfig.stopLossPercent,
                    take_profit_percent: lowRiskTradingConfig.takeProfitPercent,
                    comment: `Auto_${signal.type.toUpperCase()}_${Math.round(signal.strength * 1000)}`,
                    signal_strength: signal.strength
                };

                const response = await fetch('/api/low-risk-trading/execute-trade', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify(tradeData)
                });

                const result = await response.json();

                if (result.success) {
                    console.log('✅ 自动交易订单已发送:', result);

                    // 更新本地状态
                    lowRiskTradingState.todayTradeCount++;

                    // 刷新持仓显示
                    await loadLowRiskPositions();

                    showNotification(`自动交易执行成功: ${signal.type.toUpperCase()} ${currentSymbol}`, 'success');
                    return true;
                } else {
                    console.error('❌ 自动交易执行失败:', result.error);
                    showNotification(`自动交易执行失败: ${result.error}`, 'error');
                    return false;
                }

            } catch (error) {
                console.error('❌ 自动交易请求失败:', error);
                showNotification('自动交易请求失败', 'error');
                return false;
            }
        }

        // 获取当前交易信号
        async function getCurrentTradingSignal() {
            try {
                const response = await fetch('/api/low-risk-trading/get-signal', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({
                        symbol: currentSymbol,
                        timeframe: document.getElementById('autoTradingTimeframe').value, // 使用配置的时间框架
                        config: lowRiskTradingConfig
                    })
                });

                const result = await response.json();

                if (result.success && result.signal) {
                    return result.signal;
                } else {
                    return null;
                }

            } catch (error) {
                console.error('❌ 获取交易信号失败:', error);
                return null;
            }
        }

        // 更新自动交易运行时间
        function updateAutoTradingRunTime() {
            if (!autoTradingState.startTime) return;

            const now = new Date();
            const runTime = now - autoTradingState.startTime;

            const hours = Math.floor(runTime / (1000 * 60 * 60));
            const minutes = Math.floor((runTime % (1000 * 60 * 60)) / (1000 * 60));
            const seconds = Math.floor((runTime % (1000 * 60)) / 1000);

            const timeString = `${hours.toString().padStart(2, '0')}:${minutes.toString().padStart(2, '0')}:${seconds.toString().padStart(2, '0')}`;
            document.getElementById('autoTradingRunTime').textContent = timeString;
        }

        // 注意: isWithinTradingHours函数已在上面定义，此处删除重复定义

        // ==================== 测试下单功能 ====================

        // 测试下单功能
        async function testOrder() {
            try {
                console.log('🧪 执行测试下单...');

                const testData = {
                    symbol: currentSymbol,
                    action: 'buy', // 测试买单
                    volume: 0.01,  // 最小手数
                    stop_loss_percent: 1.0,
                    take_profit_percent: 2.0,
                    comment: 'TEST_ORDER',
                    test_mode: true // 标记为测试模式
                };

                const response = await fetch('/api/low-risk-trading/test-order', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify(testData)
                });

                const result = await response.json();

                if (result.success) {
                    console.log('✅ 测试下单成功:', result);
                    showNotification(`测试下单成功: ${result.message}`, 'success');

                    // 显示测试结果
                    const resultHtml = `
                        <div class="alert alert-success mt-3">
                            <h6><i class="fas fa-check-circle"></i> 测试下单结果</h6>
                            <p><strong>订单号:</strong> ${result.order_id || 'TEST_' + Date.now()}</p>
                            <p><strong>品种:</strong> ${testData.symbol}</p>
                            <p><strong>方向:</strong> ${testData.action.toUpperCase()}</p>
                            <p><strong>手数:</strong> ${testData.volume}</p>
                            <p><strong>状态:</strong> ${result.status || '测试成功'}</p>
                        </div>
                    `;

                    // 可以在页面上显示结果
                    const statusDiv = document.getElementById('autoTradingStatus');
                    statusDiv.insertAdjacentHTML('afterend', resultHtml);

                    // 3秒后移除结果显示
                    setTimeout(() => {
                        const alertDiv = statusDiv.nextElementSibling;
                        if (alertDiv && alertDiv.classList.contains('alert-success')) {
                            alertDiv.remove();
                        }
                    }, 5000);

                } else {
                    console.error('❌ 测试下单失败:', result.error);
                    showNotification(`测试下单失败: ${result.error}`, 'error');
                }

            } catch (error) {
                console.error('❌ 测试下单请求失败:', error);
                showNotification('测试下单请求失败: ' + error.message, 'error');
            }
        }

        // ==================== 持仓显示增强 ====================

        // 增强的持仓加载函数
        async function loadLowRiskPositions() {
            try {
                // 更新刷新状态
                positionRefreshState.lastRefreshTime = new Date();
                positionRefreshState.refreshCount++;

                // 显示刷新状态（可选）
                console.log(`🔄 刷新持仓数据 (第${positionRefreshState.refreshCount}次) - ${positionRefreshState.lastRefreshTime.toLocaleTimeString()}`);

                const response = await fetch('/api/low-risk-trading/positions');
                const data = await response.json();

                const container = document.getElementById('lowRiskPositions');

                if (data.success && data.positions && data.positions.length > 0) {
                    const positionsHtml = data.positions.map(position => {
                        const pnlClass = position.profit >= 0 ? 'text-success' : 'text-danger';
                        const pnlIcon = position.profit >= 0 ? 'fa-arrow-up' : 'fa-arrow-down';

                        // 计算持仓时间（修复时区问题）
                        let openTime;
                        try {
                            // 处理不同的时间格式
                            if (position.open_time.includes('T') && !position.open_time.includes('Z') && !position.open_time.includes('+')) {
                                // 如果是 ISO 格式但没有时区信息，假设为本地时间
                                openTime = new Date(position.open_time + '+08:00'); // 明确指定为北京时间
                            } else {
                                openTime = new Date(position.open_time);
                            }
                        } catch (e) {
                            console.error('时间解析错误:', e);
                            openTime = new Date(); // 使用当前时间作为备用
                        }

                        const now = new Date();
                        let duration = Math.floor((now - openTime) / (1000 * 60)); // 分钟

                        // 调试信息
                        console.log(`🕐 时间计算调试: 开仓时间=${position.open_time}, 解析后=${openTime.toISOString()}, 当前时间=${now.toISOString()}, 持仓时间=${duration}分钟`);

                        // 如果计算出负数，可能是时区问题
                        if (duration < 0) {
                            console.warn(`⚠️ 持仓时间为负数: ${duration}分钟，使用绝对值`);
                            duration = Math.abs(duration);
                        }

                        // 如果仍然异常，设为0
                        if (isNaN(duration) || duration < 0) {
                            duration = 0;
                        }

                        // 转换为北京时间显示
                        const beijingTime = openTime.toLocaleString('zh-CN', {
                            timeZone: 'Asia/Shanghai',
                            hour12: false,
                            hour: '2-digit',
                            minute: '2-digit',
                            second: '2-digit'
                        });

                        return `
                            <div class="card mb-2" style="background: linear-gradient(135deg, #e0f7fa 0%, #b2ebf2 100%); border-left: 4px solid ${position.type === 'buy' ? '#28a745' : '#dc3545'};">
                                <div class="card-body p-3">
                                    <div class="row align-items-center">
                                        <div class="col-md-3">
                                            <h6 class="mb-1">
                                                <span class="badge ${position.type === 'buy' ? 'bg-success' : 'bg-danger'}">
                                                    ${position.type.toUpperCase()}
                                                </span>
                                                ${position.symbol}
                                                <span class="badge ${position.operation_type === 'auto' ? 'bg-primary' : 'bg-secondary'} ms-1" style="font-size: 0.7em;">
                                                    ${position.operation_type === 'auto' ? '🤖自动' : '👤手动'}
                                                </span>
                                            </h6>
                                            <small class="text-muted">手数: ${position.volume}</small>
                                        </div>
                                        <div class="col-md-3">
                                            <small class="text-muted d-block">开仓价</small>
                                            <strong>${position.open_price}</strong>
                                            <br>
                                            <small class="text-muted">当前价: ${position.current_price || '--'}</small>
                                        </div>
                                        <div class="col-md-2">
                                            <small class="text-muted d-block">止损</small>
                                            <span class="text-danger">${position.stop_loss || '--'}</span>
                                            <br>
                                            <small class="text-muted d-block">止盈</small>
                                            <span class="text-success">${position.take_profit || '--'}</span>
                                        </div>
                                        <div class="col-md-2">
                                            <small class="text-muted d-block">持仓时间</small>
                                            <strong>${duration}分钟</strong>
                                            <br>
                                            <small class="text-muted">${beijingTime}</small>
                                        </div>
                                        <div class="col-md-2 text-end">
                                            <h6 class="mb-1 ${pnlClass}">
                                                <i class="fas ${pnlIcon}"></i>
                                                ${position.profit >= 0 ? '+' : ''}$${(position.profit || 0).toFixed(2)}
                                            </h6>
                                            <button class="btn btn-sm btn-outline-danger" onclick="closeLowRiskPosition('${position.id}')">
                                                <i class="fas fa-times"></i>
                                            </button>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        `;
                    }).join('');

                    container.innerHTML = positionsHtml;
                } else {
                    container.innerHTML = `
                        <div class="text-center py-4">
                            <i class="fas fa-inbox fa-2x text-muted mb-3"></i>
                            <p class="text-muted">暂无持仓</p>
                        </div>
                    `;
                }

                // 更新刷新状态显示
                updatePositionRefreshStatus();

            } catch (error) {
                console.error('❌ 加载持仓失败:', error);
            }
        }

        // 更新持仓刷新状态显示
        function updatePositionRefreshStatus() {
            const statusElement = document.getElementById('positionRefreshStatus');
            if (statusElement && positionRefreshState.lastRefreshTime) {
                // 转换为北京时间显示
                const lastRefresh = positionRefreshState.lastRefreshTime.toLocaleString('zh-CN', {
                    timeZone: 'Asia/Shanghai',
                    hour12: false,
                    hour: '2-digit',
                    minute: '2-digit',
                    second: '2-digit'
                });
                statusElement.innerHTML = `
                    <i class="fas fa-sync-alt me-1"></i>
                    每10秒刷新 | 最后更新: ${lastRefresh}
                `;
            }
        }

        // 单边行情检测配置控制
        function toggleTrendDetectionDetails() {
            const checkbox = document.getElementById('lowRiskTrendDetection');
            const details = document.getElementById('trendDetectionDetails');

            if (checkbox && details) {
                if (checkbox.checked) {
                    details.style.display = 'block';
                    trendDetectionConfig.enabled = true;
                } else {
                    details.style.display = 'none';
                    trendDetectionConfig.enabled = false;
                }
                saveTrendDetectionConfig();
            }
        }

        // 设置单边行情检测预设配置
        function setTrendDetectionPreset(preset) {
            let config;

            switch (preset) {
                case 'conservative':
                    config = {
                        trendStrengthThreshold: 70,
                        volatilityBreakoutMultiplier: 1.8,
                        trendConfirmationTime: 60,
                        multiTimeframeConfirm: true
                    };
                    break;
                case 'balanced':
                    config = {
                        trendStrengthThreshold: 60,
                        volatilityBreakoutMultiplier: 1.5,
                        trendConfirmationTime: 30,
                        multiTimeframeConfirm: true
                    };
                    break;
                case 'aggressive':
                    config = {
                        trendStrengthThreshold: 50,
                        volatilityBreakoutMultiplier: 1.3,
                        trendConfirmationTime: 15,
                        multiTimeframeConfirm: true
                    };
                    break;
                default:
                    return;
            }

            // 更新界面
            document.getElementById('trendStrengthThreshold').value = config.trendStrengthThreshold;
            document.getElementById('volatilityBreakoutMultiplier').value = config.volatilityBreakoutMultiplier;
            document.getElementById('trendConfirmationTime').value = config.trendConfirmationTime;
            document.getElementById('multiTimeframeConfirm').checked = config.multiTimeframeConfirm;

            // 更新配置
            Object.assign(trendDetectionConfig, config);
            saveTrendDetectionConfig();

            // 显示提示
            const presetNames = {
                'conservative': '保守型',
                'balanced': '平衡型',
                'aggressive': '激进型'
            };

            showSuccess(`已应用${presetNames[preset]}配置`);
        }

        // 保存单边行情检测配置
        function saveTrendDetectionConfig() {
            // 从界面获取当前值
            const enabled = document.getElementById('lowRiskTrendDetection')?.checked || false;
            const trendStrengthThreshold = parseFloat(document.getElementById('trendStrengthThreshold')?.value || 60);
            const volatilityBreakoutMultiplier = parseFloat(document.getElementById('volatilityBreakoutMultiplier')?.value || 1.5);
            const trendConfirmationTime = parseInt(document.getElementById('trendConfirmationTime')?.value || 30);
            const multiTimeframeConfirm = document.getElementById('multiTimeframeConfirm')?.checked || true;

            trendDetectionConfig = {
                enabled,
                trendStrengthThreshold,
                volatilityBreakoutMultiplier,
                trendConfirmationTime,
                multiTimeframeConfirm
            };

            // 保存到localStorage
            localStorage.setItem('lowRiskTrendDetectionConfig', JSON.stringify(trendDetectionConfig));
            console.log('✅ 单边行情检测配置已保存:', trendDetectionConfig);
        }

        // 加载单边行情检测配置
        function loadTrendDetectionConfig() {
            try {
                const saved = localStorage.getItem('lowRiskTrendDetectionConfig');
                if (saved) {
                    const config = JSON.parse(saved);

                    // 更新界面
                    if (document.getElementById('lowRiskTrendDetection')) {
                        document.getElementById('lowRiskTrendDetection').checked = config.enabled !== false;
                    }
                    if (document.getElementById('trendStrengthThreshold')) {
                        document.getElementById('trendStrengthThreshold').value = config.trendStrengthThreshold || 60;
                    }
                    if (document.getElementById('volatilityBreakoutMultiplier')) {
                        document.getElementById('volatilityBreakoutMultiplier').value = config.volatilityBreakoutMultiplier || 1.5;
                    }
                    if (document.getElementById('trendConfirmationTime')) {
                        document.getElementById('trendConfirmationTime').value = config.trendConfirmationTime || 30;
                    }
                    if (document.getElementById('multiTimeframeConfirm')) {
                        document.getElementById('multiTimeframeConfirm').checked = config.multiTimeframeConfirm !== false;
                    }

                    // 更新配置对象
                    trendDetectionConfig = { ...config };

                    // 更新显示状态
                    toggleTrendDetectionDetails();

                    console.log('✅ 单边行情检测配置已加载:', trendDetectionConfig);
                }
            } catch (error) {
                console.error('❌ 加载单边行情检测配置失败:', error);
            }
        }

        // 应用策略预设
        function applyStrategyPreset(presetName, showNotification = true) {
            // 控制优化策略详细说明的显示
            const optimizedDetailsElement = document.getElementById('optimizedStrategyDetails');
            if (optimizedDetailsElement) {
                if (presetName === 'optimized') {
                    optimizedDetailsElement.style.display = 'block';
                } else {
                    optimizedDetailsElement.style.display = 'none';
                }
            }

            // 控制易触发策略详细说明的显示
            const easyTriggerDetailsElement = document.getElementById('easyTriggerStrategyDetails');
            if (easyTriggerDetailsElement) {
                if (presetName === 'easyTrigger') {
                    easyTriggerDetailsElement.style.display = 'block';
                } else {
                    easyTriggerDetailsElement.style.display = 'none';
                }
            }

            if (presetName === 'custom') {
                showInfo('已切换到自定义配置模式');
                return;
            }

            const preset = strategyPresets[presetName];
            if (!preset) {
                showError('未找到指定的策略预设');
                return;
            }

            // 应用基础配置，但保留用户的autoTrade设置
            const userAutoTrade = lowRiskTradingConfig.autoTrade;
            Object.assign(lowRiskTradingConfig, preset.config);
            lowRiskTradingConfig.autoTrade = userAutoTrade; // 恢复用户设置
            console.log('🔧 应用预设后保留autoTrade设置:', userAutoTrade);

            // 应用趋势检测配置
            Object.assign(trendDetectionConfig, preset.trendConfig);

            // 易触发策略自动设置短时间间隔
            if (presetName === 'easyTrigger') {
                const timeframeSelect = document.getElementById('autoTradingTimeframe');
                if (timeframeSelect) {
                    timeframeSelect.value = '2m'; // 设置为2分钟间隔
                    console.log('🧪 易触发策略: 自动设置时间间隔为2分钟');
                }
            }

            // 更新UI界面
            updateConfigUI();

            // 保存配置
            saveTrendDetectionConfig();

            // 显示策略信息
            if (showNotification) {
                showStrategyInfo(preset);
            }

            // 更新回测策略显示
            updateBacktestStrategyDisplay(presetName);

            console.log(`✅ 已切换到${preset.name}`, preset);
        }

        // 更新配置UI界面
        function updateConfigUI() {
            // 更新基础配置
            if (document.getElementById('lowRiskDailyLimit')) {
                document.getElementById('lowRiskDailyLimit').value = lowRiskTradingConfig.dailyLimit;
            }
            if (document.getElementById('lowRiskLotSize')) {
                document.getElementById('lowRiskLotSize').value = lowRiskTradingConfig.lotSize;
            }
            if (document.getElementById('lowRiskStopLoss')) {
                document.getElementById('lowRiskStopLoss').value = lowRiskTradingConfig.stopLossPercent;
                document.getElementById('lowRiskStopLossValue').textContent = lowRiskTradingConfig.stopLossPercent + '%';
            }
            if (document.getElementById('lowRiskTakeProfit')) {
                document.getElementById('lowRiskTakeProfit').value = lowRiskTradingConfig.takeProfitPercent;
                document.getElementById('lowRiskTakeProfitValue').textContent = lowRiskTradingConfig.takeProfitPercent + '%';
            }
            if (document.getElementById('lowRiskAutoTrade')) {
                document.getElementById('lowRiskAutoTrade').checked = lowRiskTradingConfig.autoTrade;
            }

            // 更新趋势检测配置
            if (document.getElementById('lowRiskTrendDetection')) {
                document.getElementById('lowRiskTrendDetection').checked = trendDetectionConfig.enabled;
            }
            if (document.getElementById('trendStrengthThreshold')) {
                document.getElementById('trendStrengthThreshold').value = trendDetectionConfig.trendStrengthThreshold;
            }
            if (document.getElementById('volatilityBreakoutMultiplier')) {
                document.getElementById('volatilityBreakoutMultiplier').value = trendDetectionConfig.volatilityBreakoutMultiplier;
            }
            if (document.getElementById('trendConfirmationTime')) {
                document.getElementById('trendConfirmationTime').value = trendDetectionConfig.trendConfirmationTime;
            }
            if (document.getElementById('multiTimeframeConfirm')) {
                document.getElementById('multiTimeframeConfirm').checked = trendDetectionConfig.multiTimeframeConfirm;
            }

            // 更新趋势检测显示状态
            toggleTrendDetectionDetails();
        }

        // 显示策略信息
        function showStrategyInfo(preset) {
            const message = `
                <div class="strategy-info">
                    <h6><i class="fas fa-star text-warning"></i> ${preset.name}</h6>
                    <p class="mb-2">${preset.description}</p>
                    <div class="row">
                        <div class="col-6">
                            <small><strong>每日限制:</strong> ${preset.config.dailyLimit}笔</small><br>
                            <small><strong>止损:</strong> ${preset.config.stopLossPercent}%</small><br>
                            <small><strong>止盈:</strong> ${preset.config.takeProfitPercent}%</small>
                        </div>
                        <div class="col-6">
                            <small><strong>最小信号:</strong> ${preset.config.minSignals}个</small><br>
                            <small><strong>趋势检测:</strong> ${preset.trendConfig.enabled ? '启用' : '禁用'}</small><br>
                            <small><strong>趋势阈值:</strong> ${preset.trendConfig.trendStrengthThreshold}%</small>
                        </div>
                    </div>
                </div>
            `;

            showSuccess(`已应用${preset.name}`, message, 8000);
        }

        // 关闭指定持仓
        async function closeLowRiskPosition(positionId) {
            try {
                const response = await fetch('/api/low-risk-trading/close-position', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({ position_id: positionId })
                });

                const result = await response.json();

                if (result.success) {
                    showNotification('持仓已关闭', 'success');
                    await loadLowRiskPositions(); // 刷新持仓显示
                } else {
                    showNotification('关闭持仓失败: ' + result.error, 'error');
                }

            } catch (error) {
                console.error('❌ 关闭持仓失败:', error);
                showNotification('关闭持仓失败', 'error');
            }
        }

        // 显示通知
        function showNotification(message, type = 'info') {
            // 创建通知元素
            const notification = document.createElement('div');
            notification.className = `alert alert-${type === 'error' ? 'danger' : type} alert-dismissible fade show position-fixed`;
            notification.style.cssText = 'top: 20px; right: 20px; z-index: 9999; min-width: 300px;';
            notification.innerHTML = `
                ${message}
                <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
            `;

            document.body.appendChild(notification);

            // 3秒后自动移除
            setTimeout(() => {
                if (notification.parentNode) {
                    notification.remove();
                }
            }, 3000);
        }

        // 更新策略显示（回测和交易控制区域）
        function updateBacktestStrategyDisplay(presetName = null) {
            // 如果没有传入presetName，从选择器获取
            if (!presetName) {
                const strategySelect = document.getElementById('strategyPreset');
                presetName = strategySelect ? strategySelect.value : 'optimized';
            }

            // 回测区域的元素
            const strategyNameElement = document.getElementById('backtestStrategyName');
            const strategyDetailsElement = document.getElementById('backtestStrategyDetails');
            const strategyStatusElement = document.getElementById('backtestStrategyStatus');

            // 交易控制区域的元素
            const autoTradingStrategyIcon = document.getElementById('autoTradingStrategyIcon');
            const autoTradingStrategyName = document.getElementById('autoTradingStrategyName');
            const autoTradingStrategyBadge = document.getElementById('autoTradingStrategyBadge');

            // 策略信息映射
            const strategyInfo = {
                'conservative': {
                    name: '保守策略 (基准)',
                    details: '胜率: 37.5% | 月盈利: $26.44 | 每日限制: 2笔',
                    icon: 'fas fa-shield-alt',
                    color: 'secondary'
                },
                'optimized': {
                    name: '优化策略 (推荐) ⭐',
                    details: '胜率: 57.1% | 月盈利: $119.96 | 每日限制: 5笔',
                    icon: 'fas fa-star',
                    color: 'success'
                },
                'aggressive': {
                    name: '激进策略',
                    details: '胜率: 45.2% | 月盈利: $85.30 | 每日限制: 10笔',
                    icon: 'fas fa-rocket',
                    color: 'warning'
                },
                'easyTrigger': {
                    name: '易触发策略 (测试用) 🧪',
                    details: '胜率: 30.0% | 高频交易 | 每日限制: 20笔',
                    icon: 'fas fa-flask',
                    color: 'info'
                },
                'custom': {
                    name: '自定义配置',
                    details: '用户自定义参数 | 手动配置',
                    icon: 'fas fa-cog',
                    color: 'dark'
                }
            };

            const info = strategyInfo[presetName] || strategyInfo['optimized'];

            // 更新回测区域显示
            if (strategyNameElement && strategyDetailsElement && strategyStatusElement) {
                strategyNameElement.textContent = info.name;
                strategyDetailsElement.textContent = info.details;
                strategyStatusElement.className = `badge bg-${info.color}`;
                strategyStatusElement.innerHTML = `<i class="${info.icon}"></i> 已选择`;
            }

            // 更新交易控制区域显示
            if (autoTradingStrategyIcon && autoTradingStrategyName && autoTradingStrategyBadge) {
                autoTradingStrategyIcon.className = `${info.icon} text-${info.color}`;
                autoTradingStrategyName.textContent = info.name;
                autoTradingStrategyBadge.className = `badge bg-${info.color}`;
                autoTradingStrategyBadge.innerHTML = `<i class="fas fa-check-circle"></i> 已激活`;
            }

            console.log(`📊 已更新策略显示: ${info.name}`);
        }

    </script>
</body>
</html>
