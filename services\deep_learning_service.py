#!/usr/bin/env python3
"""
深度学习服务 - 处理模型训练、管理和推理
"""

import os
import json
import sqlite3
import numpy as np
import pandas as pd
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Any, Tuple
import logging
import threading
import time
import uuid

# GPU相关导入
try:
    import torch
    import torch.nn as nn
    import torch.optim as optim
    from torch.utils.data import Dataset, DataLoader
    TORCH_AVAILABLE = True
except ImportError:
    TORCH_AVAILABLE = False

try:
    import tensorflow as tf
    TF_AVAILABLE = True
except ImportError:
    TF_AVAILABLE = False

# 配置日志
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

class TradingDataset(Dataset):
    """交易数据集类"""
    
    def __init__(self, sequences, targets):
        self.sequences = torch.FloatTensor(sequences)
        self.targets = torch.FloatTensor(targets)
    
    def __len__(self):
        return len(self.sequences)
    
    def __getitem__(self, idx):
        return self.sequences[idx], self.targets[idx]

class LSTMModel(nn.Module):
    """LSTM交易预测模型"""

    def __init__(self, input_size, hidden_size, num_layers, output_size, dropout=0.2):
        super(LSTMModel, self).__init__()
        self.hidden_size = hidden_size
        self.num_layers = num_layers

        self.lstm = nn.LSTM(input_size, hidden_size, num_layers,
                           batch_first=True, dropout=dropout)
        self.dropout = nn.Dropout(dropout)
        self.fc = nn.Linear(hidden_size, output_size)

    def forward(self, x):
        h0 = torch.zeros(self.num_layers, x.size(0), self.hidden_size).to(x.device)
        c0 = torch.zeros(self.num_layers, x.size(0), self.hidden_size).to(x.device)

        out, _ = self.lstm(x, (h0, c0))
        out = self.dropout(out[:, -1, :])
        out = self.fc(out)
        return out

class GRUModel(nn.Module):
    """GRU交易预测模型"""

    def __init__(self, input_size, hidden_size, num_layers, output_size, dropout=0.2):
        super(GRUModel, self).__init__()
        self.hidden_size = hidden_size
        self.num_layers = num_layers

        self.gru = nn.GRU(input_size, hidden_size, num_layers,
                         batch_first=True, dropout=dropout)
        self.dropout = nn.Dropout(dropout)
        self.fc = nn.Linear(hidden_size, output_size)

    def forward(self, x):
        h0 = torch.zeros(self.num_layers, x.size(0), self.hidden_size).to(x.device)

        out, _ = self.gru(x, h0)
        out = self.dropout(out[:, -1, :])
        out = self.fc(out)
        return out

class TransformerModel(nn.Module):
    """Transformer交易预测模型"""

    def __init__(self, input_size, hidden_size, num_layers, output_size, dropout=0.2, nhead=8):
        super(TransformerModel, self).__init__()
        self.input_size = input_size
        self.hidden_size = hidden_size

        # 输入投影
        self.input_projection = nn.Linear(input_size, hidden_size)

        # 位置编码
        self.pos_encoding = nn.Parameter(torch.randn(1000, hidden_size))

        # Transformer编码器
        encoder_layer = nn.TransformerEncoderLayer(
            d_model=hidden_size,
            nhead=nhead,
            dim_feedforward=hidden_size * 4,
            dropout=dropout,
            batch_first=True
        )
        self.transformer = nn.TransformerEncoder(encoder_layer, num_layers=num_layers)

        # 输出层
        self.dropout = nn.Dropout(dropout)
        self.fc = nn.Linear(hidden_size, output_size)

    def forward(self, x):
        seq_len = x.size(1)

        # 输入投影
        x = self.input_projection(x)

        # 添加位置编码
        x = x + self.pos_encoding[:seq_len, :].unsqueeze(0)

        # Transformer编码
        out = self.transformer(x)

        # 取最后一个时间步
        out = self.dropout(out[:, -1, :])
        out = self.fc(out)
        return out

class CNNLSTMModel(nn.Module):
    """CNN-LSTM混合模型"""

    def __init__(self, input_size, hidden_size, num_layers, output_size, dropout=0.2):
        super(CNNLSTMModel, self).__init__()
        self.hidden_size = hidden_size
        self.num_layers = num_layers

        # CNN特征提取
        self.conv1 = nn.Conv1d(input_size, hidden_size//2, kernel_size=3, padding=1)
        self.conv2 = nn.Conv1d(hidden_size//2, hidden_size//2, kernel_size=3, padding=1)
        self.pool = nn.MaxPool1d(2)

        # LSTM序列建模
        self.lstm = nn.LSTM(hidden_size//2, hidden_size, num_layers,
                           batch_first=True, dropout=dropout)

        # 输出层
        self.dropout = nn.Dropout(dropout)
        self.fc = nn.Linear(hidden_size, output_size)

    def forward(self, x):
        # CNN特征提取 (batch, seq, features) -> (batch, features, seq)
        x = x.transpose(1, 2)
        x = torch.relu(self.conv1(x))
        x = torch.relu(self.conv2(x))

        # 如果序列长度足够，进行池化
        if x.size(2) >= 2:
            x = self.pool(x)

        # 转回LSTM格式 (batch, features, seq) -> (batch, seq, features)
        x = x.transpose(1, 2)

        # LSTM处理
        h0 = torch.zeros(self.num_layers, x.size(0), self.hidden_size).to(x.device)
        c0 = torch.zeros(self.num_layers, x.size(0), self.hidden_size).to(x.device)

        out, _ = self.lstm(x, (h0, c0))
        out = self.dropout(out[:, -1, :])
        out = self.fc(out)
        return out

class AttentionLSTMModel(nn.Module):
    """注意力LSTM模型"""

    def __init__(self, input_size, hidden_size, num_layers, output_size, dropout=0.2):
        super(AttentionLSTMModel, self).__init__()
        self.hidden_size = hidden_size
        self.num_layers = num_layers

        # LSTM层
        self.lstm = nn.LSTM(input_size, hidden_size, num_layers,
                           batch_first=True, dropout=dropout)

        # 注意力机制
        self.attention = nn.MultiheadAttention(hidden_size, num_heads=8,
                                             dropout=dropout, batch_first=True)

        # 输出层
        self.dropout = nn.Dropout(dropout)
        self.fc = nn.Linear(hidden_size, output_size)

    def forward(self, x):
        # LSTM处理
        h0 = torch.zeros(self.num_layers, x.size(0), self.hidden_size).to(x.device)
        c0 = torch.zeros(self.num_layers, x.size(0), self.hidden_size).to(x.device)

        lstm_out, _ = self.lstm(x, (h0, c0))

        # 自注意力
        attn_out, _ = self.attention(lstm_out, lstm_out, lstm_out)

        # 取最后一个时间步
        out = self.dropout(attn_out[:, -1, :])
        out = self.fc(out)
        return out

class DeepLearningService:
    """深度学习服务"""
    
    def __init__(self):
        self.db_path = 'trading_system.db'
        self.models_path = 'deep_learning_models'
        self.training_tasks = {}  # 存储训练任务状态
        self.training_control = {}  # 存储训练控制状态 {task_id: {'stop': False, 'pause': False}}

        # 确保模型目录存在
        if not os.path.exists(self.models_path):
            os.makedirs(self.models_path)

        # 检测GPU
        self.device = self._detect_device()
        logger.info(f"🔧 深度学习服务使用设备: {self.device}")

        # 初始化数据库表
        self._init_database()
    
    def _detect_device(self):
        """检测可用设备并验证GPU可用性"""
        if TORCH_AVAILABLE and torch.cuda.is_available():
            try:
                # 验证GPU实际可用
                torch.zeros(1).cuda()  # 测试GPU操作
                logger.info(f"✅ GPU验证通过: {torch.cuda.get_device_name(0)}")
                return torch.device('cuda')
            except Exception as e:
                logger.error(f"❌ GPU初始化失败: {str(e)}")
                logger.warning("⚠️ 将回退到CPU模式")
        return torch.device('cpu')
    
    def _init_database(self):
        """初始化数据库表"""
        try:
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()
            
            # 创建深度学习模型表
            cursor.execute('''
                CREATE TABLE IF NOT EXISTS deep_learning_models (
                    id TEXT PRIMARY KEY,
                    name TEXT NOT NULL,
                    model_type TEXT NOT NULL,
                    symbol TEXT NOT NULL,
                    timeframe TEXT NOT NULL,
                    model_path TEXT,
                    config TEXT,
                    training_history TEXT,
                    performance_metrics TEXT,
                    status TEXT DEFAULT 'training',
                    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    completed_at TIMESTAMP,
                    user_id INTEGER,
                    FOREIGN KEY (user_id) REFERENCES users (id)
                )
            ''')
            
            # 创建训练任务表
            cursor.execute('''
                CREATE TABLE IF NOT EXISTS training_tasks (
                    id TEXT PRIMARY KEY,
                    model_id TEXT,
                    status TEXT DEFAULT 'pending',
                    progress REAL DEFAULT 0,
                    current_epoch INTEGER DEFAULT 0,
                    total_epochs INTEGER,
                    train_loss REAL,
                    val_loss REAL,
                    best_loss REAL,
                    logs TEXT,
                    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    started_at TIMESTAMP,
                    completed_at TIMESTAMP,
                    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    FOREIGN KEY (model_id) REFERENCES deep_learning_models (id)
                )
            ''')
            
            conn.commit()
            conn.close()
            
            logger.info("✅ 深度学习数据库表初始化完成")
            
        except Exception as e:
            logger.error(f"❌ 初始化数据库失败: {e}")
    
    def get_gpu_status(self) -> Dict[str, Any]:
        """获取GPU状态"""
        try:
            status = {
                'gpu_available': False,
                'gpu_name': None,
                'memory_total': 0,
                'memory_used': 0,
                'memory_free': 0,
                'cuda_version': None,
                'pytorch_version': None,
                'tensorflow_version': None
            }
            
            if TORCH_AVAILABLE:
                status['pytorch_version'] = torch.__version__
                
                if torch.cuda.is_available():
                    status['gpu_available'] = True
                    status['gpu_name'] = torch.cuda.get_device_name(0)
                    status['cuda_version'] = torch.version.cuda
                    
                    # GPU内存信息
                    memory_total = torch.cuda.get_device_properties(0).total_memory / 1024**3
                    memory_allocated = torch.cuda.memory_allocated(0) / 1024**3
                    memory_reserved = torch.cuda.memory_reserved(0) / 1024**3
                    
                    status['memory_total'] = memory_total
                    status['memory_used'] = memory_allocated
                    status['memory_free'] = memory_total - memory_reserved
            
            if TF_AVAILABLE:
                status['tensorflow_version'] = tf.__version__
                
                gpus = tf.config.experimental.list_physical_devices('GPU')
                if gpus and not status['gpu_available']:
                    status['gpu_available'] = True
                    status['gpu_name'] = f"TensorFlow GPU ({len(gpus)} devices)"
            
            return status
            
        except Exception as e:
            logger.error(f"❌ 获取GPU状态失败: {e}")
            return {'gpu_available': False, 'error': str(e)}
    
    def start_training(self, config: Dict[str, Any], user_id: int) -> Dict[str, Any]:
        """启动模型训练"""
        try:
            # 生成唯一ID
            model_id = str(uuid.uuid4())
            task_id = str(uuid.uuid4())
            
            # 保存模型配置到数据库
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()
            
            cursor.execute('''
                INSERT INTO deep_learning_models 
                (id, name, model_type, symbol, timeframe, config, status, user_id)
                VALUES (?, ?, ?, ?, ?, ?, ?, ?)
            ''', (
                model_id,
                config['model_name'],
                config['model_type'],
                config['symbol'],
                config['timeframe'],
                json.dumps(config),
                'training',
                user_id
            ))
            
            cursor.execute('''
                INSERT INTO training_tasks 
                (id, model_id, status, total_epochs)
                VALUES (?, ?, ?, ?)
            ''', (task_id, model_id, 'pending', config['epochs']))
            
            conn.commit()
            conn.close()
            
            # 初始化训练控制状态
            self.training_control[task_id] = {'stop': False, 'pause': False}

            # 在后台线程中启动训练
            training_thread = threading.Thread(
                target=self._train_model_async,
                args=(model_id, task_id, config)
            )
            training_thread.daemon = True
            training_thread.start()

            logger.info(f"✅ 训练任务已启动: {task_id}")

            return {
                'success': True,
                'task_id': task_id,
                'model_id': model_id,
                'message': '训练任务已启动'
            }
            
        except Exception as e:
            logger.error(f"❌ 启动训练失败: {e}")
            return {
                'success': False,
                'error': str(e)
            }
    
    def _train_model_async(self, model_id: str, task_id: str, config: Dict[str, Any]):
        """异步训练模型（增加进度回调）"""
        try:
            logger.info(f"🚀 开始训练模型: {model_id}")
            
            # 新增：训练开始回调
            self._training_callback(task_id, "start", 0, 0, 0)

            # 更新任务状态为运行中
            self._update_task_status(task_id, 'running', started_at=datetime.now())
            self._update_task_progress(task_id, 5, 0, 0, 0)  # 初始进度5%

            # 准备数据
            logger.info("📊 准备训练数据...")
            self._update_task_progress(task_id, 10, 0, 0, 0)  # 数据准备开始10%

            X_train, X_val, y_train, y_val = self._prepare_training_data(config, task_id)

            if X_train is None:
                raise Exception("数据准备失败：无法获取MT5历史数据，请检查MT5连接和数据权限")

            self._update_task_progress(task_id, 20, 0, 0, 0)  # 数据准备完成20%

            # 创建模型
            logger.info("🧠 创建模型...")
            model = self._create_model(config, X_train.shape[-1])
            self._update_task_progress(task_id, 25, 0, 0, 0)  # 模型创建完成25%

            # 训练模型
            logger.info("🏋️ 开始训练...")
            training_history = self._train_model(
                model, X_train, X_val, y_train, y_val, config, task_id
            )

            # 保存模型
            logger.info("💾 保存模型...")
            model_path = os.path.join(self.models_path, f"{model_id}.pt")
            torch.save(model.state_dict(), model_path)

            # 计算性能指标
            logger.info("📊 计算性能指标...")
            performance = self._evaluate_model(model, X_val, y_val)

            # 更新数据库
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()

            cursor.execute('''
                UPDATE deep_learning_models
                SET model_path = ?, training_history = ?, performance_metrics = ?,
                    status = 'completed', completed_at = ?
                WHERE id = ?
            ''', (
                model_path,
                json.dumps(training_history),
                json.dumps(performance),
                datetime.now(),
                model_id
            ))

            # 只有在所有轮次完成后才设置为100%
            cursor.execute('''
                UPDATE training_tasks
                SET status = 'completed', progress = 100, completed_at = ?
                WHERE id = ? AND current_epoch >= total_epochs
            ''', (datetime.now(), task_id))

            # 如果训练提前停止，保持当前进度
            cursor.execute('''
                UPDATE training_tasks
                SET status = 'completed', completed_at = ?
                WHERE id = ? AND current_epoch < total_epochs
            ''', (datetime.now(), task_id))

            conn.commit()
            conn.close()

            logger.info(f"✅ 模型训练完成: {model_id}")

        except Exception as e:
            logger.error(f"❌ 模型训练失败: {e}")

            # 更新失败状态，包含详细错误信息
            self._update_task_status(task_id, 'failed', error=str(e))

            try:
                conn = sqlite3.connect(self.db_path)
                cursor = conn.cursor()
                cursor.execute('''
                    UPDATE deep_learning_models
                    SET status = 'failed'
                    WHERE id = ?
                ''', (model_id,))
                conn.commit()
                conn.close()
            except Exception as db_error:
                logger.error(f"❌ 更新数据库失败状态时出错: {db_error}")

        finally:
            # 清理训练控制状态
            if task_id in self.training_control:
                del self.training_control[task_id]
                logger.info(f"🧹 清理训练控制状态: {task_id}")
    
    def _prepare_training_data(self, config: Dict[str, Any], task_id: str = None) -> Tuple[np.ndarray, np.ndarray, np.ndarray, np.ndarray]:
        """准备训练数据（增强版本，包含详细进度更新）"""
        try:
            # 解析数据配置
            data_config = config.get('data_config', {})
            days = self._calculate_training_days(data_config, config)
            sequence_length = config.get('sequence_length', 60)

            logger.info(f"📊 准备训练数据: {days}天, 序列长度: {sequence_length}")

            # 更新进度和状态信息
            if task_id:
                self._update_task_status(task_id, 'running',
                    logs=json.dumps({
                        'stage': 'data_preparation',
                        'message': f'开始准备训练数据: {days}天, 序列长度: {sequence_length}',
                        'data_config': data_config
                    }))
                self._update_task_progress(task_id, 12, 0, 0, 0)

            # 从MT5获取真实历史数据
            logger.info(f"🔗 连接MT5获取真实历史数据...")
            if task_id:
                self._update_task_status(task_id, 'running',
                    logs=json.dumps({
                        'stage': 'data_fetching',
                        'message': '正在连接MT5获取历史数据...',
                        'symbol': config.get('symbol', 'XAUUSD'),
                        'timeframe': config.get('timeframe', 'H1'),
                        'days': days
                    }))
                self._update_task_progress(task_id, 15, 0, 0, 0)

            price_data = self._get_mt5_historical_data(config, data_config, days)

            if price_data is None or len(price_data) == 0:
                raise Exception("无法获取MT5历史数据，请检查MT5连接和数据权限")

            logger.info(f"✅ 成功获取MT5数据: {len(price_data)}条记录")

            # 更新数据获取完成状态
            if task_id:
                self._update_task_status(task_id, 'running',
                    logs=json.dumps({
                        'stage': 'data_fetched',
                        'message': f'成功获取{len(price_data)}条历史数据',
                        'data_points': len(price_data),
                        'data_range': {
                            'start': f'{price_data[0, 3]:.5f}' if len(price_data) > 0 else None,  # 使用收盘价作为范围指示
                            'end': f'{price_data[-1, 3]:.5f}' if len(price_data) > 0 else None
                        }
                    }))
                self._update_task_progress(task_id, 18, 0, 0, 0)

            # 添加技术指标
            logger.info("📈 计算技术指标...")
            if task_id:
                self._update_task_status(task_id, 'running',
                    logs=json.dumps({
                        'stage': 'feature_calculation',
                        'message': '正在计算技术指标...',
                        'features': config.get('features', ['close', 'volume'])
                    }))
                self._update_task_progress(task_id, 20, 0, 0, 0)

            features = self._calculate_features(price_data, config)

            # 验证特征数据
            logger.info("🔍 验证特征数据...")
            if features is None:
                raise Exception("特征计算返回None")

            if not isinstance(features, np.ndarray):
                raise Exception(f"特征数据类型错误: {type(features)}")

            if len(features.shape) != 2:
                raise Exception(f"特征数据形状错误: {features.shape}, 期望2维数组")

            if features.shape[0] == 0:
                raise Exception("特征数据为空")

            logger.info(f"✅ 特征验证通过: 形状{features.shape}, 类型{features.dtype}")

            # 创建序列数据
            logger.info("🔢 创建训练序列...")
            if task_id:
                self._update_task_status(task_id, 'running',
                    logs=json.dumps({
                        'stage': 'sequence_creation',
                        'message': f'正在创建训练序列，序列长度: {sequence_length}',
                        'feature_shape': list(features.shape),
                        'feature_count': features.shape[1],
                        'data_points': features.shape[0]
                    }))
                self._update_task_progress(task_id, 22, 0, 0, 0)

            X, y = self._create_sequences(features, sequence_length)

            # 分割训练和验证集
            logger.info("✂️ 分割训练集和验证集...")
            if task_id:
                self._update_task_status(task_id, 'running',
                    logs=json.dumps({
                        'stage': 'data_splitting',
                        'message': '正在分割训练集和验证集...',
                        'total_sequences': len(X),
                        'validation_split': 0.2
                    }))
                self._update_task_progress(task_id, 24, 0, 0, 0)

            split_idx = int(len(X) * 0.8)
            X_train, X_val = X[:split_idx], X[split_idx:]
            y_train, y_val = y[:split_idx], y[split_idx:]

            logger.info(f"📊 数据准备完成: 训练集 {X_train.shape}, 验证集 {X_val.shape}")

            # 更新数据准备完成状态
            if task_id:
                self._update_task_status(task_id, 'running',
                    logs=json.dumps({
                        'stage': 'data_ready',
                        'message': '数据准备完成',
                        'train_shape': list(X_train.shape),
                        'val_shape': list(X_val.shape),
                        'train_samples': len(X_train),
                        'val_samples': len(X_val),
                        'feature_count': X_train.shape[-1] if len(X_train.shape) > 1 else 1
                    }))
                self._update_task_progress(task_id, 25, 0, 0, 0)
            
            # 数据验证检查
            if np.isnan(X_train).any() or np.isnan(X_val).any():
                raise ValueError("数据包含NaN值，请检查数据源")
                
            if np.isinf(X_train).any() or np.isinf(X_val).any():
                raise ValueError("数据包含无穷大值，请检查数据源")
                
            # 检查标签分布
            unique, counts = np.unique(y_train, return_counts=True)
            class_ratio = counts[1] / counts[0] if len(counts) > 1 else 1
            if class_ratio < 0.2 or class_ratio > 5:
                logger.warning(f"⚠️ 标签分布不平衡: 类别0={counts[0]}, 类别1={counts[1]}")
                
            return X_train, X_val, y_train, y_val
             
        except Exception as e:
            logger.error(f"❌ 数据准备失败: {e}")
            import traceback
            logger.error(f"详细错误堆栈: {traceback.format_exc()}")
            # 新增：失败回调
            if task_id:
                self._training_callback(task_id, "error", 0, 0, 0, str(e))
            return None, None, None, None

    def _get_mt5_historical_data(self, config: Dict[str, Any], data_config: Dict[str, Any], days: int) -> Optional[np.ndarray]:
        """从MT5获取真实历史数据"""
        try:
            # 导入MT5相关模块
            try:
                import MetaTrader5 as mt5
                from datetime import datetime, timedelta
            except ImportError:
                logger.error("❌ MetaTrader5模块未安装，请运行: pip install MetaTrader5")
                return None

            # 获取配置参数
            symbol = config.get('symbol', 'XAUUSD')
            timeframe = config.get('timeframe', '1h')

            # 转换时间框架
            mt5_timeframe = self._convert_timeframe_to_mt5(timeframe)
            if mt5_timeframe is None:
                logger.error(f"❌ 不支持的时间框架: {timeframe}")
                return None

            # 计算日期范围
            date_range = self._get_date_range_info(data_config)
            end_date = datetime.strptime(date_range['end_date'], '%Y-%m-%d')
            start_date = datetime.strptime(date_range['start_date'], '%Y-%m-%d')

            logger.info(f"📅 获取数据范围: {symbol} {timeframe} 从 {start_date} 到 {end_date}")

            # 初始化MT5连接
            if not mt5.initialize():
                logger.error("❌ MT5初始化失败")
                return None

            try:
                # 单次初始化带超时
                logger.info(f"🔗 连接MT5({symbol}) 超时:5秒")
                if not mt5.initialize(timeout=5000):
                    logger.error("❌ MT5初始化失败: 连接超时")
                    return None
                
                # 获取历史数据（带60秒超时）
                start_time = time.time()
                timeout = 300  # 增加超时到300秒（5分钟）
                attempt = 0
                
                while time.time() - start_time < timeout:
                    attempt += 1
                    rates = mt5.copy_rates_range(symbol, mt5_timeframe, start_date, end_date)
                    if rates is not None and len(rates) > 0:
                        logger.info(f"✅ 第{attempt}次尝试获取数据成功")
                        break
                    logger.warning(f"⚠️ 第{attempt}次获取数据失败，2秒后重试...")
                    time.sleep(2)  # 等待2秒重试
                else:
                    logger.error(f"❌ 获取MT5数据超时（{timeout}秒）")
                    return None

                # 转换为numpy数组 [open, high, low, close, volume] - 修复版本
                logger.info(f"📊 MT5数据类型: {type(rates)}")
                logger.info(f"📊 MT5数据形状: {rates.shape}")
                logger.info(f"📊 MT5数据字段: {rates.dtype.names if hasattr(rates, 'dtype') and rates.dtype.names else 'N/A'}")

                try:
                    # 方法1: 如果是结构化数组，直接提取字段
                    if hasattr(rates, 'dtype') and rates.dtype.names:
                        # 确定成交量字段名
                        volume_field = None
                        for field in ['tick_volume', 'real_volume', 'volume']:
                            if field in rates.dtype.names:
                                volume_field = field
                                break

                        if volume_field is None:
                            logger.warning("⚠️ 未找到成交量字段，使用默认值")
                            volume_data = np.ones(len(rates)) * 1000  # 默认成交量
                        else:
                            volume_data = rates[volume_field]
                            logger.info(f"✅ 使用成交量字段: {volume_field}")

                        # 提取OHLCV数据
                        price_data = np.column_stack([
                            rates['open'],
                            rates['high'],
                            rates['low'],
                            rates['close'],
                            volume_data
                        ])

                        logger.info(f"✅ 结构化数组转换成功: {price_data.shape}")

                    else:
                        # 方法2: 如果是普通数组，使用原来的方法
                        logger.info("📊 使用原始转换方法")
                        price_data = np.array([
                            [rate['open'], rate['high'], rate['low'], rate['close'], rate.get('tick_volume', 1000)]
                            for rate in rates
                        ])

                        logger.info(f"✅ 原始方法转换成功: {price_data.shape}")

                    # 验证数据
                    if price_data.shape[1] != 5:
                        raise ValueError(f"数据列数不正确: {price_data.shape[1]}, 期望5列")

                    if len(price_data) == 0:
                        raise ValueError("转换后数据为空")

                    # 检查数据有效性
                    if np.any(np.isnan(price_data)) or np.any(np.isinf(price_data)):
                        logger.warning("⚠️ 数据包含NaN或Inf值，进行清理")
                        price_data = np.nan_to_num(price_data, nan=0.0, posinf=0.0, neginf=0.0)

                    logger.info(f"✅ 成功获取MT5数据: {len(price_data)}条 {symbol} {timeframe} 记录")
                    logger.info(f"📊 数据范围: {price_data[:, 3].min():.5f} - {price_data[:, 3].max():.5f}")
                    logger.info(f"📊 数据形状: {price_data.shape}, 数据类型: {price_data.dtype}")

                    return price_data

                except Exception as conversion_error:
                    logger.error(f"❌ 数据转换失败: {conversion_error}")
                    return None

            except Exception as e:
                logger.error(f"❌ 获取MT5数据异常: {str(e)}")
                return None
                
            finally:
                try:
                    logger.info("🔌 关闭MT5连接")
                    mt5.shutdown()
                except Exception as e:
                    logger.warning(f"⚠️ 关闭MT5连接失败: {str(e)}")

        except Exception as e:
            logger.error(f"❌ 获取MT5数据失败: {e}")
            return None

    def _convert_timeframe_to_mt5(self, timeframe: str):
        """转换时间框架到MT5格式（支持多种格式）"""
        try:
            import MetaTrader5 as mt5

            # 支持多种时间框架格式
            timeframe_map = {
                # 分钟级别
                '1m': mt5.TIMEFRAME_M1,
                'M1': mt5.TIMEFRAME_M1,
                '5m': mt5.TIMEFRAME_M5,
                'M5': mt5.TIMEFRAME_M5,
                '15m': mt5.TIMEFRAME_M15,
                'M15': mt5.TIMEFRAME_M15,
                '30m': mt5.TIMEFRAME_M30,
                'M30': mt5.TIMEFRAME_M30,

                # 小时级别
                '1h': mt5.TIMEFRAME_H1,
                'H1': mt5.TIMEFRAME_H1,
                '4h': mt5.TIMEFRAME_H4,
                'H4': mt5.TIMEFRAME_H4,

                # 日级别
                '1d': mt5.TIMEFRAME_D1,
                'D1': mt5.TIMEFRAME_D1,
                'daily': mt5.TIMEFRAME_D1,

                # 周级别
                '1w': mt5.TIMEFRAME_W1,
                'W1': mt5.TIMEFRAME_W1,
                'weekly': mt5.TIMEFRAME_W1,

                # 月级别
                '1M': mt5.TIMEFRAME_MN1,
                'MN1': mt5.TIMEFRAME_MN1,
                'monthly': mt5.TIMEFRAME_MN1
            }

            result = timeframe_map.get(timeframe)

            if result is None:
                logger.error(f"❌ 不支持的时间框架: {timeframe}")
                logger.info(f"💡 支持的时间框架: {list(timeframe_map.keys())}")
            else:
                logger.info(f"✅ 时间框架转换: {timeframe} -> {result}")

            return result

        except ImportError:
            logger.error("❌ MetaTrader5模块未安装")
            return None
    
    def _calculate_features(self, price_data: np.ndarray, config: Dict[str, Any]) -> np.ndarray:
        """计算特征（支持列表和字典两种配置格式）"""
        try:
            logger.info(f"📊 开始计算特征，数据形状: {price_data.shape}")

            # 获取特征配置
            features_config = config.get('features', ['close', 'volume'])

            # 支持两种配置格式
            if isinstance(features_config, list):
                # 新格式：['close', 'volume', 'high', 'low']
                return self._calculate_features_from_list(price_data, features_config)
            elif isinstance(features_config, dict):
                # 旧格式：{'price': True, 'technical': True}
                return self._calculate_features_from_dict(price_data, features_config)
            else:
                logger.warning(f"⚠️ 不支持的特征配置格式: {type(features_config)}")
                # 默认使用收盘价
                return price_data[:, 3:4]  # 只返回收盘价列

        except Exception as e:
            logger.error(f"❌ 特征计算失败: {e}")
            # 返回收盘价作为默认特征
            return price_data[:, 3:4]

    def _calculate_features_from_list(self, price_data: np.ndarray, feature_list: list) -> np.ndarray:
        """从特征列表计算特征（增强版本）"""
        try:
            # 详细的输入数据检查
            logger.info(f"📊 输入数据检查:")
            logger.info(f"   数据类型: {type(price_data)}")
            logger.info(f"   数据形状: {price_data.shape}")
            logger.info(f"   数据dtype: {price_data.dtype}")
            logger.info(f"   特征列表: {feature_list}")

            # 验证输入数据
            if not isinstance(price_data, np.ndarray):
                logger.error(f"❌ 输入数据不是NumPy数组: {type(price_data)}")
                return np.array([[0.0]] * len(price_data))  # 返回默认数据

            if len(price_data.shape) != 2:
                logger.error(f"❌ 数据形状不正确: {price_data.shape}, 期望2维数组")
                return np.array([[0.0]] * len(price_data))

            if price_data.shape[0] == 0:
                logger.error(f"❌ 数据为空")
                return np.array([[0.0]])

            logger.info(f"✅ 输入数据验证通过")

            features = []

            # 价格数据映射 [open, high, low, close, volume]
            price_map = {
                'open': 0,
                'high': 1,
                'low': 2,
                'close': 3,
                'volume': 4
            }

            logger.info(f"📈 开始计算特征: {feature_list}")

            # 提取指定的价格特征
            for i, feature_name in enumerate(feature_list):
                logger.info(f"🔄 处理特征 {i+1}/{len(feature_list)}: {feature_name}")

                if feature_name in price_map:
                    col_idx = price_map[feature_name]
                    logger.info(f"   映射到列索引: {col_idx}")

                    if col_idx < price_data.shape[1]:
                        try:
                            # 安全地提取特征数据
                            feature_data = price_data[:, col_idx]
                            logger.info(f"   提取数据形状: {feature_data.shape}")
                            logger.info(f"   数据范围: {feature_data.min():.5f} - {feature_data.max():.5f}")

                            # 检查数据有效性
                            if np.any(np.isnan(feature_data)) or np.any(np.isinf(feature_data)):
                                logger.warning(f"   ⚠️ 数据包含NaN或Inf，进行清理")
                                feature_data = np.nan_to_num(feature_data, nan=0.0, posinf=0.0, neginf=0.0)

                            # 标准化特征
                            feature_mean = feature_data.mean()
                            feature_std = feature_data.std()

                            logger.info(f"   统计信息: 均值={feature_mean:.5f}, 标准差={feature_std:.5f}")

                            if feature_std > 1e-10:
                                normalized_feature = (feature_data - feature_mean) / feature_std
                            else:
                                logger.warning(f"   ⚠️ 标准差过小，只进行中心化")
                                normalized_feature = feature_data - feature_mean

                            # 重塑为列向量
                            normalized_feature = normalized_feature.reshape(-1, 1)
                            features.append(normalized_feature)

                            logger.info(f"   ✅ 特征 {feature_name} 处理完成: 形状 {normalized_feature.shape}")

                        except Exception as feature_error:
                            logger.error(f"   ❌ 处理特征 {feature_name} 失败: {feature_error}")
                            # 添加默认特征
                            default_feature = np.zeros((price_data.shape[0], 1))
                            features.append(default_feature)
                            logger.info(f"   🔄 使用默认特征替代")

                    else:
                        logger.warning(f"   ⚠️ 特征 {feature_name} 超出数据列范围: {col_idx} >= {price_data.shape[1]}")
                        # 添加默认特征
                        default_feature = np.zeros((price_data.shape[0], 1))
                        features.append(default_feature)

                else:
                    logger.warning(f"   ⚠️ 不支持的特征: {feature_name}")
                    # 添加默认特征
                    default_feature = np.zeros((price_data.shape[0], 1))
                    features.append(default_feature)

            # 合并所有特征
            if features:
                try:
                    logger.info(f"🔗 合并 {len(features)} 个特征")
                    for i, feature in enumerate(features):
                        logger.info(f"   特征 {i}: 形状 {feature.shape}")

                    result = np.concatenate(features, axis=1)
                    logger.info(f"✅ 特征计算完成，最终形状: {result.shape}")
                    logger.info(f"📊 最终数据范围: {result.min():.5f} - {result.max():.5f}")

                    return result

                except Exception as concat_error:
                    logger.error(f"❌ 特征合并失败: {concat_error}")
                    # 返回第一个特征作为备选
                    if len(features) > 0:
                        logger.info(f"🔄 使用第一个特征作为备选")
                        return features[0]
                    else:
                        logger.info(f"🔄 使用收盘价作为备选")
                        return price_data[:, 3:4]
            else:
                logger.warning("⚠️ 没有有效特征，使用收盘价")
                try:
                    return price_data[:, 3:4]
                except Exception as fallback_error:
                    logger.error(f"❌ 连收盘价都无法提取: {fallback_error}")
                    # 最后的备选方案：返回全零数组
                    return np.zeros((price_data.shape[0], 1))

        except Exception as e:
            logger.error(f"❌ 列表特征计算失败: {e}")
            import traceback
            logger.error(f"详细错误: {traceback.format_exc()}")

            # 安全的备选方案
            try:
                if hasattr(price_data, 'shape') and len(price_data.shape) == 2 and price_data.shape[1] > 3:
                    return price_data[:, 3:4]  # 收盘价
                else:
                    return np.zeros((len(price_data) if hasattr(price_data, '__len__') else 100, 1))
            except:
                return np.zeros((100, 1))  # 最终备选

    def _calculate_features_from_dict(self, price_data: np.ndarray, features_config: dict) -> np.ndarray:
        """从特征字典计算特征（旧格式兼容）"""
        try:
            features = []

            # 价格特征
            if features_config.get('price', True):
                # 归一化价格数据
                normalized_prices = (price_data - price_data.mean(axis=0)) / (price_data.std(axis=0) + 1e-10)
                features.append(normalized_prices)

            # 技术指标
            if features_config.get('technical', True):
                # 简单移动平均
                sma_5 = np.convolve(price_data[:, 3], np.ones(5)/5, mode='same')
                sma_20 = np.convolve(price_data[:, 3], np.ones(20)/20, mode='same')

                # RSI (简化版)
                price_changes = np.diff(price_data[:, 3])
                gains = np.where(price_changes > 0, price_changes, 0)
                losses = np.where(price_changes < 0, -price_changes, 0)

                avg_gains = np.convolve(gains, np.ones(14)/14, mode='same')
                avg_losses = np.convolve(losses, np.ones(14)/14, mode='same')

                rs = avg_gains / (avg_losses + 1e-10)
                rsi = 100 - (100 / (1 + rs))
                rsi = np.concatenate([[50], rsi])  # 添加第一个值

                technical_features = np.column_stack([
                    sma_5, sma_20, rsi
                ])

                # 归一化技术指标
                technical_features = (technical_features - technical_features.mean(axis=0)) / (technical_features.std(axis=0) + 1e-10)
                features.append(technical_features)

            # 合并所有特征
            if features:
                return np.concatenate(features, axis=1)
            else:
                return price_data

        except Exception as e:
            logger.error(f"❌ 字典特征计算失败: {e}")
            return price_data[:, 3:4]
    
    def _create_sequences(self, data: np.ndarray, sequence_length: int) -> Tuple[np.ndarray, np.ndarray]:
        """创建序列数据（修复版本）"""
        try:
            logger.info(f"🔢 创建序列数据:")
            logger.info(f"   输入数据形状: {data.shape}")
            logger.info(f"   序列长度: {sequence_length}")

            # 验证输入数据
            if not isinstance(data, np.ndarray):
                raise ValueError(f"输入数据不是NumPy数组: {type(data)}")

            if len(data.shape) != 2:
                raise ValueError(f"输入数据形状不正确: {data.shape}, 期望2维数组")

            if len(data) <= sequence_length:
                raise ValueError(f"数据长度({len(data)})不足以创建序列(需要>{sequence_length})")

            X, y = [], []

            # 确定用于预测的列（通常是第一列，代表主要特征如收盘价）
            target_col = 0  # 使用第一列作为预测目标

            logger.info(f"   使用第{target_col}列作为预测目标")

            for i in range(sequence_length, len(data)):
                try:
                    # 创建输入序列
                    sequence = data[i-sequence_length:i]
                    X.append(sequence)

                    # 创建预测目标（价格变化方向）
                    current_value = data[i-1, target_col]
                    next_value = data[i, target_col]

                    # 二分类：上涨(1)或下跌(0)
                    y.append(1 if next_value > current_value else 0)

                except IndexError as idx_error:
                    logger.error(f"❌ 索引错误在位置{i}: {idx_error}")
                    logger.error(f"   数据形状: {data.shape}, 尝试访问: [{i-sequence_length}:{i}] 和 [{i}, {target_col}]")
                    raise

            X_array = np.array(X)
            y_array = np.array(y)

            logger.info(f"✅ 序列创建完成:")
            logger.info(f"   X形状: {X_array.shape}")
            logger.info(f"   y形状: {y_array.shape}")
            logger.info(f"   序列数量: {len(X_array)}")

            return X_array, y_array

        except Exception as e:
            logger.error(f"❌ 序列创建失败: {e}")
            import traceback
            logger.error(f"详细错误: {traceback.format_exc()}")

            # 返回最小可用的序列
            try:
                min_sequences = max(1, min(10, len(data) - sequence_length))
                X_fallback = np.zeros((min_sequences, sequence_length, data.shape[1]))
                y_fallback = np.zeros(min_sequences)
                logger.info(f"🔄 返回备用序列: X{X_fallback.shape}, y{y_fallback.shape}")
                return X_fallback, y_fallback
            except:
                # 最终备选方案
                X_final = np.zeros((1, sequence_length, 1))
                y_final = np.zeros(1)
                logger.info(f"🔄 返回最终备用序列: X{X_final.shape}, y{y_final.shape}")
                return X_final, y_final
    
    def _create_model(self, config: Dict[str, Any], input_size: int) -> nn.Module:
        """创建模型"""
        model_type = config.get('model_type', 'lstm').lower()
        hidden_size = config.get('hidden_size', 128)
        num_layers = config.get('num_layers', 2)
        dropout = config.get('dropout', 0.2)
        output_size = 1  # 二分类

        logger.info(f"🧠 创建 {model_type.upper()} 模型 (输入维度: {input_size}, 隐藏层: {hidden_size})")

        if model_type == 'lstm':
            model = LSTMModel(
                input_size=input_size,
                hidden_size=hidden_size,
                num_layers=num_layers,
                output_size=output_size,
                dropout=dropout
            )
        elif model_type == 'gru':
            model = GRUModel(
                input_size=input_size,
                hidden_size=hidden_size,
                num_layers=num_layers,
                output_size=output_size,
                dropout=dropout
            )
        elif model_type == 'transformer':
            # Transformer需要确保hidden_size能被nhead整除
            nhead = 8
            if hidden_size % nhead != 0:
                hidden_size = ((hidden_size // nhead) + 1) * nhead
                logger.info(f"🔧 调整Transformer隐藏层大小为 {hidden_size} (确保能被注意力头数整除)")

            model = TransformerModel(
                input_size=input_size,
                hidden_size=hidden_size,
                num_layers=num_layers,
                output_size=output_size,
                dropout=dropout,
                nhead=nhead
            )
        elif model_type == 'cnn_lstm':
            model = CNNLSTMModel(
                input_size=input_size,
                hidden_size=hidden_size,
                num_layers=num_layers,
                output_size=output_size,
                dropout=dropout
            )
        elif model_type == 'attention_lstm':
            model = AttentionLSTMModel(
                input_size=input_size,
                hidden_size=hidden_size,
                num_layers=num_layers,
                output_size=output_size,
                dropout=dropout
            )
        else:
            supported_types = ['lstm', 'gru', 'transformer', 'cnn_lstm', 'attention_lstm']
            raise ValueError(f"不支持的模型类型: {model_type}。支持的类型: {supported_types}")

        # 移动到设备
        model = model.to(self.device)

        # 打印模型信息
        total_params = sum(p.numel() for p in model.parameters())
        trainable_params = sum(p.numel() for p in model.parameters() if p.requires_grad)

        logger.info(f"📊 模型参数统计:")
        logger.info(f"   总参数: {total_params:,}")
        logger.info(f"   可训练参数: {trainable_params:,}")
        logger.info(f"   模型大小: {total_params * 4 / 1024 / 1024:.2f} MB")

        return model
    
    def _train_model(self, model: nn.Module, X_train: np.ndarray, X_val: np.ndarray,
                    y_train: np.ndarray, y_val: np.ndarray, config: Dict[str, Any],
                    task_id: str) -> Dict[str, Any]:
        """训练模型"""

        # 创建数据加载器
        logger.info("📦 创建数据加载器...")
        train_dataset = TradingDataset(X_train, y_train)
        val_dataset = TradingDataset(X_val, y_val)

        train_loader = DataLoader(
            train_dataset,
            batch_size=config.get('batch_size', 32),
            shuffle=True,
            num_workers=0,  # Windows上建议设为0
            pin_memory=True  # 如果使用GPU，这可以加速数据传输
        )
        val_loader = DataLoader(
            val_dataset,
            batch_size=config.get('batch_size', 32),
            shuffle=False,
            num_workers=0,
            pin_memory=True
        )

        # 定义损失函数和优化器
        logger.info("⚙️ 初始化优化器...")
        criterion = nn.BCEWithLogitsLoss()
        optimizer = optim.Adam(
            model.parameters(),
            lr=config.get('learning_rate', 0.001),
            weight_decay=1e-5  # 添加权重衰减防止过拟合
        )
        
        # 学习率调度器
        scheduler = optim.lr_scheduler.ReduceLROnPlateau(
            optimizer,
            mode='min',
            factor=0.5,
            patience=5,
            verbose=True
        )

        # 训练历史
        history = {
            'train_loss': [],
            'val_loss': [],
            'train_acc': [],
            'val_acc': []
        }

        best_val_loss = float('inf')
        patience_counter = 0
        patience = config.get('patience', 10)
        epochs = config.get('epochs', 100)

        # 初始化训练进度（30%开始，70%结束）
        base_progress = 30
        training_progress_range = 70

        logger.info(f"🏋️ 开始训练 {epochs} 轮...")

        # 更新状态：开始模型训练
        self._update_task_status(task_id, 'running',
            logs=json.dumps({
                'stage': 'model_training',
                'message': f'开始训练模型，共{epochs}轮',
                'epochs': epochs,
                'batch_size': config.get('batch_size', 32),
                'learning_rate': config.get('learning_rate', 0.001),
                'train_samples': len(X_train),
                'val_samples': len(X_val)
            }))
        self._update_task_progress(task_id, base_progress, 0, epochs, 0)

        for epoch in range(epochs):
            # 检查训练控制状态
            control_state = self.training_control.get(task_id, {})

            # 检查停止信号
            if control_state.get('stop', False):
                logger.info(f"🛑 训练被用户停止 (Epoch {epoch + 1})")
                break

            # 检查暂停信号
            while control_state.get('pause', False):
                logger.info(f"⏸️ 训练已暂停 (Epoch {epoch + 1})")
                time.sleep(1)  # 等待1秒后重新检查
                control_state = self.training_control.get(task_id, {})

                # 在暂停期间也检查停止信号
                if control_state.get('stop', False):
                    logger.info(f"🛑 训练在暂停期间被停止")
                    break

            # 如果在暂停检查中收到停止信号，跳出主循环
            if control_state.get('stop', False):
                break

            # 训练阶段（带超时检查）
            epoch_start_time = time.time()
            model.train()
            train_loss = 0
            train_correct = 0
            train_total = 0
            
            # 设置每个epoch的最大执行时间（5分钟）
            max_epoch_time = 5 * 60
            
            try:
                # 训练批次
                for batch_idx, (batch_X, batch_y) in enumerate(train_loader):
                    # 检查是否超时
                    if time.time() - epoch_start_time > max_epoch_time:
                        raise TimeoutError(f"训练epoch {epoch+1} 超时（超过{max_epoch_time}秒）")
                        
                    batch_X = batch_X.to(self.device)
                    batch_y = batch_y.to(self.device).float()
                    
                    optimizer.zero_grad()
                    outputs = model(batch_X).squeeze()
                    loss = criterion(outputs, batch_y)
                    loss.backward()
                    optimizer.step()
                    
                    train_loss += loss.item()
                    predicted = (torch.sigmoid(outputs) > 0.5).float()
                    train_total += batch_y.size(0)
                    train_correct += (predicted == batch_y).sum().item()
                    
                    # 每10个批次记录一次
                    if batch_idx % 10 == 0:
                        logger.debug(f"Epoch {epoch+1} 批次 {batch_idx}/{len(train_loader)}: 当前损失 {loss.item():.4f}")
            
            except TimeoutError as te:
                logger.error(f"❌ {str(te)}")
                self._update_task_status(task_id, 'failed', error=f"训练超时: {str(te)}")
                return history  # 返回当前历史记录
            
            # 验证阶段（带超时检查）
            val_start_time = time.time()
            model.eval()
            val_loss = 0
            val_correct = 0
            val_total = 0
            
            try:
                with torch.no_grad():
                    for batch_idx, (batch_X, batch_y) in enumerate(val_loader):
                        # 检查是否超时
                        if time.time() - val_start_time > max_epoch_time:
                            raise TimeoutError(f"验证epoch {epoch+1} 超时（超过{max_epoch_time}秒）")
                            
                        batch_X = batch_X.to(self.device)
                        batch_y = batch_y.to(self.device).float()
                        
                        outputs = model(batch_X).squeeze()
                        loss = criterion(outputs, batch_y)
                        
                        val_loss += loss.item()
                        predicted = (torch.sigmoid(outputs) > 0.5).float()
                        val_total += batch_y.size(0)
                        val_correct += (predicted == batch_y).sum().item()
                        
                        # 每10个批次记录一次
                        if batch_idx % 10 == 0:
                            logger.debug(f"验证 Epoch {epoch+1} 批次 {batch_idx}/{len(val_loader)}: 当前损失 {loss.item():.4f}")
            
            except TimeoutError as te:
                logger.error(f"❌ {str(te)}")
                self._update_task_status(task_id, 'failed', error=f"验证超时: {str(te)}")
                return history  # 返回当前历史记录
            
            # 计算平均损失和准确率
            avg_train_loss = train_loss / len(train_loader)
            avg_val_loss = val_loss / len(val_loader)
            train_acc = train_correct / train_total
            val_acc = val_correct / val_total
            
            # 记录历史
            history['train_loss'].append(avg_train_loss)
            history['val_loss'].append(avg_val_loss)
            history['train_acc'].append(train_acc)
            history['val_acc'].append(val_acc)
            
            # 更新任务进度（30%-95%范围内）
            epoch_progress = (epoch + 1) / epochs
            progress = base_progress + (epoch_progress * training_progress_range)
            progress = min(95, progress)  # 最大95%，留5%给最终保存

            self._update_task_progress(
                task_id, progress, epoch + 1, avg_train_loss, avg_val_loss
            )
            
            # 早停检查
            if avg_val_loss < best_val_loss:
                best_val_loss = avg_val_loss
                patience_counter = 0
            else:
                patience_counter += 1
                
                if patience_counter >= patience:
                    logger.info(f"早停触发，在第 {epoch + 1} 轮停止训练")
                    break
            
            # 日志输出
            if (epoch + 1) % 5 == 0 or epoch == 0:  # 更频繁的日志输出
                logger.info(f"Epoch {epoch + 1}/{epochs} ({progress:.1f}%): "
                          f"Train Loss: {avg_train_loss:.4f}, "
                          f"Val Loss: {avg_val_loss:.4f}, "
                          f"Train Acc: {train_acc:.4f}, "
                          f"Val Acc: {val_acc:.4f}")

                # 输出GPU内存使用情况（如果使用GPU）
                if self.device.type == 'cuda':
                    memory_allocated = torch.cuda.memory_allocated(0) / 1024**3
                    memory_reserved = torch.cuda.memory_reserved(0) / 1024**3
                    logger.info(f"GPU内存: 已分配 {memory_allocated:.2f}GB, 已保留 {memory_reserved:.2f}GB")
        
        return history
    
    def _evaluate_model(self, model: nn.Module, X_val: np.ndarray, y_val: np.ndarray) -> Dict[str, Any]:
        """评估模型性能"""
        model.eval()
        
        val_dataset = TradingDataset(X_val, y_val)
        val_loader = DataLoader(val_dataset, batch_size=32, shuffle=False)
        
        all_predictions = []
        all_targets = []
        
        with torch.no_grad():
            for batch_X, batch_y in val_loader:
                batch_X = batch_X.to(self.device)
                outputs = model(batch_X).squeeze()
                predictions = torch.sigmoid(outputs).cpu().numpy()
                
                all_predictions.extend(predictions)
                all_targets.extend(batch_y.numpy())
        
        all_predictions = np.array(all_predictions)
        all_targets = np.array(all_targets)
        
        # 计算性能指标
        predicted_classes = (all_predictions > 0.5).astype(int)
        accuracy = (predicted_classes == all_targets).mean()
        
        # 计算精确率、召回率等
        tp = ((predicted_classes == 1) & (all_targets == 1)).sum()
        fp = ((predicted_classes == 1) & (all_targets == 0)).sum()
        tn = ((predicted_classes == 0) & (all_targets == 0)).sum()
        fn = ((predicted_classes == 0) & (all_targets == 1)).sum()
        
        precision = tp / (tp + fp) if (tp + fp) > 0 else 0
        recall = tp / (tp + fn) if (tp + fn) > 0 else 0
        f1_score = 2 * (precision * recall) / (precision + recall) if (precision + recall) > 0 else 0
        
        return {
            'accuracy': float(accuracy),
            'precision': float(precision),
            'recall': float(recall),
            'f1_score': float(f1_score),
            'total_samples': len(all_targets)
        }
    
    def _update_task_status(self, task_id: str, status: str, **kwargs):
        """更新任务状态"""
        try:
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()
            
            update_fields = ['status = ?', 'updated_at = ?']
            update_values = [status, datetime.now().isoformat()]

            if 'started_at' in kwargs:
                update_fields.append('started_at = ?')
                update_values.append(kwargs['started_at'])

            if 'completed_at' in kwargs:
                update_fields.append('completed_at = ?')
                update_values.append(kwargs['completed_at'])

            if 'error' in kwargs:
                update_fields.append('logs = ?')
                update_values.append(json.dumps({'error': kwargs['error']}))

            update_values.append(task_id)
            
            cursor.execute(f'''
                UPDATE training_tasks 
                SET {', '.join(update_fields)}
                WHERE id = ?
            ''', update_values)
            
            conn.commit()
            conn.close()
            
        except Exception as e:
            logger.error(f"❌ 更新任务状态失败: {e}")
    
    def _update_task_progress(self, task_id: str, progress: float, epoch: int,
                            train_loss: float, val_loss: float):
        """更新任务进度并触发回调"""
        try:
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()
            
            cursor.execute('''
                UPDATE training_tasks
                SET progress = ?, current_epoch = ?, train_loss = ?, val_loss = ?, updated_at = ?
                WHERE id = ?
            ''', (progress, epoch, train_loss, val_loss, datetime.now().isoformat(), task_id))
            
            conn.commit()
            conn.close()
            
            # 新增：实时进度回调
            self._training_callback(task_id, "progress", progress, train_loss, val_loss)
            
        except Exception as e:
            logger.error(f"❌ 更新任务进度失败: {e}")
            # 新增：错误回调
            self._training_callback(task_id, "error", progress, train_loss, val_loss, str(e))
    
    def get_training_progress(self, task_id: str) -> Dict[str, Any]:
        """获取训练进度"""
        try:
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()
            
            cursor.execute('''
                SELECT status, progress, current_epoch, total_epochs, 
                       train_loss, val_loss, best_loss
                FROM training_tasks 
                WHERE id = ?
            ''', (task_id,))
            
            result = cursor.fetchone()
            conn.close()
            
            if result:
                return {
                    'success': True,
                    'progress': {
                        'status': result[0],
                        'progress': result[1],
                        'epoch': result[2],
                        'total_epochs': result[3],
                        'train_loss': result[4],
                        'val_loss': result[5],
                        'best_loss': result[6]
                    }
                }
            else:
                return {'success': False, 'error': '任务不存在'}
                
        except Exception as e:
            logger.error(f"❌ 获取训练进度失败: {e}")
            return {'success': False, 'error': str(e)}
    
    def get_training_tasks(self, user_id: int = None) -> Dict[str, Any]:
        """获取训练任务列表"""
        try:
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()
            
            if user_id:
                cursor.execute('''
                    SELECT t.id, m.name, m.model_type, t.status, t.progress, 
                           t.created_at, t.completed_at
                    FROM training_tasks t
                    JOIN deep_learning_models m ON t.model_id = m.id
                    WHERE m.user_id = ?
                    ORDER BY t.created_at DESC
                ''', (user_id,))
            else:
                cursor.execute('''
                    SELECT t.id, m.name, m.model_type, t.status, t.progress, 
                           t.created_at, t.completed_at
                    FROM training_tasks t
                    JOIN deep_learning_models m ON t.model_id = m.id
                    ORDER BY t.created_at DESC
                ''')
            
            tasks = []
            for row in cursor.fetchall():
                tasks.append({
                    'id': row[0],
                    'name': row[1],
                    'model_type': row[2],
                    'status': row[3],
                    'progress': row[4],
                    'created_at': row[5],
                    'completed_at': row[6]
                })
            
            conn.close()
            
            return {'success': True, 'tasks': tasks}
            
        except Exception as e:
            logger.error(f"❌ 获取训练任务失败: {e}")
            return {'success': False, 'error': str(e)}
    
    def get_system_info(self) -> Dict[str, Any]:
        """获取系统信息"""
        try:
            import sys
            import psutil
            
            info = {
                'python_version': sys.version.split()[0],
                'pytorch_version': torch.__version__ if TORCH_AVAILABLE else None,
                'tensorflow_version': tf.__version__ if TF_AVAILABLE else None,
                'cuda_version': torch.version.cuda if TORCH_AVAILABLE and torch.cuda.is_available() else None,
                'available_memory': round(psutil.virtual_memory().available / 1024**3, 1),
                'cpu_count': psutil.cpu_count(),
                'device': str(self.device)
            }
            
            return {'success': True, 'info': info}
            
        except Exception as e:
            logger.error(f"❌ 获取系统信息失败: {e}")
            return {'success': False, 'error': str(e)}

    def get_models(self, user_id: int = None) -> Dict[str, Any]:
        """获取模型列表"""
        try:
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()

            if user_id:
                cursor.execute('''
                    SELECT id, name, model_type, symbol, timeframe, status,
                           performance_metrics, created_at, completed_at
                    FROM deep_learning_models
                    WHERE user_id = ?
                    ORDER BY created_at DESC
                ''', (user_id,))
            else:
                cursor.execute('''
                    SELECT id, name, model_type, symbol, timeframe, status,
                           performance_metrics, created_at, completed_at
                    FROM deep_learning_models
                    ORDER BY created_at DESC
                ''')

            models = []
            for row in cursor.fetchall():
                performance = {}
                try:
                    if row[6]:  # performance_metrics
                        performance = json.loads(row[6])
                except:
                    pass

                models.append({
                    'id': row[0],
                    'name': row[1],
                    'model_type': row[2],
                    'symbol': row[3],
                    'timeframe': row[4],
                    'status': row[5],
                    'performance': performance,
                    'created_at': row[7],
                    'completed_at': row[8]
                })

            conn.close()

            return {'success': True, 'models': models}

        except Exception as e:
            logger.error(f"❌ 获取模型列表失败: {e}")
            return {'success': False, 'error': str(e)}

    def get_model_detail(self, model_id: str, user_id: int = None) -> Dict[str, Any]:
        """获取模型详情"""
        try:
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()

            if user_id:
                cursor.execute('''
                    SELECT id, name, model_type, symbol, timeframe, config,
                           training_history, performance_metrics, status,
                           created_at, completed_at, model_path
                    FROM deep_learning_models
                    WHERE id = ? AND user_id = ?
                ''', (model_id, user_id))
            else:
                cursor.execute('''
                    SELECT id, name, model_type, symbol, timeframe, config,
                           training_history, performance_metrics, status,
                           created_at, completed_at, model_path
                    FROM deep_learning_models
                    WHERE id = ?
                ''', (model_id,))

            result = cursor.fetchone()
            conn.close()

            if not result:
                return {'success': False, 'error': '模型不存在'}

            # 解析JSON数据
            config = {}
            training_history = {}
            performance = {}

            try:
                if result[5]:  # config
                    config = json.loads(result[5])
                if result[6]:  # training_history
                    training_history = json.loads(result[6])
                if result[7]:  # performance_metrics
                    performance = json.loads(result[7])
            except:
                pass

            model_detail = {
                'id': result[0],
                'name': result[1],
                'model_type': result[2],
                'symbol': result[3],
                'timeframe': result[4],
                'config': config,
                'training_history': training_history,
                'performance': performance,
                'status': result[8],
                'created_at': result[9],
                'completed_at': result[10],
                'model_path': result[11]
            }

            return {'success': True, 'model': model_detail}

        except Exception as e:
            logger.error(f"❌ 获取模型详情失败: {e}")
            return {'success': False, 'error': str(e)}

    def delete_model(self, model_id: str, user_id: int = None) -> Dict[str, Any]:
        """删除模型"""
        try:
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()

            # 首先获取模型信息
            if user_id:
                cursor.execute('''
                    SELECT model_path FROM deep_learning_models
                    WHERE id = ? AND user_id = ?
                ''', (model_id, user_id))
            else:
                cursor.execute('''
                    SELECT model_path FROM deep_learning_models
                    WHERE id = ?
                ''', (model_id,))

            result = cursor.fetchone()
            if not result:
                conn.close()
                return {'success': False, 'error': '模型不存在'}

            model_path = result[0]

            # 删除数据库记录
            if user_id:
                cursor.execute('''
                    DELETE FROM deep_learning_models
                    WHERE id = ? AND user_id = ?
                ''', (model_id, user_id))
            else:
                cursor.execute('''
                    DELETE FROM deep_learning_models
                    WHERE id = ?
                ''', (model_id,))

            # 删除相关的训练任务
            cursor.execute('''
                DELETE FROM training_tasks
                WHERE model_id = ?
            ''', (model_id,))

            conn.commit()
            conn.close()

            # 删除模型文件
            if model_path and os.path.exists(model_path):
                try:
                    os.remove(model_path)
                    logger.info(f"✅ 已删除模型文件: {model_path}")
                except Exception as e:
                    logger.warning(f"⚠️ 删除模型文件失败: {e}")

            logger.info(f"✅ 模型删除成功: {model_id}")
            return {'success': True, 'message': '模型删除成功'}

        except Exception as e:
            logger.error(f"❌ 删除模型失败: {e}")
            return {'success': False, 'error': str(e)}

    def pause_training(self, task_id: str) -> Dict[str, Any]:
        """暂停训练"""
        try:
            if task_id in self.training_control:
                self.training_control[task_id]['pause'] = True
                logger.info(f"⏸️ 训练暂停请求已发送: {task_id}")
                return {'success': True, 'message': '训练暂停请求已发送'}
            else:
                return {'success': False, 'error': '训练任务不存在或已完成'}
        except Exception as e:
            logger.error(f"❌ 暂停训练失败: {e}")
            return {'success': False, 'error': str(e)}

    def resume_training(self, task_id: str) -> Dict[str, Any]:
        """恢复训练"""
        try:
            if task_id in self.training_control:
                self.training_control[task_id]['pause'] = False
                logger.info(f"▶️ 训练恢复请求已发送: {task_id}")
                return {'success': True, 'message': '训练恢复请求已发送'}
            else:
                return {'success': False, 'error': '训练任务不存在或已完成'}
        except Exception as e:
            logger.error(f"❌ 恢复训练失败: {e}")
            return {'success': False, 'error': str(e)}

    def stop_training(self, task_id: str) -> Dict[str, Any]:
        """停止训练"""
        try:
            if task_id in self.training_control:
                self.training_control[task_id]['stop'] = True
                logger.info(f"🛑 训练停止请求已发送: {task_id}")

                # 更新数据库状态
                self._update_task_status(task_id, 'stopped')

                return {'success': True, 'message': '训练停止请求已发送'}
            else:
                return {'success': False, 'error': '训练任务不存在或已完成'}
        except Exception as e:
            logger.error(f"❌ 停止训练失败: {e}")
            return {'success': False, 'error': str(e)}

    def get_training_control_status(self, task_id: str) -> Dict[str, Any]:
        """获取训练控制状态"""
        try:
            if task_id in self.training_control:
                control_state = self.training_control[task_id]
                return {
                    'success': True,
                    'control': {
                        'is_paused': control_state.get('pause', False),
                        'is_stopped': control_state.get('stop', False),
                        'can_control': True
                    }
                }
            else:
                return {
                    'success': True,
                    'control': {
                        'is_paused': False,
                        'is_stopped': False,
                        'can_control': False
                    }
                }
        except Exception as e:
            logger.error(f"❌ 获取训练控制状态失败: {e}")
            return {'success': False, 'error': str(e)}

    def _calculate_training_days(self, data_config: Dict[str, Any], fallback_config: Dict[str, Any]) -> int:
        """计算训练数据天数"""
        try:
            mode = data_config.get('mode', 'days')

            if mode == 'days':
                # 按天数模式
                days = data_config.get('training_days', fallback_config.get('training_days', 365))
                logger.info(f"📅 使用天数模式: {days}天")
                return days

            elif mode == 'range':
                # 日期范围模式
                start_date_str = data_config.get('start_date')
                end_date_str = data_config.get('end_date')

                if start_date_str and end_date_str:
                    start_date = datetime.strptime(start_date_str, '%Y-%m-%d')
                    end_date = datetime.strptime(end_date_str, '%Y-%m-%d')
                    days = (end_date - start_date).days

                    logger.info(f"📅 使用日期范围模式: {start_date_str} 到 {end_date_str} ({days}天)")
                    return max(days, 1)  # 至少1天
                else:
                    logger.warning("⚠️ 日期范围模式缺少日期参数，使用默认值")
                    return fallback_config.get('training_days', 365)

            elif mode == 'preset':
                # 预设范围模式
                preset = data_config.get('preset_range')
                days = self._get_preset_days(preset)

                logger.info(f"📅 使用预设范围模式: {preset} ({days}天)")
                return days

            else:
                logger.warning(f"⚠️ 未知的日期模式: {mode}，使用默认值")
                return fallback_config.get('training_days', 365)

        except Exception as e:
            logger.error(f"❌ 计算训练天数失败: {e}")
            return fallback_config.get('training_days', 365)

    def _get_preset_days(self, preset: str) -> int:
        """获取预设范围的天数"""
        preset_days = {
            '1m': 30,
            '3m': 90,
            '6m': 180,
            '1y': 365,
            '2y': 730,
            '3y': 1095,
            '5y': 1825,
            'ytd': self._get_ytd_days(),
            'last_year': 365,
            'bull_market': 730,  # 2020-2021大约2年
            'bear_market': 365,  # 2022年
            'covid_period': 275  # 2020年3月-12月大约9个月
        }

        return preset_days.get(preset, 365)

    def _get_ytd_days(self) -> int:
        """获取今年至今的天数"""
        today = datetime.now()
        year_start = datetime(today.year, 1, 1)
        return (today - year_start).days + 1

    def _get_date_range_info(self, data_config: Dict[str, Any]) -> Dict[str, Any]:
        """获取日期范围信息（用于日志和调试）"""
        try:
            mode = data_config.get('mode', 'days')

            if mode == 'days':
                days = data_config.get('training_days', 365)
                end_date_str = data_config.get('end_date')

                if end_date_str:
                    end_date = datetime.strptime(end_date_str, '%Y-%m-%d')
                else:
                    end_date = datetime.now()

                start_date = end_date - timedelta(days=days)

                return {
                    'start_date': start_date.strftime('%Y-%m-%d'),
                    'end_date': end_date.strftime('%Y-%m-%d'),
                    'days': days,
                    'mode': 'days'
                }

            elif mode == 'range':
                return {
                    'start_date': data_config.get('start_date'),
                    'end_date': data_config.get('end_date'),
                    'days': self._calculate_training_days(data_config, {}),
                    'mode': 'range'
                }

            elif mode == 'preset':
                preset = data_config.get('preset_range')
                days = self._get_preset_days(preset)
                end_date = datetime.now()
                start_date = end_date - timedelta(days=days)

                return {
                    'start_date': start_date.strftime('%Y-%m-%d'),
                    'end_date': end_date.strftime('%Y-%m-%d'),
                    'days': days,
                    'mode': 'preset',
                    'preset': preset
                }

        except Exception as e:
            logger.error(f"❌ 获取日期范围信息失败: {e}")

        # 默认返回
        return {
            'start_date': (datetime.now() - timedelta(days=365)).strftime('%Y-%m-%d'),
            'end_date': datetime.now().strftime('%Y-%m-%d'),
            'days': 365,
            'mode': 'default'
        }

    def _training_callback(self, task_id: str, status: str, progress: float,
                          train_loss: float, val_loss: float, error: str = None):
        """训练进度回调函数（供外部系统订阅）"""
        # 实际项目中这里会连接消息队列或WebSocket
        # 简化版：直接打印日志
        log_msg = f"📢 训练回调: 任务={task_id}, 状态={status}, 进度={progress}%"
        if status == "progress":
            log_msg += f", 训练损失={train_loss:.4f}, 验证损失={val_loss:.4f}"
        elif status == "error":
            log_msg += f", 错误={error}"
        
        logger.info(log_msg)
        
        # 实际实现示例（伪代码）：
        # websocket.broadcast(f"training/{task_id}", {
        #     'status': status,
        #     'progress': progress,
        #     'train_loss': train_loss,
        #     'val_loss': val_loss,
        #     'error': error
        # })

# 全局深度学习服务实例
deep_learning_service = DeepLearningService()
