{% extends "base.html" %}

{% block title %}模型推理{% endblock %}

{% block content %}
<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center mb-4">
                <h1 class="h3 mb-0 text-gray-800">
                    <i class="fas fa-magic text-primary me-2"></i>
                    深度学习模型推理
                </h1>
                <a href="{{ url_for('deep_learning_dashboard') }}" class="btn btn-outline-secondary">
                    <i class="fas fa-arrow-left me-1"></i>返回仪表板
                </a>
            </div>
        </div>
    </div>

    <div class="row">
        <!-- 推理配置 -->
        <div class="col-xl-8 col-lg-7">
            <div class="card shadow mb-4">
                <div class="card-header py-3">
                    <h6 class="m-0 font-weight-bold text-primary">
                        <i class="fas fa-cog me-2"></i>推理配置
                    </h6>
                </div>
                <div class="card-body">
                    <form id="inferenceForm">
                        <!-- 模型选择 -->
                        <div class="row mb-3">
                            <div class="col-md-6">
                                <label class="form-label">选择模型</label>
                                <select class="form-select" id="modelSelect" required>
                                    <option value="">请选择已训练的模型</option>
                                </select>
                                <div class="form-text">只显示训练完成的模型</div>
                            </div>
                            <div class="col-md-6">
                                <label class="form-label">交易品种</label>
                                <select class="form-select" id="symbol">
                                    <option value="XAUUSD">XAU/USD (黄金)</option>
                                    <option value="EURUSD">EUR/USD</option>
                                    <option value="GBPUSD">GBP/USD</option>
                                    <option value="USDJPY">USD/JPY</option>
                                </select>
                            </div>
                        </div>

                        <!-- 数据配置 -->
                        <div class="row mb-3">
                            <div class="col-md-4">
                                <label class="form-label">时间框架</label>
                                <select class="form-select" id="timeframe">
                                    <option value="1m">1分钟</option>
                                    <option value="5m">5分钟</option>
                                    <option value="15m">15分钟</option>
                                    <option value="1h" selected>1小时</option>
                                    <option value="4h">4小时</option>
                                    <option value="1d">1天</option>
                                </select>
                            </div>
                            <div class="col-md-4">
                                <label class="form-label">数据点数</label>
                                <input type="number" class="form-control" id="dataPoints" value="100" min="50" max="1000">
                                <div class="form-text">用于推理的历史数据点数</div>
                            </div>
                            <div class="col-md-4">
                                <label class="form-label">推理模式</label>
                                <select class="form-select" id="inferenceMode" onchange="toggleTimeRangeVisibility()">
                                    <option value="single">单次推理</option>
                                    <option value="batch">批量推理</option>
                                    <option value="realtime">实时推理</option>
                                </select>
                                <div class="form-text">
                                    <small id="inferenceModeHelp" class="text-muted">选择推理执行方式</small>
                                </div>
                            </div>
                        </div>

                        <!-- 推理时间范围 -->
                        <div class="mb-3" id="timeRangeSection">
                            <label class="form-label">
                                <i class="fas fa-calendar-alt me-1"></i>推理时间范围
                                <small class="text-muted">(对指定时间范围内的MT5真实市场数据进行推理)</small>
                            </label>
                            <div class="row">
                                <div class="col-md-4">
                                    <label class="form-label">开始日期</label>
                                    <input type="date" class="form-control" id="startDate" required>
                                </div>
                                <div class="col-md-4">
                                    <label class="form-label">结束日期</label>
                                    <input type="date" class="form-control" id="endDate" required>
                                </div>
                                <div class="col-md-4">
                                    <label class="form-label">时间范围预设</label>
                                    <select class="form-select" id="timeRangePreset" onchange="applyTimeRangePreset()">
                                        <option value="">自定义时间范围</option>
                                        <option value="last_week">最近一周</option>
                                        <option value="last_month">最近一个月</option>
                                        <option value="last_3months">最近三个月</option>
                                        <option value="last_6months">最近六个月</option>
                                        <option value="last_year">最近一年</option>
                                    </select>
                                </div>
                            </div>
                            <div class="form-text mt-2">
                                <i class="fas fa-info-circle text-info"></i>
                                推理将基于MT5服务器的真实历史价格数据，确保结果的准确性和可靠性
                            </div>
                        </div>

                        <!-- 高级选项 -->
                        <div class="row mb-4">
                            <div class="col-md-6">
                                <div class="form-check form-switch">
                                    <input class="form-check-input" type="checkbox" id="useGPU" checked>
                                    <label class="form-check-label" for="useGPU">
                                        使用GPU加速推理
                                    </label>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="form-check form-switch">
                                    <input class="form-check-input" type="checkbox" id="showConfidence" checked>
                                    <label class="form-check-label" for="showConfidence">
                                        显示置信度
                                    </label>
                                </div>
                            </div>
                        </div>

                        <!-- 操作按钮 -->
                        <div class="d-flex gap-2">
                            <button type="submit" class="btn btn-primary" id="startInferenceBtn">
                                <i class="fas fa-play me-1"></i>开始推理
                            </button>
                            <button type="button" class="btn btn-success" id="startBacktestBtn" onclick="startBacktest()">
                                <i class="fas fa-chart-line me-1"></i>交易回测
                            </button>
                            <button type="button" class="btn btn-secondary" onclick="resetForm()">
                                <i class="fas fa-undo me-1"></i>重置
                            </button>
                            <button type="button" class="btn btn-info" onclick="loadSampleData()">
                                <i class="fas fa-download me-1"></i>加载示例数据
                            </button>
                        </div>
                    </form>
                </div>
            </div>

            <!-- 推理结果 -->
            <div class="card shadow mb-4" id="resultsCard" style="display: none;">
                <div class="card-header py-3">
                    <h6 class="m-0 font-weight-bold text-primary">
                        <i class="fas fa-chart-line me-2"></i>推理结果
                    </h6>
                </div>
                <div class="card-body">
                    <div id="inferenceResults">
                        <!-- 推理结果将在这里显示 -->
                    </div>
                </div>
            </div>

            <!-- 回测结果 -->
            <div class="card shadow mb-4" id="backtestCard" style="display: none;">
                <div class="card-header py-3">
                    <h6 class="m-0 font-weight-bold text-success">
                        <i class="fas fa-chart-bar me-2"></i>交易回测结果
                    </h6>
                </div>
                <div class="card-body">
                    <!-- 回测统计 -->
                    <div class="row mb-4" id="backtestStats">
                        <!-- 回测统计将在这里显示 -->
                    </div>

                    <!-- 回测详细结果 -->
                    <div id="backtestResults">
                        <!-- 回测详细结果将在这里显示 -->
                    </div>
                </div>
            </div>
        </div>

        <!-- 模型信息和状态 -->
        <div class="col-xl-4 col-lg-5">
            <!-- 选中模型信息 -->
            <div class="card shadow mb-4">
                <div class="card-header py-3">
                    <h6 class="m-0 font-weight-bold text-primary">
                        <i class="fas fa-info-circle me-2"></i>模型信息
                    </h6>
                </div>
                <div class="card-body">
                    <div id="modelInfo">
                        <p class="text-muted text-center py-3">请先选择一个模型</p>
                    </div>
                </div>
            </div>

            <!-- 推理状态 -->
            <div class="card shadow mb-4" id="inferenceStatusCard" style="display: none;">
                <div class="card-header py-3">
                    <h6 class="m-0 font-weight-bold text-primary">
                        <i class="fas fa-tachometer-alt me-2"></i>推理状态
                    </h6>
                </div>
                <div class="card-body">
                    <div id="inferenceStatus">
                        <!-- 推理状态将在这里显示 -->
                    </div>
                </div>
            </div>

            <!-- 快速操作 -->
            <div class="card shadow mb-4">
                <div class="card-header py-3">
                    <h6 class="m-0 font-weight-bold text-primary">
                        <i class="fas fa-bolt me-2"></i>快速操作
                    </h6>
                </div>
                <div class="card-body">
                    <div class="d-grid gap-2">
                        <button class="btn btn-outline-primary" onclick="loadLatestData()">
                            <i class="fas fa-sync me-2"></i>加载最新数据
                        </button>
                        <button class="btn btn-outline-success" onclick="exportResults()">
                            <i class="fas fa-download me-2"></i>导出结果
                        </button>
                        <button class="btn btn-outline-info" onclick="compareModels()">
                            <i class="fas fa-balance-scale me-2"></i>模型对比
                        </button>
                        <a href="{{ url_for('model_management') }}" class="btn btn-outline-warning">
                            <i class="fas fa-database me-2"></i>管理模型
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- 推理交易区域 -->
    <div class="row" id="tradingSection">
        <div class="col-12">
            <div class="card shadow mb-4">
                <div class="card-header py-3 d-flex justify-content-between align-items-center">
                    <h6 class="m-0 font-weight-bold text-primary">
                        <i class="fas fa-robot me-2"></i>AI推理交易
                        <small class="text-muted ms-2">基于深度学习模型推理结果执行实时交易</small>
                    </h6>
                    <div class="d-flex align-items-center">
                        <span class="badge bg-secondary me-2" id="mt5ConnectionStatus">MT5未连接</span>
                        <button type="button" class="btn btn-sm btn-outline-info" onclick="checkMT5Connection()">
                            <i class="fas fa-sync-alt"></i> 检查连接
                        </button>
                        <button type="button" class="btn btn-sm btn-outline-success ms-1" onclick="autoConnectMT5()">
                            <i class="fas fa-plug"></i> 自动连接
                        </button>
                    </div>
                </div>
                <div class="card-body">
                    <div class="row">
                        <!-- 交易配置 -->
                        <div class="col-md-6">
                            <h6 class="text-primary mb-3">
                                <i class="fas fa-cogs me-1"></i>交易配置
                            </h6>

                            <!-- 模型选择 -->
                            <div class="mb-3">
                                <label class="form-label">
                                    <i class="fas fa-brain me-1"></i>交易模型
                                    <small class="text-muted">(选择用于自动交易的深度学习模型)</small>
                                </label>
                                <select class="form-select" id="tradingModelSelect">
                                    <option value="">请选择交易模型...</option>
                                </select>
                                <div class="form-text" id="tradingModelInfo">
                                    <i class="fas fa-info-circle text-info"></i>
                                    选择一个训练完成的模型用于AI交易决策
                                </div>
                            </div>

                            <!-- 基础交易参数 -->
                            <div class="row mb-3">
                                <div class="col-md-6">
                                    <label class="form-label">交易手数</label>
                                    <input type="number" class="form-control" id="tradingLotSize"
                                           value="0.01" min="0.01" max="10" step="0.01">
                                    <div class="form-text">每次交易的手数大小</div>
                                </div>
                                <div class="col-md-6">
                                    <label class="form-label">最大持仓数</label>
                                    <input type="number" class="form-control" id="maxPositions"
                                           value="3" min="1" max="10" step="1">
                                    <div class="form-text">同时持有的最大仓位数</div>
                                </div>
                            </div>

                            <!-- 风险管理参数 -->
                            <div class="row mb-3">
                                <div class="col-md-6">
                                    <label class="form-label">止损点数 (pips)</label>
                                    <input type="number" class="form-control" id="stopLossPips"
                                           value="50" min="10" max="500" step="5">
                                    <div class="form-text">自动止损距离</div>
                                </div>
                                <div class="col-md-6">
                                    <label class="form-label">止盈点数 (pips)</label>
                                    <input type="number" class="form-control" id="takeProfitPips"
                                           value="100" min="10" max="1000" step="5">
                                    <div class="form-text">自动止盈距离</div>
                                </div>
                            </div>

                            <!-- 推理交易条件 -->
                            <div class="row mb-3">
                                <div class="col-md-6">
                                    <label class="form-label">最低置信度</label>
                                    <input type="number" class="form-control" id="minConfidence"
                                           value="0.75" min="0.5" max="0.99" step="0.05">
                                    <div class="form-text">执行交易的最低置信度</div>
                                </div>
                                <div class="col-md-6">
                                    <label class="form-label">推理间隔</label>
                                    <select class="form-select" id="inferenceInterval">
                                        <option value="auto">自动适配 (推荐)</option>
                                        <option value="30">30秒 (高频)</option>
                                        <option value="60">1分钟</option>
                                        <option value="300">5分钟</option>
                                        <option value="900">15分钟</option>
                                        <option value="1800">30分钟</option>
                                        <option value="3600">1小时</option>
                                    </select>
                                    <div class="form-text" id="inferenceIntervalHelp">
                                        自动适配将根据模型时间框架智能设置间隔
                                    </div>
                                </div>
                            </div>

                            <!-- 交易时间限制 -->
                            <div class="row mb-3">
                                <div class="col-md-6">
                                    <label class="form-label">交易开始时间</label>
                                    <input type="time" class="form-control" id="tradingStartTime" value="00:05">
                                </div>
                                <div class="col-md-6">
                                    <label class="form-label">交易结束时间</label>
                                    <input type="time" class="form-control" id="tradingEndTime" value="23:55">
                                </div>
                            </div>

                            <!-- 高级选项 -->
                            <div class="mb-3">
                                <div class="form-check form-switch mb-2">
                                    <input class="form-check-input" type="checkbox" id="enableAutoTrading">
                                    <label class="form-check-label" for="enableAutoTrading">
                                        <strong>启用自动交易</strong>
                                        <small class="text-muted d-block">基于推理结果自动执行交易</small>
                                    </label>
                                </div>
                                <div class="form-check form-switch mb-2">
                                    <input class="form-check-input" type="checkbox" id="enableTrailingStop">
                                    <label class="form-check-label" for="enableTrailingStop">
                                        移动止损
                                        <small class="text-muted d-block">盈利时自动调整止损位置</small>
                                    </label>
                                </div>
                                <div class="form-check form-switch mb-2">
                                    <input class="form-check-input" type="checkbox" id="enableNewsFilter" checked>
                                    <label class="form-check-label" for="enableNewsFilter">
                                        新闻过滤
                                        <small class="text-muted d-block">重要新闻时段暂停交易</small>
                                    </label>
                                </div>
                            </div>
                        </div>

                        <!-- 交易状态和控制 -->
                        <div class="col-md-6">
                            <h6 class="text-success mb-3">
                                <i class="fas fa-chart-bar me-1"></i>交易状态
                            </h6>

                            <!-- 实时市场数据 -->
                            <div class="card bg-light mb-3">
                                <div class="card-body p-3">
                                    <h6 class="card-title mb-2">
                                        <i class="fas fa-chart-line text-info me-1"></i>实时市场数据
                                    </h6>
                                    <div class="row text-center">
                                        <div class="col-4">
                                            <div class="text-muted small">买价</div>
                                            <div class="fw-bold text-success" id="currentBid">--</div>
                                        </div>
                                        <div class="col-4">
                                            <div class="text-muted small">卖价</div>
                                            <div class="fw-bold text-danger" id="currentAsk">--</div>
                                        </div>
                                        <div class="col-4">
                                            <div class="text-muted small">点差</div>
                                            <div class="fw-bold text-info" id="currentSpread">--</div>
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <!-- 最新推理结果 -->
                            <div class="card bg-light mb-3">
                                <div class="card-body p-3">
                                    <h6 class="card-title mb-2">
                                        <i class="fas fa-brain text-warning me-1"></i>最新推理结果
                                    </h6>
                                    <div id="latestInferenceResult">
                                        <div class="text-muted text-center">暂无推理结果</div>
                                    </div>
                                </div>
                            </div>

                            <!-- 交易统计 -->
                            <div class="row mb-3">
                                <div class="col-6">
                                    <div class="card bg-primary text-white">
                                        <div class="card-body p-2 text-center">
                                            <div class="small">今日交易</div>
                                            <div class="h5 mb-0" id="todayTrades">0</div>
                                        </div>
                                    </div>
                                </div>
                                <div class="col-6">
                                    <div class="card bg-success text-white">
                                        <div class="card-body p-2 text-center">
                                            <div class="small">当前持仓</div>
                                            <div class="h5 mb-0" id="currentPositions">0</div>
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <!-- 控制按钮 -->
                            <div class="d-grid gap-2">
                                <button type="button" class="btn btn-success" id="startTradingBtn" onclick="startAutoTrading()">
                                    <i class="fas fa-play me-1"></i>开始AI交易
                                </button>
                                <button type="button" class="btn btn-danger" id="stopTradingBtn" onclick="stopAutoTrading()" style="display: none;">
                                    <i class="fas fa-stop me-1"></i>停止AI交易
                                </button>
                                <button type="button" class="btn btn-warning" onclick="closeAllPositions()">
                                    <i class="fas fa-times-circle me-1"></i>平仓所有持仓
                                </button>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
let selectedModel = null;
let inferenceResults = null;

// 交易相关全局变量
let autoTradingActive = false;
let tradingInterval = null;
let mt5Connected = false;
let currentMarketData = null;
let selectedTradingModel = null;
let tradingStatistics = {
    todayTrades: 0,
    currentPositions: 0,
    totalProfit: 0
};

// 页面加载时初始化
document.addEventListener('DOMContentLoaded', function() {
    loadAvailableModels();
    loadTradingModels(); // 加载交易模型
    initializeDefaultTimeRange(); // 初始化默认时间范围
    toggleTimeRangeVisibility(); // 初始化时间范围显示状态
    checkMT5Connection(); // 检查MT5连接状态
    restoreAutoTradingState(); // 恢复自动交易状态

    // 监听推理间隔选择变化
    document.getElementById('inferenceInterval').addEventListener('change', function() {
        updateInferenceIntervalForModel();
    });

    // 监听交易模型选择变化
    document.getElementById('tradingModelSelect').addEventListener('change', onTradingModelChange);

    // 监听模型选择变化
    document.getElementById('modelSelect').addEventListener('change', function() {
        const modelId = this.value;
        updateInferenceIntervalForModel(); // 更新推理间隔
        if (modelId) {
            loadModelInfo(modelId);
        } else {
            clearModelInfo();
        }
    });
});

// 加载可用模型
async function loadAvailableModels() {
    try {
        const response = await fetch('/api/deep-learning/models');
        const data = await response.json();
        
        if (data.success) {
            const modelSelect = document.getElementById('modelSelect');
            modelSelect.innerHTML = '<option value="">请选择已训练的模型</option>';
            
            // 只显示训练完成的模型
            const completedModels = data.models.filter(model => model.status === 'completed');
            
            if (completedModels.length === 0) {
                modelSelect.innerHTML = '<option value="">暂无可用模型</option>';
                return;
            }
            
            completedModels.forEach(model => {
                const option = document.createElement('option');
                option.value = model.id;
                option.textContent = `${model.name} (${model.model_type.toUpperCase()})`;
                modelSelect.appendChild(option);
            });
            
            // 如果URL中有模型参数，自动选择
            const urlParams = new URLSearchParams(window.location.search);
            const modelParam = urlParams.get('model');
            if (modelParam) {
                modelSelect.value = modelParam;
                loadModelInfo(modelParam);
            }
        } else {
            showError('加载模型列表失败: ' + data.error);
        }
    } catch (error) {
        console.error('加载模型列表失败:', error);
        showError('加载模型列表失败: ' + error.message);
    }
}

// 加载模型信息
async function loadModelInfo(modelId) {
    try {
        const response = await fetch(`/api/deep-learning/models/${modelId}`);
        const data = await response.json();
        
        if (data.success) {
            selectedModel = data.model;
            displayModelInfo(selectedModel);
        } else {
            showError('加载模型信息失败: ' + data.error);
        }
    } catch (error) {
        console.error('加载模型信息失败:', error);
        showError('加载模型信息失败: ' + error.message);
    }
}

// 显示模型信息
function displayModelInfo(model) {
    const modelInfoElement = document.getElementById('modelInfo');
    
    const accuracy = model.performance?.accuracy ? (model.performance.accuracy * 100).toFixed(1) + '%' : 'N/A';
    const precision = model.performance?.precision ? (model.performance.precision * 100).toFixed(1) + '%' : 'N/A';
    
    modelInfoElement.innerHTML = `
        <div class="mb-3">
            <h6 class="text-primary">${model.name}</h6>
            <small class="text-muted">${model.id}</small>
        </div>
        
        <div class="row mb-2">
            <div class="col-6">
                <small class="text-muted">模型类型:</small><br>
                <span class="badge bg-info">${model.model_type.toUpperCase()}</span>
            </div>
            <div class="col-6">
                <small class="text-muted">交易品种:</small><br>
                <strong>${model.symbol}</strong>
            </div>
        </div>
        
        <div class="row mb-2">
            <div class="col-6">
                <small class="text-muted">时间框架:</small><br>
                <strong>${model.timeframe}</strong>
            </div>
            <div class="col-6">
                <small class="text-muted">准确率:</small><br>
                <strong class="text-success">${accuracy}</strong>
            </div>
        </div>
        
        <div class="row">
            <div class="col-6">
                <small class="text-muted">精确率:</small><br>
                <strong>${precision}</strong>
            </div>
            <div class="col-6">
                <small class="text-muted">创建时间:</small><br>
                <small>${formatDate(model.created_at)}</small>
            </div>
        </div>
    `;
    
    // 自动设置匹配的交易品种和时间框架
    document.getElementById('symbol').value = model.symbol;
    document.getElementById('timeframe').value = model.timeframe;
}

// 清除模型信息
function clearModelInfo() {
    selectedModel = null;
    document.getElementById('modelInfo').innerHTML = '<p class="text-muted text-center py-3">请先选择一个模型</p>';
}

// 提交推理表单
document.getElementById('inferenceForm').addEventListener('submit', async function(e) {
    e.preventDefault();
    
    if (!selectedModel) {
        showError('请先选择一个模型');
        return;
    }
    
    const inferenceMode = document.getElementById('inferenceMode').value;

    const formData = {
        model_id: selectedModel.id,
        symbol: document.getElementById('symbol').value,
        timeframe: document.getElementById('timeframe').value,
        data_points: parseInt(document.getElementById('dataPoints').value),
        inference_mode: inferenceMode,
        use_gpu: document.getElementById('useGPU').checked,
        show_confidence: document.getElementById('showConfidence').checked
    };

    // 只有非实时推理模式才需要时间范围参数
    if (inferenceMode !== 'realtime') {
        formData.start_date = document.getElementById('startDate').value;
        formData.end_date = document.getElementById('endDate').value;
    }
    
    try {
        const startBtn = document.getElementById('startInferenceBtn');
        startBtn.disabled = true;
        startBtn.innerHTML = '<i class="fas fa-spinner fa-spin me-1"></i>推理中...';
        
        // 显示推理状态
        showInferenceStatus('running');
        
        const response = await fetch('/api/deep-learning/inference', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json'
            },
            body: JSON.stringify(formData)
        });
        
        const result = await response.json();
        
        if (result.success) {
            inferenceResults = result;  // result本身就包含了完整的推理结果
            displayInferenceResults(result);  // 传递完整的result对象
            showInferenceStatus('completed');

            showSuccess('推理完成！');
        } else {
            showError('推理失败: ' + result.error);
            showInferenceStatus('failed');
        }
    } catch (error) {
        console.error('推理失败:', error);
        showError('推理失败: ' + error.message);
        showInferenceStatus('failed');
    } finally {
        const startBtn = document.getElementById('startInferenceBtn');
        startBtn.disabled = false;
        startBtn.innerHTML = '<i class="fas fa-play me-1"></i>开始推理';
    }
});

// 显示推理状态
function showInferenceStatus(status) {
    const statusCard = document.getElementById('inferenceStatusCard');
    const statusElement = document.getElementById('inferenceStatus');
    
    statusCard.style.display = 'block';
    
    const statusInfo = {
        'running': {
            icon: 'fas fa-spinner fa-spin text-primary',
            text: '推理进行中...',
            class: 'text-primary'
        },
        'completed': {
            icon: 'fas fa-check-circle text-success',
            text: '推理完成',
            class: 'text-success'
        },
        'failed': {
            icon: 'fas fa-times-circle text-danger',
            text: '推理失败',
            class: 'text-danger'
        }
    };
    
    const info = statusInfo[status] || statusInfo['running'];
    
    statusElement.innerHTML = `
        <div class="text-center">
            <i class="${info.icon} fa-2x mb-2"></i>
            <p class="${info.class}">${info.text}</p>
        </div>
    `;
}

// 显示推理结果
function displayInferenceResults(results) {
    const resultsCard = document.getElementById('resultsCard');
    const resultsElement = document.getElementById('inferenceResults');
    const tradingSection = document.getElementById('tradingSection');

    resultsCard.style.display = 'block';

    if (results && results.results && results.results.length > 0) {
        // 显示推理结果
        let resultsHtml = '<div class="mb-3">';

        results.results.forEach((result, index) => {
            const predictionClass = result.prediction === 'BUY' ? 'text-success' :
                                  result.prediction === 'SELL' ? 'text-danger' : 'text-warning';

            // 为HOLD预测提供特殊的显示逻辑
            const targetDisplay = result.prediction === 'HOLD' ?
                `当前: ${result.current_price} (观望)` :
                `目标: ${result.price_target}`;

            const predictionIcon = result.prediction === 'BUY' ? '📈' :
                                 result.prediction === 'SELL' ? '📉' : '⏸️';

            resultsHtml += `
                <div class="border-bottom pb-2 mb-2">
                    <div class="d-flex justify-content-between align-items-center">
                        <div>
                            <span class="badge bg-secondary">#${index + 1}</span>
                            <strong class="${predictionClass}">
                                ${predictionIcon} ${result.prediction}
                            </strong>
                        </div>
                        <div class="text-end">
                            <div class="small text-muted">${result.timestamp.substring(0, 19)}</div>
                            <div class="fw-bold">${targetDisplay}</div>
                        </div>
                    </div>
                    ${result.confidence ? `
                        <div class="mt-1">
                            <small class="text-muted">置信度: </small>
                            <span class="badge ${result.confidence > 0.8 ? 'bg-success' : result.confidence > 0.6 ? 'bg-warning' : 'bg-secondary'}">
                                ${(result.confidence * 100).toFixed(1)}%
                            </span>
                        </div>
                    ` : ''}
                    ${result.analysis && result.analysis.reason ? `
                        <div class="mt-1">
                            <small class="text-muted d-block">${result.analysis.reason}</small>
                        </div>
                    ` : ''}
                </div>
            `;
        });

        resultsHtml += '</div>';
        resultsElement.innerHTML = resultsHtml;

        // 更新最新推理结果到交易区域
        updateLatestInferenceResult(results.results[results.results.length - 1]);

    } else {
        resultsElement.innerHTML = `
            <div class="alert alert-warning">
                <h6>推理完成</h6>
                <p>未获得有效的推理结果，请检查模型状态或重新尝试。</p>
            </div>
        `;
    }
}

// 应用时间范围预设
function applyTimeRangePreset() {
    const preset = document.getElementById('timeRangePreset').value;
    const startDateInput = document.getElementById('startDate');
    const endDateInput = document.getElementById('endDate');

    if (!preset) return;

    const now = new Date();
    const endDate = new Date(now);
    let startDate = new Date(now);

    switch (preset) {
        case 'last_week':
            startDate.setDate(now.getDate() - 7);
            break;
        case 'last_month':
            startDate.setMonth(now.getMonth() - 1);
            break;
        case 'last_3months':
            startDate.setMonth(now.getMonth() - 3);
            break;
        case 'last_6months':
            startDate.setMonth(now.getMonth() - 6);
            break;
        case 'last_year':
            startDate.setFullYear(now.getFullYear() - 1);
            break;
    }

    // 格式化为YYYY-MM-DD格式
    startDateInput.value = startDate.toISOString().split('T')[0];
    endDateInput.value = endDate.toISOString().split('T')[0];
}

// 格式化日期
function formatDate(dateString) {
    const date = new Date(dateString);
    return date.toLocaleString('zh-CN');
}

// 初始化默认时间范围
function initializeDefaultTimeRange() {
    const now = new Date();
    const endDate = new Date(now);
    const startDate = new Date(now);
    startDate.setMonth(now.getMonth() - 1); // 默认最近一个月

    document.getElementById('startDate').value = startDate.toISOString().split('T')[0];
    document.getElementById('endDate').value = endDate.toISOString().split('T')[0];
}

// 根据推理模式切换时间范围区域的显示
function toggleTimeRangeVisibility() {
    const inferenceMode = document.getElementById('inferenceMode').value;
    const timeRangeSection = document.getElementById('timeRangeSection');
    const inferenceModeHelp = document.getElementById('inferenceModeHelp');

    if (inferenceMode === 'realtime') {
        // 实时推理模式：隐藏时间范围选择
        timeRangeSection.style.display = 'none';
        inferenceModeHelp.innerHTML = '<i class="fas fa-broadcast-tower text-success"></i> 实时推理：基于当前最新的市场数据进行推理';
        inferenceModeHelp.className = 'text-success';
    } else {
        // 单次推理或批量推理模式：显示时间范围选择
        timeRangeSection.style.display = 'block';

        if (inferenceMode === 'single') {
            inferenceModeHelp.innerHTML = '<i class="fas fa-play-circle text-primary"></i> 单次推理：对指定时间范围进行一次性推理';
            inferenceModeHelp.className = 'text-primary';
        } else if (inferenceMode === 'batch') {
            inferenceModeHelp.innerHTML = '<i class="fas fa-layer-group text-warning"></i> 批量推理：对指定时间范围进行批量推理分析';
            inferenceModeHelp.className = 'text-warning';
        } else {
            inferenceModeHelp.innerHTML = '选择推理执行方式';
            inferenceModeHelp.className = 'text-muted';
        }
    }
}

// 重置表单
function resetForm() {
    document.getElementById('inferenceForm').reset();
    document.getElementById('resultsCard').style.display = 'none';
    document.getElementById('inferenceStatusCard').style.display = 'none';
    clearModelInfo();
    inferenceResults = null;
}

// 加载示例数据
function loadSampleData() {
    showInfo('示例数据加载功能正在开发中');
}

// 加载最新数据
function loadLatestData() {
    showInfo('最新数据加载功能正在开发中');
}

// 导出结果
function exportResults() {
    if (!inferenceResults) {
        showError('没有可导出的结果');
        return;
    }
    showInfo('结果导出功能正在开发中');
}

// 模型对比
function compareModels() {
    showInfo('模型对比功能正在开发中');
}

// 显示成功消息
function showSuccess(message) {
    alert('✅ ' + message);
}

// 显示错误消息
function showError(message) {
    alert('❌ ' + message);
}

// 显示信息消息
function showInfo(message) {
    alert('ℹ️ ' + message);
}

// ==================== 回测相关函数 ====================

// 开始交易回测
async function startBacktest() {
    const selectedModel = getSelectedModel();
    if (!selectedModel) {
        showError('请先选择一个模型');
        return;
    }

    const formData = getFormData();
    if (!formData) {
        showError('请填写完整的推理参数');
        return;
    }

    try {
        showInferenceStatus('running');
        document.getElementById('startBacktestBtn').disabled = true;

        console.log('🔄 开始交易回测...');

        const backtestData = {
            model_id: selectedModel.id,
            symbol: formData.symbol,
            timeframe: formData.timeframe,
            start_date: formData.start_date,
            end_date: formData.end_date,
            initial_balance: 10000, // 初始资金
            lot_size: 0.01,
            stop_loss_pips: 50,
            take_profit_pips: 100,
            min_confidence: 0.7
        };

        const response = await fetch('/api/deep-learning/inference-backtest', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json'
            },
            body: JSON.stringify(backtestData)
        });

        const result = await response.json();

        if (result.success) {
            displayBacktestResults(result);
            showInferenceStatus('completed');
            showSuccess('回测完成！');
        } else {
            showError(`回测失败: ${result.error}`);
            showInferenceStatus('error');
        }

    } catch (error) {
        console.error('回测失败:', error);
        showError(`回测失败: ${error.message}`);
        showInferenceStatus('error');
    } finally {
        document.getElementById('startBacktestBtn').disabled = false;
    }
}

// 显示回测结果
function displayBacktestResults(results) {
    const backtestCard = document.getElementById('backtestCard');
    const backtestStats = document.getElementById('backtestStats');
    const backtestResults = document.getElementById('backtestResults');

    // 显示回测卡片
    backtestCard.style.display = 'block';

    // 显示统计信息
    const stats = results.statistics;
    backtestStats.innerHTML = `
        <div class="col-md-3">
            <div class="card bg-primary text-white">
                <div class="card-body">
                    <div class="d-flex justify-content-between">
                        <div>
                            <div class="small">总收益</div>
                            <div class="h5 mb-0">${stats.total_return.toFixed(2)}%</div>
                        </div>
                        <div class="align-self-center">
                            <i class="fas fa-chart-line fa-2x"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="card bg-success text-white">
                <div class="card-body">
                    <div class="d-flex justify-content-between">
                        <div>
                            <div class="small">胜率</div>
                            <div class="h5 mb-0">${stats.win_rate.toFixed(1)}%</div>
                        </div>
                        <div class="align-self-center">
                            <i class="fas fa-trophy fa-2x"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="card bg-info text-white">
                <div class="card-body">
                    <div class="d-flex justify-content-between">
                        <div>
                            <div class="small">总交易</div>
                            <div class="h5 mb-0">${stats.total_trades}</div>
                        </div>
                        <div class="align-self-center">
                            <i class="fas fa-exchange-alt fa-2x"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="card bg-warning text-white">
                <div class="card-body">
                    <div class="d-flex justify-content-between">
                        <div>
                            <div class="small">最大回撤</div>
                            <div class="h5 mb-0">${stats.max_drawdown.toFixed(2)}%</div>
                        </div>
                        <div class="align-self-center">
                            <i class="fas fa-arrow-down fa-2x"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    `;

    // 显示详细交易记录
    let tradesHtml = `
        <h6 class="mb-3">交易记录</h6>
        <div class="table-responsive">
            <table class="table table-striped">
                <thead>
                    <tr>
                        <th>时间</th>
                        <th>预测</th>
                        <th>置信度</th>
                        <th>入场价</th>
                        <th>出场价</th>
                        <th>盈亏</th>
                        <th>累计余额</th>
                    </tr>
                </thead>
                <tbody>
    `;

    results.trades.forEach(trade => {
        const profitClass = trade.profit > 0 ? 'text-success' : trade.profit < 0 ? 'text-danger' : 'text-muted';
        const predictionClass = trade.prediction === 'BUY' ? 'text-success' : 'text-danger';

        tradesHtml += `
            <tr>
                <td>${trade.timestamp.substring(0, 19)}</td>
                <td><span class="badge bg-secondary ${predictionClass}">${trade.prediction}</span></td>
                <td>${(trade.confidence * 100).toFixed(1)}%</td>
                <td>${trade.entry_price.toFixed(5)}</td>
                <td>${trade.exit_price.toFixed(5)}</td>
                <td class="${profitClass}">$${trade.profit.toFixed(2)}</td>
                <td>$${trade.balance.toFixed(2)}</td>
            </tr>
        `;
    });

    tradesHtml += `
                </tbody>
            </table>
        </div>
    `;

    backtestResults.innerHTML = tradesHtml;
}

// 格式化日期显示
function formatDate(dateString) {
    if (!dateString) return 'N/A';

    try {
        const date = new Date(dateString);
        return date.toLocaleString('zh-CN', {
            year: 'numeric',
            month: '2-digit',
            day: '2-digit',
            hour: '2-digit',
            minute: '2-digit'
        });
    } catch (e) {
        return dateString;
    }
}

// ==================== 交易相关函数 ====================

// 加载可用的交易模型
async function loadTradingModels() {
    try {
        const response = await fetch('/api/deep-learning/models');
        const data = await response.json();

        const tradingModelSelect = document.getElementById('tradingModelSelect');
        const tradingModelInfo = document.getElementById('tradingModelInfo');

        if (data.success && data.models) {
            // 清空现有选项
            tradingModelSelect.innerHTML = '<option value="">请选择交易模型...</option>';

            // 只显示训练完成的模型
            const completedModels = data.models.filter(model => model.status === 'completed');

            if (completedModels.length > 0) {
                completedModels.forEach(model => {
                    const option = document.createElement('option');
                    option.value = model.id;
                    option.textContent = `${model.name} (${model.model_type.toUpperCase()}, ${model.symbol}, ${model.timeframe})`;
                    option.dataset.model = JSON.stringify(model);
                    tradingModelSelect.appendChild(option);
                });

                tradingModelInfo.innerHTML = `
                    <i class="fas fa-check-circle text-success"></i>
                    找到 ${completedModels.length} 个可用的交易模型
                `;
                tradingModelInfo.className = 'form-text text-success';
            } else {
                tradingModelInfo.innerHTML = `
                    <i class="fas fa-exclamation-triangle text-warning"></i>
                    没有找到训练完成的模型，请先训练模型
                `;
                tradingModelInfo.className = 'form-text text-warning';
            }
        } else {
            tradingModelInfo.innerHTML = `
                <i class="fas fa-times-circle text-danger"></i>
                加载模型失败: ${data.error || '未知错误'}
            `;
            tradingModelInfo.className = 'form-text text-danger';
        }

    } catch (error) {
        console.error('加载交易模型失败:', error);
        document.getElementById('tradingModelInfo').innerHTML = `
            <i class="fas fa-times-circle text-danger"></i>
            加载模型失败: ${error.message}
        `;
        document.getElementById('tradingModelInfo').className = 'form-text text-danger';
    }
}

// 处理交易模型选择变化
function onTradingModelChange() {
    const tradingModelSelect = document.getElementById('tradingModelSelect');
    const selectedOption = tradingModelSelect.options[tradingModelSelect.selectedIndex];

    if (selectedOption.value && selectedOption.dataset.model) {
        selectedTradingModel = JSON.parse(selectedOption.dataset.model);

        // 更新模型信息显示
        updateTradingModelInfo(selectedTradingModel);

        // 更新推理间隔适配
        updateInferenceIntervalForTradingModel();

        console.log('选择交易模型:', selectedTradingModel);
    } else {
        selectedTradingModel = null;
        resetTradingModelInfo();
    }
}

// 更新交易模型信息显示
function updateTradingModelInfo(model) {
    const tradingModelInfo = document.getElementById('tradingModelInfo');

    tradingModelInfo.innerHTML = `
        <div class="d-flex align-items-center">
            <i class="fas fa-robot text-primary me-2"></i>
            <div>
                <div class="fw-bold text-primary">${model.name}</div>
                <div class="small text-muted">
                    ${model.model_type.toUpperCase()} | ${model.symbol} | ${model.timeframe} |
                    创建于 ${formatDate(model.created_at)}
                </div>
            </div>
        </div>
    `;
    tradingModelInfo.className = 'form-text';
}

// 重置交易模型信息
function resetTradingModelInfo() {
    const tradingModelInfo = document.getElementById('tradingModelInfo');
    tradingModelInfo.innerHTML = `
        <i class="fas fa-info-circle text-info"></i>
        选择一个训练完成的模型用于AI交易决策
    `;
    tradingModelInfo.className = 'form-text text-muted';
}

// 为交易模型更新推理间隔适配
function updateInferenceIntervalForTradingModel() {
    if (!selectedTradingModel) return;

    const intervalSelect = document.getElementById('inferenceInterval');
    const intervalHelp = document.getElementById('inferenceIntervalHelp');

    if (intervalSelect.value === 'auto') {
        const timeframe = selectedTradingModel.timeframe;
        const recommendedInterval = getRecommendedInterval(timeframe);

        // 更新帮助文本显示推荐间隔
        intervalHelp.innerHTML = `
            <i class="fas fa-magic text-primary"></i>
            自动适配: ${timeframe} 模型推荐间隔 ${formatInterval(recommendedInterval)}
        `;
        intervalHelp.className = 'form-text text-primary';
    }
}

// 更新最新推理结果到交易区域
function updateLatestInferenceResult(result) {
    const latestResultElement = document.getElementById('latestInferenceResult');

    if (result) {
        const predictionClass = result.prediction === 'BUY' ? 'text-success' :
                              result.prediction === 'SELL' ? 'text-danger' : 'text-warning';

        const predictionIcon = result.prediction === 'BUY' ? '📈' :
                             result.prediction === 'SELL' ? '📉' : '⏸️';

        // 为HOLD预测提供特殊的显示逻辑
        const targetDisplay = result.prediction === 'HOLD' ?
            `当前: ${result.current_price} (建议观望)` :
            `目标: ${result.price_target}`;

        // 添加交易建议
        const tradingAdvice = result.prediction === 'HOLD' ?
            '<div class="small text-muted mt-1">💡 市场趋势不明显，建议暂时观望</div>' :
            result.analysis?.reason ?
            `<div class="small text-muted mt-1">💡 ${result.analysis.reason}</div>` : '';

        latestResultElement.innerHTML = `
            <div class="d-flex justify-content-between align-items-center">
                <div>
                    <div class="fw-bold ${predictionClass}">
                        ${predictionIcon} ${result.prediction}
                    </div>
                    <div class="small text-muted">${result.timestamp.substring(11, 19)}</div>
                </div>
                <div class="text-end">
                    <div class="fw-bold">${targetDisplay}</div>
                    ${result.confidence ? `
                        <div class="small">
                            <span class="badge ${result.confidence > 0.8 ? 'bg-success' : result.confidence > 0.6 ? 'bg-warning' : 'bg-secondary'}">
                                ${(result.confidence * 100).toFixed(1)}%
                            </span>
                        </div>
                    ` : ''}
                </div>
            </div>
            ${tradingAdvice}
        `;
    } else {
        latestResultElement.innerHTML = '<div class="text-muted">暂无推理结果</div>';
    }
}

// 恢复自动交易状态
async function restoreAutoTradingState() {
    try {
        console.log('🔄 检查自动交易状态...');

        const response = await fetch('/api/deep-learning/auto-trading/status');
        const result = await response.json();

        if (result.success && result.active) {
            console.log('✅ 发现活跃的自动交易会话，正在恢复...');

            // 恢复交易状态
            autoTradingActive = true;

            // 恢复交易模型
            if (result.model_info) {
                // 等待交易模型加载完成
                setTimeout(() => {
                    const modelSelect = document.getElementById('tradingModelSelect');
                    if (modelSelect) {
                        modelSelect.value = result.model_info.id;
                        onTradingModelChange(); // 触发模型选择事件
                    }
                }, 1000);
            }

            // 更新UI状态
            document.getElementById('startTradingBtn').style.display = 'none';
            document.getElementById('stopTradingBtn').style.display = 'block';
            document.getElementById('enableAutoTrading').checked = true;

            // 重新启动交易循环
            startTradingLoop();

            showSuccess('自动交易状态已恢复');
        } else {
            console.log('ℹ️ 没有活跃的自动交易会话');
        }

    } catch (error) {
        console.error('恢复自动交易状态失败:', error);
    }
}

// 检查MT5连接状态
async function checkMT5Connection() {
    try {
        const response = await fetch('/api/mt5/connection-status');
        const data = await response.json();

        const statusElement = document.getElementById('mt5ConnectionStatus');

        if (data.success && data.connected) {
            mt5Connected = true;
            statusElement.textContent = 'MT5已连接';
            statusElement.className = 'badge bg-success me-2';

            // 开始获取实时市场数据
            startMarketDataUpdates();
        } else {
            mt5Connected = false;
            statusElement.textContent = 'MT5未连接';
            statusElement.className = 'badge bg-danger me-2';

            // 停止市场数据更新
            stopMarketDataUpdates();
        }

    } catch (error) {
        console.error('检查MT5连接失败:', error);
        mt5Connected = false;
        document.getElementById('mt5ConnectionStatus').textContent = 'MT5连接错误';
        document.getElementById('mt5ConnectionStatus').className = 'badge bg-danger me-2';
    }
}

// 自动连接MT5
async function autoConnectMT5() {
    const statusElement = document.getElementById('mt5ConnectionStatus');

    try {
        // 显示连接中状态
        statusElement.textContent = 'MT5连接中...';
        statusElement.className = 'badge bg-warning me-2';

        console.log('🔌 开始自动连接MT5...');

        // 调用自动连接API
        const response = await fetch('/api/mt5/auto-connect', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json'
            }
        });

        const data = await response.json();

        if (data.success) {
            console.log('✅ MT5自动连接成功');
            showSuccess('MT5自动连接成功！');

            // 等待一下再检查连接状态
            setTimeout(() => {
                checkMT5Connection();
            }, 1000);

        } else {
            console.error('❌ MT5自动连接失败:', data.error);
            showError(`MT5自动连接失败: ${data.error}`);

            statusElement.textContent = 'MT5连接失败';
            statusElement.className = 'badge bg-danger me-2';
        }

    } catch (error) {
        console.error('❌ 自动连接MT5异常:', error);
        showError(`自动连接MT5异常: ${error.message}`);

        statusElement.textContent = 'MT5连接异常';
        statusElement.className = 'badge bg-danger me-2';
    }
}

// 开始市场数据更新
function startMarketDataUpdates() {
    if (!mt5Connected) return;

    // 立即获取一次数据
    updateMarketData();

    // 每5秒更新一次市场数据
    if (window.marketDataInterval) {
        clearInterval(window.marketDataInterval);
    }

    window.marketDataInterval = setInterval(updateMarketData, 5000);
}

// 停止市场数据更新
function stopMarketDataUpdates() {
    if (window.marketDataInterval) {
        clearInterval(window.marketDataInterval);
        window.marketDataInterval = null;
    }
}

// 更新市场数据
async function updateMarketData() {
    if (!mt5Connected || !selectedModel) return;

    try {
        const symbol = selectedModel.symbol || document.getElementById('symbol').value;
        const response = await fetch(`/api/mt5/market-data/${symbol}`);
        const data = await response.json();

        if (data.success && data.market_data) {
            currentMarketData = data.market_data;

            // 更新显示
            document.getElementById('currentBid').textContent = currentMarketData.bid.toFixed(5);
            document.getElementById('currentAsk').textContent = currentMarketData.ask.toFixed(5);
            document.getElementById('currentSpread').textContent =
                ((currentMarketData.ask - currentMarketData.bid) * 100000).toFixed(1);
        }

    } catch (error) {
        console.error('获取市场数据失败:', error);
    }
}

// 开始自动交易
async function startAutoTrading() {
    if (!mt5Connected) {
        showError('请先连接MT5');
        return;
    }

    if (!selectedTradingModel) {
        showError('请先选择一个交易模型');
        return;
    }

    const enableAutoTrading = document.getElementById('enableAutoTrading').checked;
    if (!enableAutoTrading) {
        showError('请先启用自动交易选项');
        return;
    }

    // 验证交易参数
    const tradingConfig = getTradingConfig();
    if (!validateTradingConfig(tradingConfig)) {
        return;
    }

    try {
        // 启动自动交易
        const response = await fetch('/api/deep-learning/auto-trading/start', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json'
            },
            body: JSON.stringify({
                model_id: selectedTradingModel.id,
                trading_config: tradingConfig
            })
        });

        const result = await response.json();

        if (result.success) {
            autoTradingActive = true;

            // 更新UI
            document.getElementById('startTradingBtn').style.display = 'none';
            document.getElementById('stopTradingBtn').style.display = 'block';

            // 开始定期推理和交易检查
            startTradingLoop();

            showSuccess('AI自动交易已启动');
        } else {
            showError('启动自动交易失败: ' + result.error);
        }

    } catch (error) {
        console.error('启动自动交易失败:', error);
        showError('启动自动交易失败: ' + error.message);
    }
}

// 停止自动交易
async function stopAutoTrading() {
    try {
        const response = await fetch('/api/deep-learning/auto-trading/stop', {
            method: 'POST'
        });

        const result = await response.json();

        if (result.success) {
            autoTradingActive = false;

            // 停止交易循环
            if (tradingInterval) {
                clearInterval(tradingInterval);
                tradingInterval = null;
            }

            // 更新UI
            document.getElementById('startTradingBtn').style.display = 'block';
            document.getElementById('stopTradingBtn').style.display = 'none';

            showSuccess('AI自动交易已停止');
        } else {
            showError('停止自动交易失败: ' + result.error);
        }

    } catch (error) {
        console.error('停止自动交易失败:', error);
        showError('停止自动交易失败: ' + error.message);
    }
}

// 更新推理间隔以适配模型时间框架
function updateInferenceIntervalForModel() {
    if (!selectedModel) return;

    const intervalSelect = document.getElementById('inferenceInterval');
    const intervalHelp = document.getElementById('inferenceIntervalHelp');

    if (intervalSelect.value === 'auto') {
        const timeframe = selectedModel.timeframe;
        const recommendedInterval = getRecommendedInterval(timeframe);

        // 更新帮助文本显示推荐间隔
        intervalHelp.innerHTML = `
            <i class="fas fa-magic text-primary"></i>
            自动适配: ${timeframe} 模型推荐间隔 ${formatInterval(recommendedInterval)}
        `;
        intervalHelp.className = 'form-text text-primary';
    } else {
        intervalHelp.innerHTML = '自动适配将根据模型时间框架智能设置间隔';
        intervalHelp.className = 'form-text text-muted';
    }
}

// 根据时间框架获取推荐的推理间隔
function getRecommendedInterval(timeframe) {
    const intervalMap = {
        '1m': 30,      // 1分钟K线 → 30秒推理
        '5m': 150,     // 5分钟K线 → 2.5分钟推理 (K线一半时间)
        '15m': 450,    // 15分钟K线 → 7.5分钟推理
        '30m': 900,    // 30分钟K线 → 15分钟推理
        '1h': 1800,    // 1小时K线 → 30分钟推理
        '4h': 7200,    // 4小时K线 → 2小时推理
        '1d': 21600    // 1天K线 → 6小时推理
    };

    return intervalMap[timeframe] || 300; // 默认5分钟
}

// 格式化间隔时间显示
function formatInterval(seconds) {
    if (seconds < 60) {
        return `${seconds}秒`;
    } else if (seconds < 3600) {
        const minutes = Math.floor(seconds / 60);
        const remainingSeconds = seconds % 60;
        return remainingSeconds > 0 ? `${minutes}分${remainingSeconds}秒` : `${minutes}分钟`;
    } else {
        const hours = Math.floor(seconds / 3600);
        const remainingMinutes = Math.floor((seconds % 3600) / 60);
        return remainingMinutes > 0 ? `${hours}小时${remainingMinutes}分钟` : `${hours}小时`;
    }
}

// 获取实际使用的推理间隔
function getActualInferenceInterval() {
    const intervalSelect = document.getElementById('inferenceInterval');

    if (intervalSelect.value === 'auto' && selectedModel) {
        return getRecommendedInterval(selectedModel.timeframe);
    } else {
        return parseInt(intervalSelect.value) || 300;
    }
}

// 获取交易配置
function getTradingConfig() {
    return {
        lot_size: parseFloat(document.getElementById('tradingLotSize').value),
        max_positions: parseInt(document.getElementById('maxPositions').value),
        stop_loss_pips: parseInt(document.getElementById('stopLossPips').value),
        take_profit_pips: parseInt(document.getElementById('takeProfitPips').value),
        min_confidence: parseFloat(document.getElementById('minConfidence').value),
        inference_interval: getActualInferenceInterval(), // 使用智能间隔
        trading_start_time: document.getElementById('tradingStartTime').value,
        trading_end_time: document.getElementById('tradingEndTime').value,
        enable_trailing_stop: document.getElementById('enableTrailingStop').checked,
        enable_news_filter: document.getElementById('enableNewsFilter').checked
    };
}

// 验证交易配置
function validateTradingConfig(config) {
    if (config.lot_size <= 0 || config.lot_size > 10) {
        showError('交易手数必须在0.01-10之间');
        return false;
    }

    if (config.max_positions <= 0 || config.max_positions > 10) {
        showError('最大持仓数必须在1-10之间');
        return false;
    }

    if (config.min_confidence < 0.5 || config.min_confidence > 0.99) {
        showError('最低置信度必须在0.5-0.99之间');
        return false;
    }

    return true;
}

// 开始交易循环
function startTradingLoop() {
    const intervalSeconds = getActualInferenceInterval();

    console.log(`🔄 启动交易循环，推理间隔: ${formatInterval(intervalSeconds)}`);

    tradingInterval = setInterval(async () => {
        if (!autoTradingActive) return;

        // 检查交易时间
        if (!isWithinTradingHours()) {
            console.log('当前不在交易时间内');
            return;
        }

        // 执行推理
        await performAutoInference();

        // 更新交易统计
        updateTradingStatistics();

    }, intervalSeconds * 1000);
}

// 检查是否在交易时间内
function isWithinTradingHours() {
    const now = new Date();
    const currentTime = now.getHours() * 60 + now.getMinutes();

    const startTime = document.getElementById('tradingStartTime').value.split(':');
    const endTime = document.getElementById('tradingEndTime').value.split(':');

    const startMinutes = parseInt(startTime[0]) * 60 + parseInt(startTime[1]);
    const endMinutes = parseInt(endTime[0]) * 60 + parseInt(endTime[1]);

    return currentTime >= startMinutes && currentTime <= endMinutes;
}

// 执行自动推理
async function performAutoInference() {
    if (!selectedTradingModel) return;

    try {
        const formData = {
            model_id: selectedTradingModel.id,
            symbol: selectedTradingModel.symbol,
            timeframe: selectedTradingModel.timeframe,
            inference_mode: 'realtime',
            data_points: 100,
            use_gpu: true,
            show_confidence: true
        };

        const response = await fetch('/api/deep-learning/inference', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json'
            },
            body: JSON.stringify(formData)
        });

        const result = await response.json();

        if (result.success && result.results && result.results.length > 0) {
            const latestResult = result.results[result.results.length - 1];

            // 更新最新推理结果显示
            updateLatestInferenceResult(latestResult);

            // 检查是否满足交易条件
            await checkTradingConditions(latestResult);
        }

    } catch (error) {
        console.error('自动推理失败:', error);
    }
}

// 检查交易条件并执行交易
async function checkTradingConditions(inferenceResult) {
    const minConfidence = parseFloat(document.getElementById('minConfidence').value);
    const maxPositions = parseInt(document.getElementById('maxPositions').value);

    // 检查置信度
    if (!inferenceResult.confidence || inferenceResult.confidence < minConfidence) {
        console.log(`置信度不足: ${inferenceResult.confidence} < ${minConfidence}`);
        return;
    }

    // 检查当前持仓数
    if (tradingStatistics.currentPositions >= maxPositions) {
        console.log(`已达到最大持仓数: ${tradingStatistics.currentPositions}`);
        return;
    }

    // 检查预测信号
    if (inferenceResult.prediction === 'HOLD') {
        console.log('推理结果为持有，不执行交易');
        return;
    }

    // 执行交易
    await executeTrade(inferenceResult);
}

// 执行交易
async function executeTrade(inferenceResult) {
    const tradingConfig = getTradingConfig();

    try {
        const tradeData = {
            symbol: selectedTradingModel.symbol,
            action: inferenceResult.prediction, // BUY or SELL
            lot_size: tradingConfig.lot_size,
            stop_loss_pips: tradingConfig.stop_loss_pips,
            take_profit_pips: tradingConfig.take_profit_pips,
            inference_result: inferenceResult,
            trading_config: tradingConfig
        };

        const response = await fetch('/api/deep-learning/execute-trade', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json'
            },
            body: JSON.stringify(tradeData)
        });

        const result = await response.json();

        if (result.success) {
            console.log('交易执行成功:', result);
            tradingStatistics.todayTrades++;
            tradingStatistics.currentPositions++;

            // 更新统计显示
            updateTradingStatistics();

            // 可以添加交易通知
            showSuccess(`AI交易执行成功: ${inferenceResult.prediction} ${tradingConfig.lot_size}手`);
        } else {
            console.error('交易执行失败:', result.error);
        }

    } catch (error) {
        console.error('执行交易失败:', error);
    }
}

// 更新交易统计
async function updateTradingStatistics() {
    try {
        const response = await fetch('/api/deep-learning/trading-statistics');
        const data = await response.json();

        if (data.success) {
            tradingStatistics = data.statistics;

            // 更新显示
            document.getElementById('todayTrades').textContent = tradingStatistics.todayTrades;
            document.getElementById('currentPositions').textContent = tradingStatistics.currentPositions;
        }

    } catch (error) {
        console.error('获取交易统计失败:', error);
    }
}

// 平仓所有持仓
async function closeAllPositions() {
    if (!mt5Connected) {
        showError('MT5未连接');
        return;
    }

    if (!confirm('确定要平仓所有持仓吗？')) {
        return;
    }

    try {
        const response = await fetch('/api/deep-learning/close-all-positions', {
            method: 'POST'
        });

        const result = await response.json();

        if (result.success) {
            tradingStatistics.currentPositions = 0;
            updateTradingStatistics();
            showSuccess('所有持仓已平仓');
        } else {
            showError('平仓失败: ' + result.error);
        }

    } catch (error) {
        console.error('平仓失败:', error);
        showError('平仓失败: ' + error.message);
    }
}

// 页面卸载时清理
window.addEventListener('beforeunload', function() {
    if (autoTradingActive) {
        stopAutoTrading();
    }

    if (window.marketDataInterval) {
        clearInterval(window.marketDataInterval);
    }

    if (tradingInterval) {
        clearInterval(tradingInterval);
    }
});
</script>
{% endblock %}
