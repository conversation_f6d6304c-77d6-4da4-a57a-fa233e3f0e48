#!/usr/bin/env python3
"""
测试深度学习训练进度修复效果
"""

import requests
import json
import time
import sqlite3
from datetime import datetime

def login_session():
    """创建登录会话"""
    session = requests.Session()
    
    try:
        login_data = {
            'username': 'admin',
            'password': 'admin123'
        }
        
        response = session.post('http://127.0.0.1:5000/login', data=login_data)
        return session if response.status_code == 200 else None
            
    except Exception as e:
        return None

def check_database_updated_at():
    """检查数据库updated_at字段"""
    
    print("🔍 检查数据库updated_at字段")
    print("=" * 50)
    
    try:
        conn = sqlite3.connect('trading_system.db')
        cursor = conn.cursor()
        
        # 检查表结构
        cursor.execute("PRAGMA table_info(training_tasks)")
        columns = cursor.fetchall()
        
        column_names = [col[1] for col in columns]
        
        if 'updated_at' in column_names:
            print("✅ updated_at字段存在")
            
            # 检查现有记录
            cursor.execute("""
                SELECT id, status, progress, created_at, updated_at
                FROM training_tasks 
                ORDER BY created_at DESC 
                LIMIT 3
            """)
            
            tasks = cursor.fetchall()
            
            print(f"\n📊 最近的训练任务:")
            for task in tasks:
                task_id, status, progress, created_at, updated_at = task
                print(f"  任务: {task_id}")
                print(f"  状态: {status}")
                print(f"  进度: {progress}%")
                print(f"  创建: {created_at}")
                print(f"  更新: {updated_at}")
                print("  " + "-" * 30)
                
        else:
            print("❌ updated_at字段不存在")
            return False
        
        conn.close()
        return True
        
    except Exception as e:
        print(f"❌ 数据库检查失败: {e}")
        return False

def test_training_start():
    """测试训练启动"""
    
    print("\n🚀 测试训练启动")
    print("=" * 50)
    
    session = login_session()
    if not session:
        print("❌ 登录失败")
        return None
    
    try:
        # 获取可用模型
        models_response = session.get('http://127.0.0.1:5000/api/deep-learning/models')
        
        if models_response.status_code == 200:
            models_result = models_response.json()
            
            if models_result.get('success') and models_result.get('models'):
                model_id = models_result['models'][0]['id']
                print(f"✅ 找到模型: {model_id}")
                
                # 启动训练
                training_config = {
                    'model_name': 'test_training_progress',
                    'model_type': 'LSTM',
                    'symbol': 'XAUUSD',
                    'timeframe': 'H1',
                    'epochs': 3,  # 少量轮次用于测试
                    'batch_size': 32,
                    'learning_rate': 0.001,
                    'validation_split': 0.2
                }
                
                print(f"🔄 启动训练...")
                
                train_response = session.post(
                    'http://127.0.0.1:5000/api/deep-learning/start-training',
                    json=training_config,
                    headers={'Content-Type': 'application/json'}
                )
                
                if train_response.status_code == 200:
                    train_result = train_response.json()
                    
                    if train_result.get('success'):
                        task_id = train_result.get('task_id')
                        print(f"✅ 训练启动成功，任务ID: {task_id}")
                        return task_id
                    else:
                        print(f"❌ 训练启动失败: {train_result.get('error')}")
                else:
                    print(f"❌ 训练请求失败: {train_response.status_code}")
            else:
                print("❌ 没有找到可用模型")
        else:
            print(f"❌ 获取模型失败: {models_response.status_code}")
            
    except Exception as e:
        print(f"❌ 测试训练启动失败: {e}")
    
    return None

def monitor_training_progress(task_id, duration=60):
    """监控训练进度"""
    
    print(f"\n📊 监控训练进度 (任务: {task_id})")
    print("=" * 50)
    
    session = login_session()
    if not session:
        print("❌ 登录失败")
        return
    
    start_time = time.time()
    last_progress = -1
    progress_updates = 0
    
    while time.time() - start_time < duration:
        try:
            # 获取进度
            response = session.get(f'http://127.0.0.1:5000/api/deep-learning/training-progress/{task_id}')
            
            if response.status_code == 200:
                result = response.json()
                
                if result.get('success'):
                    progress_data = result['progress']
                    current_progress = progress_data.get('progress', 0)
                    status = progress_data.get('status', 'unknown')
                    epoch = progress_data.get('epoch', 0)
                    total_epochs = progress_data.get('total_epochs', 0)
                    train_loss = progress_data.get('train_loss')
                    val_loss = progress_data.get('val_loss')
                    
                    # 检查进度是否有变化
                    if current_progress != last_progress:
                        progress_updates += 1
                        print(f"🔄 进度更新 #{progress_updates}:")
                        print(f"   时间: {datetime.now().strftime('%H:%M:%S')}")
                        print(f"   状态: {status}")
                        print(f"   进度: {current_progress}%")
                        print(f"   轮次: {epoch}/{total_epochs}")
                        
                        if train_loss is not None:
                            print(f"   训练损失: {train_loss:.4f}")
                        if val_loss is not None:
                            print(f"   验证损失: {val_loss:.4f}")
                        
                        last_progress = current_progress
                        
                        # 检查数据库updated_at字段
                        check_database_update_time(task_id)
                        
                        print("   " + "-" * 40)
                    
                    # 如果训练完成或失败，退出监控
                    if status in ['completed', 'failed', 'stopped']:
                        print(f"🏁 训练结束，状态: {status}")
                        break
                        
                else:
                    print(f"❌ 获取进度失败: {result.get('error')}")
            else:
                print(f"❌ 进度API请求失败: {response.status_code}")
                
        except Exception as e:
            print(f"❌ 监控异常: {e}")
        
        time.sleep(3)  # 每3秒检查一次
    
    print(f"\n📋 监控总结:")
    print(f"   监控时长: {duration} 秒")
    print(f"   进度更新次数: {progress_updates}")
    
    if progress_updates > 0:
        print(f"   ✅ 进度更新正常")
    else:
        print(f"   ❌ 进度没有更新")
    
    return progress_updates > 0

def check_database_update_time(task_id):
    """检查数据库更新时间"""
    
    try:
        conn = sqlite3.connect('trading_system.db')
        cursor = conn.cursor()
        
        cursor.execute("""
            SELECT updated_at, progress, current_epoch
            FROM training_tasks 
            WHERE id = ?
        """, (task_id,))
        
        result = cursor.fetchone()
        
        if result:
            updated_at, progress, current_epoch = result
            print(f"   📅 数据库更新时间: {updated_at}")
        else:
            print(f"   ❌ 数据库中找不到任务")
        
        conn.close()
        
    except Exception as e:
        print(f"   ❌ 检查数据库更新时间失败: {e}")

def main():
    """主函数"""
    
    print("🧪 深度学习训练进度修复验证")
    print("=" * 60)
    
    # 检查数据库字段
    if not check_database_updated_at():
        print("\n❌ 数据库字段检查失败，请先运行迁移脚本")
        return
    
    # 测试训练启动
    task_id = test_training_start()
    
    if task_id:
        # 监控训练进度
        progress_working = monitor_training_progress(task_id, duration=90)
        
        print(f"\n🎯 修复验证结果")
        print("=" * 60)
        
        if progress_working:
            print(f"🎉 训练进度修复成功!")
            print(f"✅ updated_at字段正常工作")
            print(f"✅ 进度更新机制正常")
            print(f"✅ 前端可以正常获取进度")
            
            print(f"\n💡 建议:")
            print(f"• 训练进度现在应该能正常显示")
            print(f"• 可以在模型训练页面查看实时进度")
            print(f"• 如果仍有问题，检查前端轮询间隔")
            
        else:
            print(f"⚠️ 训练进度仍有问题")
            print(f"✅ 数据库字段已修复")
            print(f"❌ 进度更新可能仍有问题")
            
            print(f"\n🔧 进一步排查建议:")
            print(f"• 检查训练进程是否正常运行")
            print(f"• 查看应用程序日志中的错误信息")
            print(f"• 确认训练回调函数被正确调用")
            print(f"• 检查前端JavaScript控制台错误")
            
    else:
        print(f"\n❌ 无法启动训练进行测试")
        print(f"💡 可能的原因:")
        print(f"• 没有可用的模型")
        print(f"• 训练服务有问题")
        print(f"• 数据准备失败")
        
        print(f"\n🔧 建议:")
        print(f"• 先创建一个深度学习模型")
        print(f"• 检查应用程序日志")
        print(f"• 确认GPU/CPU环境正常")

if __name__ == '__main__':
    main()
