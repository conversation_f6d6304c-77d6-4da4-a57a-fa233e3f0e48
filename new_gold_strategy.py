#!/usr/bin/env python3
"""
新黄金交易策略模块
多指标黄金交易信号生成系统，结合RSI、MACD、布林带和市场环境识别
"""

import numpy as np
import pandas as pd
from datetime import datetime, timedelta
import logging

logger = logging.getLogger(__name__)

def calculate_rsi(prices, period=14):
    """计算RSI指标"""
    try:
        if len(prices) < period + 1:
            return None
        
        prices = np.array(prices)
        deltas = np.diff(prices)
        
        gains = np.where(deltas > 0, deltas, 0)
        losses = np.where(deltas < 0, -deltas, 0)
        
        avg_gain = np.mean(gains[:period])
        avg_loss = np.mean(losses[:period])
        
        if avg_loss == 0:
            return 100
        
        rs = avg_gain / avg_loss
        rsi = 100 - (100 / (1 + rs))
        
        return rsi
    except Exception as e:
        logger.warning(f"RSI计算失败: {e}")
        return 50  # 返回中性值

def calculate_macd(prices, fast=12, slow=26, signal=9):
    """计算MACD指标"""
    try:
        if len(prices) < slow + signal:
            return None, None, None
        
        prices = np.array(prices)
        
        # 计算EMA
        def ema(data, period):
            alpha = 2 / (period + 1)
            ema_values = [data[0]]
            for price in data[1:]:
                ema_values.append(alpha * price + (1 - alpha) * ema_values[-1])
            return np.array(ema_values)
        
        ema_fast = ema(prices, fast)
        ema_slow = ema(prices, slow)
        
        macd_line = ema_fast - ema_slow
        signal_line = ema(macd_line, signal)
        histogram = macd_line - signal_line
        
        return macd_line[-1], signal_line[-1], histogram[-1]
    except Exception as e:
        logger.warning(f"MACD计算失败: {e}")
        return 0, 0, 0

def calculate_bollinger_bands(prices, period=20, std_dev=2):
    """计算布林带指标"""
    try:
        if len(prices) < period:
            return None, None, None
        
        prices = np.array(prices)
        sma = np.mean(prices[-period:])
        std = np.std(prices[-period:])
        
        upper_band = sma + (std_dev * std)
        lower_band = sma - (std_dev * std)
        
        return upper_band, sma, lower_band
    except Exception as e:
        logger.warning(f"布林带计算失败: {e}")
        current_price = prices[-1] if len(prices) > 0 else 2650
        return current_price * 1.01, current_price, current_price * 0.99

def analyze_market_environment(market_data):
    """分析市场环境"""
    try:
        if not market_data or len(market_data) < 20:
            return {
                'trend': 'neutral',
                'volatility': 'medium',
                'strength': 0.5
            }
        
        # 提取价格数据
        prices = [float(d.get('close', d.get('price', 2650))) for d in market_data]
        
        # 趋势分析
        recent_prices = prices[-10:]
        trend_slope = 0  # 初始化变量
        if len(recent_prices) >= 2:
            trend_slope = (recent_prices[-1] - recent_prices[0]) / len(recent_prices)
            if trend_slope > 0.5:
                trend = 'bullish'
            elif trend_slope < -0.5:
                trend = 'bearish'
            else:
                trend = 'neutral'
        else:
            trend = 'neutral'
        
        # 波动性分析
        if len(prices) >= 20:
            volatility_std = np.std(prices[-20:])
            avg_price = np.mean(prices[-20:])
            volatility_ratio = volatility_std / avg_price if avg_price > 0 else 0
            
            if volatility_ratio > 0.02:
                volatility = 'high'
            elif volatility_ratio < 0.01:
                volatility = 'low'
            else:
                volatility = 'medium'
        else:
            volatility = 'medium'
        
        # 强度分析
        strength = min(1.0, abs(trend_slope) / 2.0)
        
        return {
            'trend': trend,
            'volatility': volatility,
            'strength': strength
        }
    except Exception as e:
        logger.warning(f"市场环境分析失败: {e}")
        return {
            'trend': 'neutral',
            'volatility': 'medium',
            'strength': 0.5
        }

def generate_new_trading_signal(market_data, current_time, config):
    """
    生成新的交易信号
    
    Args:
        market_data: 市场数据列表
        current_time: 当前时间
        config: 配置参数
    
    Returns:
        dict: 交易信号字典，包含action, confidence, reasoning等
    """
    try:
        logger.info(f"🔍 生成新黄金交易信号，数据点数: {len(market_data) if market_data else 0}")
        
        # 验证输入数据
        if not market_data or len(market_data) < 10:
            logger.warning("市场数据不足，返回中性信号")
            return {
                'action': 'hold',
                'type': 'hold',
                'confidence': 0.3,
                'strength': 0.3,
                'reasoning': '市场数据不足，建议观望',
                'reason': '市场数据不足，建议观望',
                'timestamp': current_time.isoformat() if current_time else datetime.now().isoformat(),
                'indicators': {}
            }
        
        # 提取价格数据
        prices = []
        for data_point in market_data:
            if isinstance(data_point, dict):
                price = data_point.get('close') or data_point.get('price') or data_point.get('bid', 2650)
            else:
                price = float(data_point) if data_point else 2650
            prices.append(float(price))
        
        current_price = prices[-1]
        logger.info(f"📊 当前价格: {current_price:.2f}")
        
        # 计算技术指标
        rsi = calculate_rsi(prices)
        macd, macd_signal, macd_histogram = calculate_macd(prices)
        bb_upper, bb_middle, bb_lower = calculate_bollinger_bands(prices)
        
        # 分析市场环境
        market_env = analyze_market_environment(market_data)
        
        # 信号评分系统
        buy_score = 0
        sell_score = 0
        reasoning_parts = []
        
        # RSI信号
        if rsi is not None:
            if rsi < 30:
                buy_score += 2
                reasoning_parts.append(f"RSI超卖({rsi:.1f})")
            elif rsi > 70:
                sell_score += 2
                reasoning_parts.append(f"RSI超买({rsi:.1f})")
            elif rsi < 45:
                buy_score += 1
                reasoning_parts.append(f"RSI偏低({rsi:.1f})")
            elif rsi > 55:
                sell_score += 1
                reasoning_parts.append(f"RSI偏高({rsi:.1f})")
        
        # MACD信号
        if macd is not None and macd_signal is not None:
            if macd > macd_signal and macd_histogram > 0:
                buy_score += 1
                reasoning_parts.append("MACD金叉")
            elif macd < macd_signal and macd_histogram < 0:
                sell_score += 1
                reasoning_parts.append("MACD死叉")
        
        # 布林带信号
        if bb_upper is not None and bb_lower is not None:
            if current_price <= bb_lower:
                buy_score += 2
                reasoning_parts.append("价格触及布林带下轨")
            elif current_price >= bb_upper:
                sell_score += 2
                reasoning_parts.append("价格触及布林带上轨")
            elif current_price < bb_middle:
                buy_score += 1
                reasoning_parts.append("价格低于布林带中轨")
            elif current_price > bb_middle:
                sell_score += 1
                reasoning_parts.append("价格高于布林带中轨")
        
        # 市场环境调整
        if market_env['trend'] == 'bullish':
            buy_score += 1
            reasoning_parts.append("市场趋势看涨")
        elif market_env['trend'] == 'bearish':
            sell_score += 1
            reasoning_parts.append("市场趋势看跌")
        
        # 决策逻辑
        total_score = buy_score + sell_score
        if total_score == 0:
            action = 'hold'
            confidence = 0.3
            reasoning = "技术指标中性，建议观望"
        elif buy_score > sell_score:
            action = 'buy'
            confidence = min(0.9, 0.5 + (buy_score - sell_score) * 0.1)
            reasoning = f"看涨信号 ({', '.join(reasoning_parts)})"
        else:
            action = 'sell'
            confidence = min(0.9, 0.5 + (sell_score - buy_score) * 0.1)
            reasoning = f"看跌信号 ({', '.join(reasoning_parts)})"
        
        # 构建信号结果
        signal = {
            'action': action,
            'type': action,  # 兼容性字段
            'confidence': confidence,
            'strength': confidence,  # 添加strength字段，与confidence相同
            'reasoning': reasoning,
            'reason': reasoning,  # 兼容性字段
            'timestamp': current_time.isoformat() if current_time else datetime.now().isoformat(),
            'current_price': current_price,
            'indicators': {
                'rsi': rsi,
                'macd': macd,
                'macd_signal': macd_signal,
                'macd_histogram': macd_histogram,
                'bb_upper': bb_upper,
                'bb_middle': bb_middle,
                'bb_lower': bb_lower,
                'market_trend': market_env['trend'],
                'volatility': market_env['volatility']
            },
            'scores': {
                'buy_score': buy_score,
                'sell_score': sell_score
            }
        }
        
        logger.info(f"✅ 生成交易信号: {action} (置信度: {confidence:.2f}) - {reasoning}")
        return signal
        
    except Exception as e:
        logger.error(f"❌ 生成交易信号失败: {e}")
        return {
            'action': 'hold',
            'type': 'hold',
            'confidence': 0.2,
            'strength': 0.2,
            'reasoning': f'信号生成异常: {str(e)}',
            'reason': f'信号生成异常: {str(e)}',
            'timestamp': current_time.isoformat() if current_time else datetime.now().isoformat(),
            'indicators': {},
            'error': str(e)
        }

# 兼容性函数
def get_trading_signal(market_data, config=None):
    """兼容性函数，供其他模块调用"""
    return generate_new_trading_signal(market_data, datetime.now(), config or {})

if __name__ == '__main__':
    # 测试代码
    print("🧪 测试新黄金交易策略模块")
    
    # 模拟市场数据
    test_data = []
    base_price = 2650
    for i in range(50):
        price = base_price + np.random.normal(0, 10)  # 添加随机波动
        test_data.append({
            'close': price,
            'timestamp': datetime.now() - timedelta(minutes=i)
        })
    
    # 生成测试信号
    signal = generate_new_trading_signal(test_data, datetime.now(), {})
    
    print(f"测试信号: {signal['action']} (置信度: {signal['confidence']:.2f})")
    print(f"推理: {signal['reasoning']}")
    print(f"指标: {signal['indicators']}")
